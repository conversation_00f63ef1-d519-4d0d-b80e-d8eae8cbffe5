name: salespro_saas_admin
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 0.0.4+4

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_feather_icons: ^2.0.0+1
  material_design_icons_flutter: ^7.0.7296
  font_awesome_flutter: ^10.7.0
  syncfusion_flutter_charts: ^29.2.11
  firebase_core: ^3.4.0
  firebase_database: ^11.1.1
  firebase_auth: ^5.2.0
  firebase_storage: ^12.2.0
  flutter_riverpod: ^2.5.1
  flutter_easyloading: ^3.0.5
  nb_utils: ^7.0.4
  http: ^1.2.2

  youtube_player_flutter: ^9.0.1
  image_picker: ^1.1.0
  file_picker: ^10.2.0
  image_picker_web: ^4.0.0
  intl: ^0.20.2
  hugeicons: ^0.0.7
  fl_chart: ^1.0.0
  flutter_svg: ^2.0.10+1
  restart_app: ^1.2.1
  responsive_framework: ^1.4.0
  flutter_spinkit: ^5.2.1
  go_router: ^15.2.4
  expansion_widget: ^0.1.0
  collection: ^1.18.0
  html: ^0.15.4

  # Remote Packages
  responsive_grid:
    git:
      url: https://github.com/iamtoricool/ResponsiveGrid_Flutter.git
      ref: master

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - images/