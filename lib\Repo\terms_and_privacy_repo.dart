import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:salespro_saas_admin/model/terms_and_condition_model.dart';

class TermsAndPrivacyRepo {
  Future<TermsAndPrivacyModel> getTerms() async {
    DatabaseReference bankRef = FirebaseDatabase.instance.ref('Admin Panel/Terms and Conditions');
    final bankData = await bankRef.get();
    TermsAndPrivacyModel bankInfoModel = TermsAndPrivacyModel.fromJson(jsonDecode(jsonEncode(bankData.value)));

    return bankInfoModel;
  }

  Future<TermsAndPrivacyModel> getPrivacy() async {
    DatabaseReference bankRef = FirebaseDatabase.instance.ref('Admin Panel/Privacy Policy');
    final bankData = await bankRef.get();
    TermsAndPrivacyModel bankInfoModel = TermsAndPrivacyModel.fromJson(jsonDecode(jsonEncode(bankData.value)));

    return bankInfoModel;
  }
}
