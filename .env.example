# Firebase Configuration Template
# نسخ هذا الملف إلى .env وتعديل البيانات حسب العميل

# مثال على بيانات Firebase الصحيحة:
# من Firebase Console > Project Settings > General > Your apps

# Android Configuration
FIREBASE_API_KEY_ANDROID=AIzaSyDNJ2l5ssvs4NHl83g5r6WxkburMkBEm_w
FIREBASE_AUTH_DOMAIN=your-project-name.firebaseapp.com
FIREBASE_DATABASE_URL=https://your-project-name-default-rtdb.firebaseio.com
FIREBASE_PROJECT_ID=your-project-name
FIREBASE_STORAGE_BUCKET=your-project-name.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=138230456121
FIREBASE_APP_ID_ANDROID=1:138230456121:web:58a5fd25ee821f880692e8
FIREBASE_MEASUREMENT_ID=G-LHDYZGB9R7

# Windows Configuration (عادة نفس البيانات)
FIREBASE_API_KEY_WINDOWS=AIzaSyDNJ2l5ssvs4NHl83g5r6WxkburMkBEm_w
FIREBASE_APP_ID_WINDOWS=1:138230456121:web:58a5fd25ee821f880692e8

# Web Configuration (للمشاريع الأخرى)
FIREBASE_API_KEY_WEB=AIzaSyDNJ2l5ssvs4NHl83g5r6WxkburMkBEm_w
FIREBASE_APP_ID_WEB=1:138230456121:web:58a5fd25ee821f880692e8

# معلومات العميل
CLIENT_NAME=اسم العميل
CLIENT_VERSION=1.0.0

# تعليمات:
# 1. انسخ هذا الملف إلى .env
# 2. غير "your-project-name" باسم مشروع Firebase الخاص بك
# 3. استبدل جميع القيم بالبيانات الفعلية من Firebase Console
# 4. احفظ الملف
# 5. شغل التطبيق
