// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
  apiKey: "AIzaSyBp6BbdNDixLwhbOGZw2Gy4OzMtSIJjoDw",
  authDomain: "amrdev-pos-access.firebaseapp.com",
  databaseURL: "https://amrdev-pos-access-default-rtdb.firebaseio.com",
  projectId: "amrdev-pos-access",
  storageBucket: "amrdev-pos-access.firebasestorage.app",
  messagingSenderId: "852160502272",
  appId: "1:852160502272:web:97e7afde6a3454a165d6e5",
  measurementId: "G-34NGMG5Y1X");

  // static const FirebaseOptions web = FirebaseOptions(
  //   apiKey: 'AIzaSyBJN0qZG6o9RTBDxf0z9c146lhXhGT9cIE',
  //   appId: '1:77048286492:web:dfb912c481eb96c4847ed9',
  //   messagingSenderId: '77048286492',
  //   projectId: 'salespro-saas-4a6d5',
  //   authDomain: 'salespro-saas-4a6d5.firebaseapp.com',
  //   storageBucket: 'salespro-saas-4a6d5.appspot.com',
  //   databaseURL: "https://salespro-saas-4a6d5-default-rtdb.firebaseio.com",
  //   measurementId: "G-5KWP9HP8EG",
  // );
}
