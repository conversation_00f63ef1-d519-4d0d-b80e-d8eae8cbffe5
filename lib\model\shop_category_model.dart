class ShopCategoryModel {
  ShopCategoryModel({
    this.categoryName,
    this.description,
    this.imageUrl,
  });

  ShopCategoryModel.fromJson(dynamic json) {
    categoryName = json['categoryName'];
    description = json['description'];
    imageUrl = json['imageUrl'];
  }

  String? categoryName;
  String? description;
  String? imageUrl;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['categoryName'] = categoryName;
    map['description'] = description;
    if (imageUrl != null) map['imageUrl'] = imageUrl;
    return map;
  }
}
