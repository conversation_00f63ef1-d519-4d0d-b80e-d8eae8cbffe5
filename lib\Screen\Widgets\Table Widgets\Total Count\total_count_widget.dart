import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:salespro_saas_admin/Screen/Widgets/Constant%20Data/constant.dart';
import 'package:responsive_framework/responsive_framework.dart' as rf;

import '../../static_string/static_string.dart';

class TotalCount extends StatelessWidget {
  const TotalCount({
    super.key,
    required this.title,
    required this.count,
    required this.icon,
    required this.backgroundColor,
    required this.iconBgColor,
  });

  final String title;
  final String count;
  final Color iconBgColor;
  final String icon;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      // height: 100,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: backgroundColor,
      ),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        leading: Container(
          padding: const EdgeInsets.all(10.0),
          height: 50,
          width: 50,
          decoration: BoxDecoration(shape: BoxShape.circle, color: iconBgColor),
          child: SvgPicture.asset(
            icon,
            height: 28.0,
            width: 28.0,
            allowDrawingOutsideViewBox: false,
            fit: BoxFit.cover,
          ),
        ),
        subtitle: Text(
          title,
          style: kTextStyle.copyWith(color: kGreyTextColor),
          maxLines: 1,
        ),
        title: Text(
          count,
          style: kTextStyle.copyWith(
            color: kTitleColor,
            fontSize: 21.0,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

class InfoWidget extends StatelessWidget {
  const InfoWidget({
    super.key,
    required this.title,
    required this.count,
    required this.icon,
    required this.percentageColor,
    required this.iconColor,
    required this.countPercentage,
    required this.lastMonth,
    required this.currentYear,
    this.arrowIcon,
    this.arrowIconColor,
    required this.bgColor,
    this.height,
  });

  final String title;
  final double? height;
  final String count;
  final String lastMonth;
  final String currentYear;
  final String countPercentage;
  final Color iconColor;
  final IconData icon;
  final IconData? arrowIcon;
  final Color? arrowIconColor;
  final Color percentageColor;
  final Color bgColor;

  @override
  Widget build(BuildContext context) {
    final tabAndPhoneSize = MediaQuery.of(context).size.width < 1200;
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
      elevation: 0.0,
      shadowColor: kGreyTextColor,
      child: Container(
        height: tabAndPhoneSize ? 142 : 170,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: bgColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: RichText(
                text: TextSpan(
                  text: title,
                  style: kTextStyle.copyWith(
                    color: kTitleColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.start,
                  alignment: WrapAlignment.start,
                  children: [
                    Text(
                      count,
                      style: kTextStyle.copyWith(
                        color: kTitleColor,
                        fontSize: 21.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 5.0),
                    Icon(arrowIcon, color: arrowIconColor),
                    RichText(
                      text: TextSpan(
                        text: countPercentage,
                        style: kTextStyle.copyWith(
                          color: percentageColor,
                          fontWeight: FontWeight.bold,
                        ),
                        children: [
                          TextSpan(
                            text: '  شهري',
                            style: kTextStyle.copyWith(
                              color: kGreyTextColor,
                              fontWeight: FontWeight.bold,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // const SizedBox(width: 4.0),
                    // Text('Monthly',style: kTextStyle.copyWith(color: kGreyTextColor),overflow: TextOverflow.ellipsis),
                  ],
                ),
              ),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'الشهر الماضي:',
                  style: kTextStyle.copyWith(color: kGreyTextColor),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(width: 4.0),
                Text(
                  lastMonth,
                  style: kTextStyle.copyWith(
                    color: kTitleColor,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              children: [
                Text(
                  'الإجمالي (السنة الحالية)',
                  style: kTextStyle.copyWith(color: kGreyTextColor),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(width: 4.0),
                Flexible(
                  child: Text(
                    currentYear,
                    style: kTextStyle.copyWith(
                      color: kTitleColor,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class YearlyInfoWidget extends StatelessWidget {
  const YearlyInfoWidget({
    super.key,
    required this.title,
    required this.count,
    required this.icon,
    required this.percentageColor,
    required this.iconColor,
    required this.countPercentage,
    required this.lastYear,
    required this.currentYear,
    this.arrowIcon,
    this.arrowIconColor,
    required this.bgColor,
  });

  final String title;
  final String count;
  final String lastYear;
  final String currentYear;
  final String countPercentage;
  final Color iconColor;
  final IconData icon;
  final IconData? arrowIcon;
  final Color? arrowIconColor;
  final Color percentageColor;
  final Color bgColor;

  @override
  Widget build(BuildContext context) {
    final tabAndPhoneSize = rf.ResponsiveValue<bool>(
      context,
      defaultValue: false,
      conditionalValues: [
        rf.Condition.smallerThan(name: BreakpointName.LG.name, value: true),
      ],
    ).value;
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
      elevation: 0.0,
      shadowColor: kGreyTextColor,
      child: Container(
        height: tabAndPhoneSize ? 142 : 161,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: bgColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: RichText(
                text: TextSpan(
                  text: title,
                  style: kTextStyle.copyWith(
                    color: kTitleColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.start,
                  alignment: WrapAlignment.start,
                  children: [
                    Text(
                      count,
                      style: kTextStyle.copyWith(
                        color: kTitleColor,
                        fontSize: 21.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 5.0),
                    Icon(arrowIcon, color: arrowIconColor),
                    RichText(
                      text: TextSpan(
                        text: countPercentage,
                        style: kTextStyle.copyWith(
                          color: percentageColor,
                          fontWeight: FontWeight.bold,
                        ),
                        children: [
                          TextSpan(
                            text: '  هذه السنة',
                            style: kTextStyle.copyWith(
                              color: kGreyTextColor,
                              fontWeight: FontWeight.bold,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // const SizedBox(width: 4.0),
                    // Text('Monthly',style: kTextStyle.copyWith(color: kGreyTextColor),overflow: TextOverflow.ellipsis),
                  ],
                ),
              ),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'السنة الماضية:',
                  style: kTextStyle.copyWith(color: kGreyTextColor),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(width: 4.0),
                Text(
                  lastYear,
                  style: kTextStyle.copyWith(
                    color: kTitleColor,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              children: [
                Text(
                  'الإجمالي (السنة الحالية)',
                  style: kTextStyle.copyWith(color: kGreyTextColor),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(width: 4.0),
                Flexible(
                  child: Text(
                    currentYear,
                    style: kTextStyle.copyWith(
                      color: kTitleColor,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
