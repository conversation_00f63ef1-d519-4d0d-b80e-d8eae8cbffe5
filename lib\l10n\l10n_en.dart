// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get mrp => 'MRP';

  @override
  String get userTitle => 'User Title';

  @override
  String get yourPackageWillExpireTodayPleasePurchaseagain =>
      'Your Package Will Expire Today\n\nPlease Purchase again';

  @override
  String get theSystemIsProvided =>
      '(a) The System is provided solely for the purpose of facilitating point of sale transactions and related activities in your business.';

  @override
  String get toUseTheSystem =>
      '(a) To use the System, you may be required to create an account. You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate and complete.';

  @override
  String get aboutApp => 'About App';

  @override
  String get accept => 'Accept';

  @override
  String get acceptanceOfTerms => 'Acceptance of Terms';

  @override
  String get accepted => 'Accepted';

  @override
  String get account => 'Account';

  @override
  String get accountName => 'Account Name';

  @override
  String get accountNumber => 'Account Number';

  @override
  String get accountType => 'Account Type';

  @override
  String get add => 'Add';

  @override
  String get addBrand => 'Add Brand';

  @override
  String get addCategory => 'Add Category';

  @override
  String get addCustomer => 'Add Customer';

  @override
  String get addDelivery => 'Add Delivery';

  @override
  String get addDocumentId => 'Add Document ID';

  @override
  String get addDucument => 'Add Document';

  @override
  String get addExpense => 'Add Expense';

  @override
  String get addExpenseCategory => 'Add Expense Category';

  @override
  String get addItems => 'Add Items';

  @override
  String get addNewAddress => 'Add New Address';

  @override
  String get addNewProduct => 'Add New Product';

  @override
  String get addNote => 'Add Note';

  @override
  String get addPurchase => 'Add Purchase';

  @override
  String get addSales => 'Add Sales';

  @override
  String get addSuccessful => 'Added Successfully';

  @override
  String get addUnit => 'Add Unit';

  @override
  String get addUserRole => 'Add User Role';

  @override
  String get address => 'Address';

  @override
  String get all => 'All';

  @override
  String get allBusinessSolution => 'All Business Solutions';

  @override
  String get alreadyHaveAnAccounts => 'Already have an account?';

  @override
  String get amount => 'Amount';

  @override
  String get androidIOSAppSupport => 'Android & iOS App Support';

  @override
  String get apply => 'Apply';

  @override
  String get areYourSureDeleteThisUser =>
      'Are you sure you want to delete this user?';

  @override
  String get backToHome => 'Back To Home';

  @override
  String get balance => 'Balance';

  @override
  String get bankAccountNumber => 'Bank Account Number';

  @override
  String get bankAccounts => 'Bank Accounts';

  @override
  String get bankDetails => 'Bank Details';

  @override
  String get bankName => 'Bank Name';

  @override
  String get barcodeORqrCodeScanner => 'Barcode/QR Code Scanner';

  @override
  String get basicInformation => 'Basic Information';

  @override
  String get youMustBeAtLeastYearsOld =>
      '(b) You must be at least 18 years old or the legal age of majority in your jurisdiction to use the System.';

  @override
  String get brand => 'Brand';

  @override
  String get brandName => 'Brand Name';

  @override
  String get businessCategory => 'Business Category';

  @override
  String get businessCategoryPlaceHolder => 'Enter your business category';

  @override
  String get businessIdentificationNumber => 'Business Identification Number';

  @override
  String get businessName => 'Business Name';

  @override
  String get businessNamePlaceHolder => 'Enter your business name';

  @override
  String get businessType => 'Business Type';

  @override
  String get buyPremiumPlan => 'Buy Premium Plan';

  @override
  String get buySms => 'Buy SMS';

  @override
  String get buysmsPlan => 'Buy SMS Plan';

  @override
  String get theUserCanCreateSalesInvoice =>
      '(c) The user can create sales invoices, purchase invoices, add stock, add expense, and view sales/purchase/expense/loss profit reports.';

  @override
  String get call => 'Call';

  @override
  String get camera => 'Camera';

  @override
  String get cancel => 'Cancel';

  @override
  String get capacity => 'Capacity';

  @override
  String get cash => 'Cash';

  @override
  String get cashAndBank => 'Cash & Bank';

  @override
  String get category => 'Category';

  @override
  String get categoryName => 'Category Name';

  @override
  String get changePassword => 'Change Password';

  @override
  String get checkEmail => 'Check Email';

  @override
  String get choseYourFeature => 'Choose Your Features';

  @override
  String get city => 'City';

  @override
  String get cityPinCode => 'City Pin Code';

  @override
  String get collectdue => 'Collect Due';

  @override
  String get color => 'Color';

  @override
  String get companyNameAlt => 'Company Name';

  @override
  String get companyName => 'Company Name';

  @override
  String get companyNamePlaceHolder => 'Enter your company name';

  @override
  String get completeTransaction => 'Complete Transaction';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get continueButton => 'Continue';

  @override
  String get country => 'Country';

  @override
  String get countryPinCode => 'Country Pin Code';

  @override
  String get create => 'Create';

  @override
  String get createAFreeAccounts => 'Create a free account';

  @override
  String get cteateAnAccount => 'Create an Account';

  @override
  String get currentStock => 'Current Stock';

  @override
  String get customer => 'Customer';

  @override
  String get customerDue => 'Customer Due';

  @override
  String get customerList => 'Customer List';

  @override
  String get customerName => 'Customer Name';

  @override
  String get customerOfTheMonth => 'Customer of the Month';

  @override
  String get customerType => 'Customer Type';

  @override
  String get dailyTransaction => 'Daily Transaction';

  @override
  String get dashBoardOverView => 'Dashboard Overview';

  @override
  String get date => 'Date';

  @override
  String get dealer => 'Dealer';

  @override
  String get dealerPrice => 'Dealer Price';

  @override
  String get delete => 'Delete';

  @override
  String get deleteSuccessful => 'Delete Successful';

  @override
  String get description => 'Description';

  @override
  String get details => 'Details';

  @override
  String get discount => 'Discount';

  @override
  String get doNotDistrub => 'Do not disturb';

  @override
  String get dueAmount => 'Due Amount';

  @override
  String get dueCollection => 'Due Collection';

  @override
  String get dueCollectionReports => 'Due Collection Reports';

  @override
  String get dueDate => 'Due Date';

  @override
  String get dueList => 'Due List';

  @override
  String get dueTransaction => 'Due Transaction';

  @override
  String get easyToUseMobilePos => 'Easy to use mobile POS';

  @override
  String get edit => 'Edit';

  @override
  String get editPurchaseInvoice => 'Edit Purchase Invoice';

  @override
  String get editSalesInvoice => 'Edit Sales Invoice';

  @override
  String get editSocailMedia => 'Edit Social Media';

  @override
  String get email => 'Email';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get endDate => 'End Date';

  @override
  String get enterAmount => 'Enter Amount';

  @override
  String get enterCategoryName => 'Enter Category Name';

  @override
  String get enterCustomerName => 'Enter Customer Name';

  @override
  String get enterDealerPrice => 'Enter Dealer Price';

  @override
  String get enterDiscountAmount => 'Enter Discount Amount';

  @override
  String get enterEmailAddresss => 'Enter Email Address';

  @override
  String get enterExpenseCategory => 'Enter Expense Category';

  @override
  String get enterExpenseDate => 'Enter Expense Date';

  @override
  String get enterFullAddress => 'Enter Full Address';

  @override
  String get enterIncomeCategory => 'Enter Income Category';

  @override
  String get enterIncomeDate => 'Enter Income Date';

  @override
  String get enterLandMark => 'Enter Landmark';

  @override
  String get enterMessageContent => 'Enter Message Content';

  @override
  String get enterMrpOrRetailerPirce => 'Enter MRP/Retailer Price';

  @override
  String get enterNote => 'Enter Note';

  @override
  String get enterOpeningBalance => 'Enter Opening Balance';

  @override
  String get enterPaidAmonts => 'Enter Paid Amount';

  @override
  String get enterPassword => 'Enter Password';

  @override
  String get enterPhoneNumber => 'Enter Phone Number';

  @override
  String get enterPinCode => 'Enter Pin Code';

  @override
  String get enterPurchasePrice => 'Enter Purchase Price';

  @override
  String get enterQuantity => 'Enter Quantity';

  @override
  String get enterReferenceNumber => 'Enter Reference Number';

  @override
  String get enterSalePrice => 'Enter Sale Price';

  @override
  String get enterShopName => 'Enter Shop Name';

  @override
  String get enterStockAmount => 'Enter Stock Amount';

  @override
  String get enterUnitName => 'Enter Unit Name';

  @override
  String get enterUserTitle => 'Enter User Title';

  @override
  String get enterWholeSalePrice => 'Enter Wholesale Price';

  @override
  String get enterYourBankAccountNumber => 'Enter Your Bank Account Number';

  @override
  String get enterYourBankName => 'Enter Your Bank Name';

  @override
  String get enterYourBanktc => 'Enter Your Bank Terms & Conditions';

  @override
  String get enterYourCityPinCode => 'Enter Your City Pin Code';

  @override
  String get enterYourCompanyName => 'Enter Your Company Name';

  @override
  String get enterYourConfirmPassord => 'Enter Your Confirm Password';

  @override
  String get enterYourDealerPrice => 'Enter Your Dealer Price';

  @override
  String get enterYourDetails => 'Enter Your Details';

  @override
  String get enterYourEmailAddress => 'Enter Your Email Address';

  @override
  String get enterYourFullName => 'Enter Your Full Name';

  @override
  String get enterYourGstNumber => 'Enter Your GST Number';

  @override
  String get enterYourLandmark => 'Enter Your Landmark';

  @override
  String get enterYourMobileNumber => 'Enter Your Mobile Number';

  @override
  String get enterYourName => 'Enter Your Name';

  @override
  String get enterYourOpeningBalance => 'Enter Your Opening Balance';

  @override
  String get enterYourPassword => 'Enter Your Password';

  @override
  String get enterYourPhoneNumber => 'Enter Your Phone Number';

  @override
  String get enterYourPincode => 'Enter Your Pincode';

  @override
  String get enterYourPurchasePrice => 'Enter Your Purchase Price';

  @override
  String get enterYourSalePrice => 'Enter Your Sale Price';

  @override
  String get enterYourShopName => 'Enter Your Shop Name';

  @override
  String get enterYourState => 'Enter Your State';

  @override
  String get enterYourTotalSalePrice => 'Enter Your Total Sale Price';

  @override
  String get enterYourUserRoleName => 'Enter Your User Role Name';

  @override
  String get enterYourWholeSalePrice => 'Enter Your Wholesale Price';

  @override
  String get enterYourZip => 'Enter Your ZIP';

  @override
  String get expense => 'Expense';

  @override
  String get expenseCategory => 'Expense Category';

  @override
  String get expenseDate => 'Expense Date';

  @override
  String get expenseDetails => 'Expense Details';

  @override
  String get expenseFor => 'Expense For';

  @override
  String get expenseList => 'Expense List';

  @override
  String get expenseReport => 'Expense Report';

  @override
  String get facebook => 'Facebook';

  @override
  String get failedToGetPlatformVersion => 'Failed to get platform version.';

  @override
  String get featureAreTheImportant =>
      'Features are the important part which makes AmrDev POS different from traditional solutions.';

  @override
  String get firstName => 'First Name';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get freeDataBackup => 'Free Data Backup';

  @override
  String get freeLifeTimeUpdate => 'Free Lifetime Update';

  @override
  String get freePacakge => 'Free Package';

  @override
  String get freePlan => 'Free Plan';

  @override
  String get gallary => 'Gallery';

  @override
  String get gstNumber => 'GST Number';

  @override
  String get history => 'History';

  @override
  String get home => 'Home';

  @override
  String get howWeProtectYourInformation => 'How we protect your information';

  @override
  String get identityVerify => 'Identity Verify';

  @override
  String get image => 'Image';

  @override
  String get income => 'Income';

  @override
  String get incomeCategory => 'Income Category';

  @override
  String get incomeDate => 'Income Date';

  @override
  String get incomeDetails => 'Income Details';

  @override
  String get incomeList => 'Income List';

  @override
  String get incomeReport => 'Income Report';

  @override
  String get instagram => 'Instagram';

  @override
  String get inv => 'Inv';

  @override
  String get invoice => 'Invoice';

  @override
  String get invoiceNumber => 'Invoice Number';

  @override
  String get invoiceSettings => 'Invoice Settings';

  @override
  String get itemAdded => 'Item Added';

  @override
  String get kycVerification => 'KYC Verification';

  @override
  String get landmark => 'Landmark';

  @override
  String get language => 'Language';

  @override
  String get lastName => 'Last Name';

  @override
  String get lifeTimePurchase => 'Lifetime\nPurchase';

  @override
  String get limitedUsage => 'Limited Usage';

  @override
  String get link => 'Link';

  @override
  String get linkedIN => 'LinkedIn';

  @override
  String get loading => 'Loading...';

  @override
  String get logIn => 'Log In';

  @override
  String get logOut => 'Log Out';

  @override
  String get login => 'Login';

  @override
  String get maan => 'Maan';

  @override
  String get makeALastingImpression =>
      'Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty.';

  @override
  String get manageYourBussinessWith => 'Manage your business with';

  @override
  String get masterCard => 'Master Card';

  @override
  String get manufacturer => 'Manufacturer';

  @override
  String get message => 'Message';

  @override
  String get messageHistory => 'Message History';

  @override
  String get mobiPosAppIsFree =>
      'AmrDev POS app is free, easy to use. In fact, it\'s one of the best POS systems around the world.';

  @override
  String get mobiPosIsaCompleteBusinesSolution =>
      'AmrDev POS is a complete business solution with stock, account, sales, expense & loss/profit.';

  @override
  String get monthly => 'Monthly';

  @override
  String get moreInfo => 'More Info';

  @override
  String get name => 'Enter your name';

  @override
  String get next => 'Next';

  @override
  String get noConnection => 'No Connection';

  @override
  String get noData => 'No Data';

  @override
  String get noDataAvailable => 'No Data Available';

  @override
  String get noHistoryFound => 'No History Found!';

  @override
  String get noTransactionFound => 'No Transaction Found!';

  @override
  String get noUserFoundForThatEmail => 'No user found for that email.';

  @override
  String get noUserRoleFound => 'No User Role Found';

  @override
  String get note => 'Note';

  @override
  String get notification => 'Notification';

  @override
  String get ok => 'OK';

  @override
  String get onboardOne =>
      'POS system includes a lot of features, including sales tracking, inventory management.';

  @override
  String get onboardThree =>
      'This system helps you improve your operations for your customers.';

  @override
  String get onboardTwo =>
      'Our POS system should make daily operations easy and faster.';

  @override
  String get openingBalance => 'Opening Balance';

  @override
  String get order => 'Orders';

  @override
  String get otpClose => 'Close';

  @override
  String get paid => 'Paid';

  @override
  String get paidAmount => 'Paid Amount';

  @override
  String get parties => 'Parties';

  @override
  String get partyList => 'Party List';

  @override
  String get partyName => 'Party Name';

  @override
  String get partyType => 'Party Type';

  @override
  String get password => 'Password';

  @override
  String get payeeName => 'Payee Name';

  @override
  String get payeeNumber => 'Payee Number';

  @override
  String get paymentInstructions => 'Payment Instructions:';

  @override
  String get paymentType => 'Payment Type';

  @override
  String get paymentTypes => 'Payment Types';

  @override
  String get paypalClientId => 'Paypal Client ID';

  @override
  String get paypalClientSecret => 'Paypal Client Secret';

  @override
  String get phone => 'Phone';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get pinCode => 'Pin Code';

  @override
  String get pleaseAddSomeProductFirst => 'Please add some product first';

  @override
  String get pleaseConnectYourBlutohPrinter =>
      'Please connect your bluetooth printer';

  @override
  String get pleaseConnectYourPrinter => 'Please connect your printer';

  @override
  String get pleaseEnterAConfirmPassword => 'Please enter a confirm password';

  @override
  String get pleaseEnterAPassword => 'Please enter a password';

  @override
  String get pleaseEnterAValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterAvalidPhoneNumber =>
      'Please enter a valid phone number';

  @override
  String get pleaseEnterAnEmail => 'Please enter an email';

  @override
  String get pleaseEnterCustomerName => 'Please enter customer name';

  @override
  String get pleaseEnterValidData => 'Please enter valid data';

  @override
  String get pleaseEnterYourDetails => 'Please enter your details';

  @override
  String get pleaseEnterYourEmailAddress => 'Please enter your email address';

  @override
  String get pleaseEnterYourOpeningBalance =>
      'Please enter your opening balance';

  @override
  String get pleaseEnterYourPassword => 'Please enter your password';

  @override
  String get pleaseEnterYourPhoneNumber => 'Please enter your phone number';

  @override
  String get pleaseEnterYourPincode => 'Please enter your pincode';

  @override
  String get pleaseEnterYourUserRoleName => 'Please enter your user role name';

  @override
  String get pleaseMakeASaleFirst => 'Please make a sale first';

  @override
  String get pleaseSelectACustomer => 'Please select a customer';

  @override
  String get pleaseSelectAExpenseCategory =>
      'Please select an expense category';

  @override
  String get pleaseSelectAIncomeCategory => 'Please select an income category';

  @override
  String get pleaseSelectAInvoicePrintSize =>
      'Please select an invoice print size';

  @override
  String get pleaseSelectAPaymentType => 'Please select a payment type';

  @override
  String get pleaseSelectAProduct => 'Please select a product';

  @override
  String get pleaseSelectAProductCategory => 'Please select a product category';

  @override
  String get pleaseSelectAProductFirst => 'Please select a product first';

  @override
  String get pleaseSelectAValidDate => 'Please select a valid date';

  @override
  String get pleaseSelectAnInvoicePrintSize =>
      'Please select an invoice print size';

  @override
  String get pleaseSelectCustomer => 'Please select customer';

  @override
  String get pleaseSelectProductCategory => 'Please select product category';

  @override
  String get pleaseSelectProductFirst => 'Please select product first';

  @override
  String get pleaseSelectValidDate => 'Please select valid date';

  @override
  String get pleaseSelectYourLanguage => 'Please select your language';

  @override
  String get pleaseSignInWithYourEmailAndPassword =>
      'Please sign in with your email and password';

  @override
  String get pleaseUpdateYourPlanToAccessThisFeature =>
      'Please update your plan to access this feature';

  @override
  String get powerdedByMobiPos => 'Powered By AmrDev POS';

  @override
  String get premiumCustomerSupport => 'Premium Customer Support';

  @override
  String get premiumPlan => 'Premium Plan';

  @override
  String get previousDue => 'Previous Due';

  @override
  String get print => 'Print';

  @override
  String get printingOption => 'Printing Option';

  @override
  String get product => 'Product';

  @override
  String get productBrand => 'Product Brand';

  @override
  String get productCategory => 'Product Category';

  @override
  String get productCode => 'Product Code';

  @override
  String get productCodeIsRequired => 'Product code is required';

  @override
  String get productDetails => 'Product Details';

  @override
  String get productList => 'Product List';

  @override
  String get productName => 'Product Name';

  @override
  String get productNameIsRequired => 'Product name is required';

  @override
  String get productNature => 'Product Nature';

  @override
  String get productSize => 'Product Size';

  @override
  String get productType => 'Product Type';

  @override
  String get productUnit => 'Product Unit';

  @override
  String get productWarranty => 'Product Warranty';

  @override
  String get productWeight => 'Product Weight';

  @override
  String get profile => 'Profile';

  @override
  String get profileEdit => 'Profile Edit';

  @override
  String get profit => 'Profit';

  @override
  String get profitplus => 'Profit+';

  @override
  String get purchase => 'Purchase';

  @override
  String get purchaseAlarm => 'Purchase Alarm';

  @override
  String get purchaseConfirmed => 'Purchase Confirmed';

  @override
  String get purchaseDetails => 'Purchase Details';

  @override
  String get purchaseList => 'Purchase List';

  @override
  String get purchaseNow => 'Purchase Now';

  @override
  String get purchasePrice => 'Purchase Price';

  @override
  String get purchasePremiumPlan => 'Purchase Premium Plan';

  @override
  String get purchaseReportss => 'Purchase Reports';

  @override
  String get qty => 'Qty';

  @override
  String get quantity => 'Quantity';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get referenceNumber => 'Reference Number';

  @override
  String get register => 'Register';

  @override
  String get remainingDue => 'Remaining Due';

  @override
  String get reports => 'Reports';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get retailer => 'Retailer';

  @override
  String get returnItem => 'Return';

  @override
  String get returnAmount => 'Return Amount';

  @override
  String get revenue => 'Revenue';

  @override
  String get sale => 'Sale';

  @override
  String get saleConfirmed => 'Sale Confirmed';

  @override
  String get saleDetails => 'Sale Details';

  @override
  String get saleList => 'Sale List';

  @override
  String get salePrice => 'Sale Price';

  @override
  String get saleReportss => 'Sale Reports';

  @override
  String get saleTransactionquationSumary =>
      'Sale Transaction Quotation Summary';

  @override
  String get sales => 'Sales';

  @override
  String get salesAndPurchaseReports => 'Sale & Purchase Reports';

  @override
  String get salesList => 'Sales List';

  @override
  String get salesPurchaseOverview => 'Sales Purchase Overview';

  @override
  String get salesReport => 'Sales Report';

  @override
  String get save => 'Save';

  @override
  String get saveNPublish => 'Save & Publish';

  @override
  String get search => 'Search';

  @override
  String get searchHere => 'Search here...';

  @override
  String get seeAllPromoCode => 'See all promo code';

  @override
  String get select => 'Select';

  @override
  String get selectABrand => 'Select a brand';

  @override
  String get selectAInvoicePrintSize => 'Select a invoice print size';

  @override
  String get selectAPaymentType => 'Select a payment type';

  @override
  String get selectAProduct => 'Select a product';

  @override
  String get selectAProductCategory => 'Select a product category';

  @override
  String get selectContacts => 'Select Contacts';

  @override
  String get selectCustomer => 'Select Customer';

  @override
  String get selectDate => 'Select Date';

  @override
  String get selectExpenseCategory => 'Select Expense Category';

  @override
  String get selectIncomeCategory => 'Select Income Category';

  @override
  String get selectProductCategory => 'Select Product Category';

  @override
  String get selectSize => 'Select Size';

  @override
  String get selectUnit => 'Select Unit';

  @override
  String get selectWarranty => 'Select Warranty';

  @override
  String get selectYourLanguage => 'Select Your Language';

  @override
  String get sendEmail => 'Send Email';

  @override
  String get sendMessage => 'Send Message';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get sendSms => 'Send SMS';

  @override
  String get sendSmsQuestion => 'Send SMS?';

  @override
  String get sendYourEmail => 'Send Your Email';

  @override
  String get setUpYourProfile => 'Set up your profile';

  @override
  String get setting => 'Settings';

  @override
  String get share => 'Share';

  @override
  String get size => 'Size';

  @override
  String get skip => 'Skip';

  @override
  String get sms => 'SMS';

  @override
  String get startDate => 'Start Date';

  @override
  String get startNewSale => 'Start New Sale';

  @override
  String get state => 'State';

  @override
  String get status => 'Status';

  @override
  String get stock => 'Stock';

  @override
  String get stockList => 'Stock List';

  @override
  String get stockvalue => 'Stock Value';

  @override
  String get subTotal => 'Sub Total';

  @override
  String get submit => 'Submit';

  @override
  String get subscription => 'Subscription';

  @override
  String get supplier => 'Supplier';

  @override
  String get supplierDue => 'Supplier Due';

  @override
  String get supplierName => 'Supplier Name';

  @override
  String get takeADriveruser =>
      'Take a driver\'s license, national identity card or passport photo';

  @override
  String get termsAndConditions => 'Terms & Conditions';

  @override
  String get thisCustomerHasnoDue => 'This customer has no due';

  @override
  String get to => 'To';

  @override
  String get total => 'Total';

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get totalCollected => 'Total Collected';

  @override
  String get totalDue => 'Total Due';

  @override
  String get totalExpense => 'Total Expense';

  @override
  String get totalIncome => 'Total Income';

  @override
  String get totalItems => 'Total Items';

  @override
  String get totalPayable => 'Total Payable';

  @override
  String get totalPaymentIn => 'Total Payment In';

  @override
  String get totalPaymentOut => 'Total Payment Out';

  @override
  String get totalPrice => 'Total Price';

  @override
  String get totalProduct => 'Total Product';

  @override
  String get totalPurchase => 'Total Purchase';

  @override
  String get totalSales => 'Total Sales';

  @override
  String get totalStock => 'Total Stocks';

  @override
  String get totalValue => 'Total Value';

  @override
  String get transaction => 'Transaction';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get twitter => 'Twitter';

  @override
  String get type => 'Type';

  @override
  String get unpaid => 'Unpaid';

  @override
  String get unitName => 'Unit Name';

  @override
  String get unitPrice => 'Unit Price';

  @override
  String get units => 'Units';

  @override
  String get unlimitedInvoices => 'Unlimited Invoices';

  @override
  String get unlimitedUsage => 'Unlimited Usage';

  @override
  String get unlockTheFull =>
      'Unlock the full potential of AmrDev POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes.';

  @override
  String get update => 'Update';

  @override
  String get updateContact => 'Update Contact';

  @override
  String get updateNow => 'Update Now';

  @override
  String get updateProduct => 'Update Product';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get updateYourProfile => 'Update your profile';

  @override
  String get updateYourSubscription => 'Update your subscription';

  @override
  String get uploadAnImage => 'Upload an image';

  @override
  String get uploadImage => 'Upload Image';

  @override
  String get useOfTheSystem => 'Use of the system';

  @override
  String get useMobiPos => 'Use AmrDev POS';

  @override
  String get userRole => 'User Role';

  @override
  String get userRoleDetails => 'User Role Details';

  @override
  String get vatGst => 'VAT/GST';

  @override
  String get view => 'View';

  @override
  String get viewAll => 'View All';

  @override
  String get viewDetails => 'View details';

  @override
  String get viewReport => 'View Report';

  @override
  String get warranty => 'Warranty';

  @override
  String get weight => 'Weight';

  @override
  String get whatsNew => 'What\'s New';

  @override
  String get wholesaler => 'Wholesaler';

  @override
  String get wholeSalePrice => 'Wholesale Price';

  @override
  String get writeYourMessageHere => 'Write your message here';

  @override
  String get wrongPasswordProvidedforThatUser =>
      'Wrong password provided for that user.';

  @override
  String get yearly => 'Yearly';

  @override
  String get yesDeleteForever => 'Yes, Delete Forever';

  @override
  String get youAreUsing => 'You are using';

  @override
  String get youHaveGotAnEmail => 'You Have Got An Email';

  @override
  String get youHaveSuccefulyLogin =>
      'You have successfully logged in to your account. Stay with AmrDev POS.';

  @override
  String get youHaveToReLogin => 'You have to re-login to your account.';

  @override
  String get youNeedToIdentityVerifyBeforeYouBuying =>
      'You need to identity verify before your buying sms';

  @override
  String get yourMessageRemains => 'Your message remains';

  @override
  String get yourPackage => 'Your Package';

  @override
  String get yourPackageWillExpireinDay => 'Your Package Will Expire in 5 Day';
}
