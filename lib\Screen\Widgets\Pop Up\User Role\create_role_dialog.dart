import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../Constant Data/constant.dart';
import '../../../../model/user_role_model.dart';
import '../../../../Provider/user_role_provider.dart';

class CreateRoleDialog extends ConsumerStatefulWidget {
  const CreateRoleDialog({super.key});

  @override
  ConsumerState<CreateRoleDialog> createState() => _CreateRoleDialogState();
}

class _CreateRoleDialogState extends ConsumerState<CreateRoleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _roleNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _descriptionController = TextEditingController();

  final Map<String, bool> _permissions = {
    'salePermission': false,
    'partiesPermission': false,
    'purchasePermission': false,
    'productPermission': false,
    'profileEditPermission': false,
    'addExpensePermission': false,
    'lossProfitPermission': false,
    'dueListPermission': false,
    'stockPermission': false,
    'reportsPermission': false,
    'salesListPermission': false,
    'purchaseListPermission': false,
  };

  final Map<String, String> _permissionLabels = {
    'salePermission': 'المبيعات',
    'partiesPermission': 'العملاء والموردين',
    'purchasePermission': 'المشتريات',
    'productPermission': 'المنتجات',
    'profileEditPermission': 'تعديل الملف الشخصي',
    'addExpensePermission': 'المصروفات',
    'lossProfitPermission': 'الأرباح والخسائر',
    'dueListPermission': 'المستحقات',
    'stockPermission': 'المخزون',
    'reportsPermission': 'التقارير',
    'salesListPermission': 'قائمة المبيعات',
    'purchaseListPermission': 'قائمة المشتريات',
  };

  @override
  void dispose() {
    _roleNameController.dispose();
    _emailController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: kMainColor.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    HugeIcons.strokeRoundedUserSettings01,
                    color: kMainColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'إنشاء دور جديد',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: kMainColor,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Role Name
                      TextFormField(
                        controller: _roleNameController,
                        decoration: InputDecoration(
                          labelText: 'اسم الدور *',
                          hintText: 'مثال: مدير المبيعات',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(HugeIcons.strokeRoundedUser),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم الدور مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Email
                      TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: 'البريد الإلكتروني *',
                          hintText: '<EMAIL>',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(HugeIcons.strokeRoundedMail01),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'البريد الإلكتروني مطلوب';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: 'الوصف (اختياري)',
                          hintText: 'وصف مختصر للدور ومسؤولياته',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(HugeIcons.strokeRoundedNote),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Permissions Section
                      Text(
                        'الصلاحيات',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اختر الصلاحيات المطلوبة لهذا الدور',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Quick Actions
                      Row(
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _permissions.updateAll((key, value) => true);
                              });
                            },
                            icon: const Icon(HugeIcons.strokeRoundedCheckmarkSquare02, size: 16),
                            label: const Text('تحديد الكل'),
                          ),
                          const SizedBox(width: 8),
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _permissions.updateAll((key, value) => false);
                              });
                            },
                            icon: const Icon(HugeIcons.strokeRoundedSquare, size: 16),
                            label: const Text('إلغاء الكل'),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Permissions Grid
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 4,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: _permissions.length,
                        itemBuilder: (context, index) {
                          final key = _permissions.keys.elementAt(index);
                          final label = _permissionLabels[key] ?? key;

                          return CheckboxListTile(
                            value: _permissions[key],
                            onChanged: (value) {
                              setState(() {
                                _permissions[key] = value ?? false;
                              });
                            },
                            title: Text(
                              label,
                              style: const TextStyle(fontSize: 14),
                            ),
                            controlAffinity: ListTileControlAffinity.leading,
                            dense: true,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade100,
                        foregroundColor: Colors.grey.shade700,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createRole,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        elevation: 2,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إنشاء الدور'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createRole() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      EasyLoading.show(status: 'جاري إنشاء الدور...');

      final userRole = UserRoleModel(
        email: _emailController.text.trim(),
        userTitle: _roleNameController.text.trim(),
        databaseId: DateTime.now().millisecondsSinceEpoch.toString(),
        salePermission: _permissions['salePermission'] ?? false,
        partiesPermission: _permissions['partiesPermission'] ?? false,
        purchasePermission: _permissions['purchasePermission'] ?? false,
        productPermission: _permissions['productPermission'] ?? false,
        profileEditPermission: _permissions['profileEditPermission'] ?? false,
        addExpensePermission: _permissions['addExpensePermission'] ?? false,
        lossProfitPermission: _permissions['lossProfitPermission'] ?? false,
        dueListPermission: _permissions['dueListPermission'] ?? false,
        stockPermission: _permissions['stockPermission'] ?? false,
        reportsPermission: _permissions['reportsPermission'] ?? false,
        salesListPermission: _permissions['salesListPermission'] ?? false,
        purchaseListPermission: _permissions['purchaseListPermission'] ?? false,
      );

      final DatabaseReference userRoleRef = FirebaseDatabase.instance
          .ref()
          .child('Admin Panel')
          .child('User Role');

      await userRoleRef.push().set(userRole.toJson());

      EasyLoading.showSuccess('تم إنشاء الدور بنجاح!');

      // Refresh the provider
      ref.invalidate(allAdminUserRoleProvider);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showError('فشل في إنشاء الدور: ${e.toString()}');
    }
  }
}
