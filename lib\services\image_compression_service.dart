import 'package:flutter/foundation.dart';

/// خدمة ضغط الصور لتحسين الأداء وتوفير البيانات
class ImageCompressionService {
  /// ضغط الصورة حسب الجودة المطلوبة
  static Uint8List? compressImage(
    Uint8List imageBytes, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
    int? maxSizeKB,
  }) {
    try {
      // للآن سنعيد الصورة كما هي
      // يمكن إضافة مكتبة image package لاحقاً للضغط الفعلي

      // فحص الحجم
      final currentSizeKB = imageBytes.length / 1024;

      if (maxSizeKB != null && currentSizeKB > maxSizeKB) {
        debugPrint(
            '⚠️ الصورة كبيرة جداً: ${currentSizeKB.toStringAsFixed(1)}KB > ${maxSizeKB}KB');

        // حساب نسبة الضغط المطلوبة
        final compressionRatio = maxSizeKB / currentSizeKB;
        final newQuality = (quality * compressionRatio).clamp(10, 100).toInt();

        debugPrint('🔄 ضغط الصورة بجودة: $newQuality%');

        // هنا يمكن إضافة الضغط الفعلي
        return imageBytes;
      }

      return imageBytes;
    } catch (e) {
      debugPrint('❌ خطأ في ضغط الصورة: $e');
      return imageBytes;
    }
  }

  /// تغيير حجم الصورة
  static Uint8List? resizeImage(
    Uint8List imageBytes, {
    int? maxWidth,
    int? maxHeight,
    bool maintainAspectRatio = true,
  }) {
    try {
      // للآن سنعيد الصورة كما هي
      // يمكن إضافة مكتبة image package لاحقاً لتغيير الحجم الفعلي

      debugPrint('🔄 تغيير حجم الصورة: ${maxWidth}x$maxHeight');

      return imageBytes;
    } catch (e) {
      debugPrint('❌ خطأ في تغيير حجم الصورة: $e');
      return imageBytes;
    }
  }

  /// ضغط تلقائي ذكي
  static Uint8List? smartCompress(Uint8List imageBytes) {
    try {
      final sizeKB = imageBytes.length / 1024;

      if (sizeKB < 100) {
        // صورة صغيرة - لا حاجة للضغط
        debugPrint(
            '✅ صورة صغيرة، لا حاجة للضغط: ${sizeKB.toStringAsFixed(1)}KB');
        return imageBytes;
      } else if (sizeKB < 500) {
        // صورة متوسطة - ضغط خفيف
        debugPrint(
            '🔄 ضغط خفيف للصورة المتوسطة: ${sizeKB.toStringAsFixed(1)}KB');
        return compressImage(imageBytes, quality: 90);
      } else if (sizeKB < 2000) {
        // صورة كبيرة - ضغط متوسط
        debugPrint(
            '🔄 ضغط متوسط للصورة الكبيرة: ${sizeKB.toStringAsFixed(1)}KB');
        return compressImage(imageBytes, quality: 80, maxSizeKB: 1000);
      } else {
        // صورة كبيرة جداً - ضغط قوي
        debugPrint(
            '🔄 ضغط قوي للصورة الكبيرة جداً: ${sizeKB.toStringAsFixed(1)}KB');
        return compressImage(imageBytes, quality: 70, maxSizeKB: 1500);
      }
    } catch (e) {
      debugPrint('❌ خطأ في الضغط الذكي: $e');
      return imageBytes;
    }
  }

  /// التحقق من نوع الصورة
  static String? getImageType(Uint8List imageBytes) {
    if (imageBytes.length < 4) return null;

    // فحص JPEG
    if (imageBytes[0] == 0xFF && imageBytes[1] == 0xD8) {
      return 'jpeg';
    }

    // فحص PNG
    if (imageBytes[0] == 0x89 &&
        imageBytes[1] == 0x50 &&
        imageBytes[2] == 0x4E &&
        imageBytes[3] == 0x47) {
      return 'png';
    }

    // فحص GIF
    if (imageBytes[0] == 0x47 &&
        imageBytes[1] == 0x49 &&
        imageBytes[2] == 0x46) {
      return 'gif';
    }

    // فحص WebP
    if (imageBytes.length >= 12 &&
        imageBytes[0] == 0x52 &&
        imageBytes[1] == 0x49 &&
        imageBytes[2] == 0x46 &&
        imageBytes[3] == 0x46 &&
        imageBytes[8] == 0x57 &&
        imageBytes[9] == 0x45 &&
        imageBytes[10] == 0x42 &&
        imageBytes[11] == 0x50) {
      return 'webp';
    }

    return 'unknown';
  }

  /// الحصول على معلومات الصورة
  static Map<String, dynamic> getImageInfo(Uint8List imageBytes) {
    final sizeBytes = imageBytes.length;
    final sizeKB = sizeBytes / 1024;
    final sizeMB = sizeKB / 1024;
    final type = getImageType(imageBytes);

    return {
      'sizeBytes': sizeBytes,
      'sizeKB': sizeKB,
      'sizeMB': sizeMB,
      'type': type,
      'isValid': type != null && type != 'unknown',
      'recommendedCompression': _getCompressionRecommendation(sizeKB),
    };
  }

  /// اقتراح نوع الضغط المناسب
  static String _getCompressionRecommendation(double sizeKB) {
    if (sizeKB < 100) {
      return 'لا حاجة للضغط';
    } else if (sizeKB < 500) {
      return 'ضغط خفيف (90% جودة)';
    } else if (sizeKB < 2000) {
      return 'ضغط متوسط (80% جودة)';
    } else {
      return 'ضغط قوي (70% جودة)';
    }
  }

  /// تحويل الصورة إلى تنسيق محدد
  static Uint8List? convertImageFormat(
    Uint8List imageBytes,
    String targetFormat, {
    int quality = 85,
  }) {
    try {
      // للآن سنعيد الصورة كما هي
      // يمكن إضافة مكتبة image package لاحقاً للتحويل الفعلي

      debugPrint('🔄 تحويل الصورة إلى تنسيق: $targetFormat');

      return imageBytes;
    } catch (e) {
      debugPrint('❌ خطأ في تحويل تنسيق الصورة: $e');
      return imageBytes;
    }
  }
}

/// إحصائيات ضغط الصور
class CompressionStats {
  static int _totalCompressions = 0;
  static double _totalOriginalSizeMB = 0.0;
  static double _totalCompressedSizeMB = 0.0;

  static void recordCompression(
      double originalSizeMB, double compressedSizeMB) {
    _totalCompressions++;
    _totalOriginalSizeMB += originalSizeMB;
    _totalCompressedSizeMB += compressedSizeMB;
  }

  static Map<String, dynamic> getStats() {
    final savedSizeMB = _totalOriginalSizeMB - _totalCompressedSizeMB;
    final compressionRatio = _totalOriginalSizeMB > 0
        ? (savedSizeMB / _totalOriginalSizeMB * 100)
        : 0.0;

    return {
      'totalCompressions': _totalCompressions,
      'totalOriginalSizeMB': _totalOriginalSizeMB,
      'totalCompressedSizeMB': _totalCompressedSizeMB,
      'savedSizeMB': savedSizeMB,
      'compressionRatio': compressionRatio,
      'averageOriginalSizeMB': _totalCompressions > 0
          ? (_totalOriginalSizeMB / _totalCompressions)
          : 0.0,
      'averageCompressedSizeMB': _totalCompressions > 0
          ? (_totalCompressedSizeMB / _totalCompressions)
          : 0.0,
    };
  }

  static void reset() {
    _totalCompressions = 0;
    _totalOriginalSizeMB = 0.0;
    _totalCompressedSizeMB = 0.0;
  }
}
