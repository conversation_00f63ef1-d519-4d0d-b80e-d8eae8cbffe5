import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker_web/image_picker_web.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:salespro_saas_admin/Provider/shop_category_provider.dart';
import 'package:salespro_saas_admin/Screen/Widgets/Constant%20Data/constant.dart';
import 'package:salespro_saas_admin/model/shop_category_model.dart';

class NewCategory extends StatefulWidget {
  const NewCategory({super.key, required this.listOfIncomeCategory});
  final List<ShopCategoryModel> listOfIncomeCategory;

  @override
  State<NewCategory> createState() => _NewCategoryState();
}

class _NewCategoryState extends State<NewCategory> {
  TextEditingController categoryNameController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  // متغيرات الصورة
  Uint8List? _selectedImageBytes;
  String? _uploadedImageUrl;
  bool _isUploadingImage = false;

  // دالة رفع الصورة
  Future<void> _uploadCategoryImage() async {
    if (kIsWeb) {
      try {
        setState(() {
          _isUploadingImage = true;
        });

        EasyLoading.show(
          status: 'جاري رفع صورة الفئة...',
          dismissOnTap: false,
        );

        Uint8List? bytesFromPicker = await ImagePickerWeb.getImageAsBytes();

        if (bytesFromPicker != null) {
          // رفع الصورة إلى Firebase Storage
          var snapshot = await FirebaseStorage.instance
              .ref('Category Images/${DateTime.now().millisecondsSinceEpoch}')
              .putData(bytesFromPicker);

          var url = await snapshot.ref.getDownloadURL();

          setState(() {
            _selectedImageBytes = bytesFromPicker;
            _uploadedImageUrl = url;
          });

          EasyLoading.showSuccess('تم رفع الصورة بنجاح!');
        } else {
          EasyLoading.dismiss();
        }
      } catch (e) {
        EasyLoading.dismiss();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في رفع الصورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() {
          _isUploadingImage = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> names = [];
    for (var element in widget.listOfIncomeCategory) {
      names.add(element.categoryName!.removeAllWhiteSpace().toLowerCase());
    }
    return Consumer(
      builder: (_, ref, watch) {
        return SizedBox(
          width: 500,
          child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'إضافة فئة جديدة',
                        style: kTextStyle.copyWith(
                            color: kTitleColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 18.0),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: (() => Navigator.pop(context)),
                        child: Container(
                          padding: const EdgeInsets.all(4.0),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.transparent,
                            border: Border.all(
                              color: kRedTextColor.withValues(alpha: 0.1),
                            ),
                          ),
                          child:
                              const Icon(FeatherIcons.x, color: kRedTextColor),
                        ),
                      ),
                    ],
                  ),
                  const Divider(thickness: 1.0, color: kBorderColorTextField),
                  const SizedBox(height: 20.0),
                  TextField(
                    controller: categoryNameController,
                    showCursor: true,
                    cursorColor: kTitleColor,
                    decoration: kInputDecoration.copyWith(
                      labelText: 'اسم الفئة',
                      labelStyle: kTextStyle.copyWith(color: kTitleColor),
                      hintText: 'اكتب اسم الفئة',
                      hintStyle: kTextStyle.copyWith(color: kGreyTextColor),
                      enabledBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8.0)),
                        borderSide:
                            BorderSide(color: kBorderColorTextField, width: 2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20.0),

                  // قسم رفع صورة الفئة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                      border:
                          Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.image,
                                color: Colors.blue, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'صورة الفئة (اختيارية)',
                              style: kTextStyle.copyWith(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // معاينة الصورة
                        if (_selectedImageBytes != null)
                          Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.memory(
                                _selectedImageBytes!,
                                height: 100,
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),

                        // أزرار التحكم
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _isUploadingImage
                                    ? null
                                    : _uploadCategoryImage,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8),
                                ),
                                icon: _isUploadingImage
                                    ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : const Icon(Icons.upload, size: 16),
                                label: Text(
                                  _isUploadingImage
                                      ? 'جاري الرفع...'
                                      : 'رفع صورة',
                                  style: kTextStyle.copyWith(
                                      color: Colors.white, fontSize: 12),
                                ),
                              ),
                            ),
                            if (_uploadedImageUrl != null) ...[
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    _selectedImageBytes = null;
                                    _uploadedImageUrl = null;
                                  });
                                },
                                icon: const Icon(Icons.delete,
                                    color: Colors.red, size: 20),
                                tooltip: 'حذف الصورة',
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20.0),

                  TextFormField(
                    controller: descriptionController,
                    showCursor: true,
                    cursorColor: kTitleColor,
                    maxLines: 4,
                    decoration: kInputDecoration.copyWith(
                      labelText: 'الوصف',
                      labelStyle: kTextStyle.copyWith(color: kTitleColor),
                      hintText: 'اكتب وصف الفئة',
                      hintStyle: kTextStyle.copyWith(color: kGreyTextColor),
                      floatingLabelBehavior: FloatingLabelBehavior.always,
                      enabledBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8.0)),
                        borderSide:
                            BorderSide(color: kBorderColorTextField, width: 2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // أزرار الحفظ والإلغاء
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey.shade300, width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        // زر الإلغاء
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey.shade100,
                              foregroundColor: kRedTextColor,
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: kRedTextColor),
                              ),
                            ),
                            child: Text(
                              'إلغاء',
                              style: kTextStyle.copyWith(
                                color: kRedTextColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // زر الحفظ
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () async {
                              if (categoryNameController.text != '' &&
                                  !names.contains(categoryNameController.text
                                      .toLowerCase()
                                      .removeAllWhiteSpace())) {
                                if (categoryNameController.text.isEmpty) {
                                  EasyLoading.showError('اسم الفئة مطلوب');
                                } else {
                                  try {
                                    EasyLoading.show(
                                        status: 'بيحمل...', dismissOnTap: false);
                                    // ignore: no_leading_underscores_for_local_identifiers
                                    final DatabaseReference shopCategoryRef =
                                        FirebaseDatabase.instance
                                            .ref()
                                            .child('Admin Panel')
                                            .child('Category');
                                    ShopCategoryModel shopCategoryModel =
                                        ShopCategoryModel(
                                      categoryName: categoryNameController.text,
                                      description: descriptionController.text,
                                      imageUrl: _uploadedImageUrl,
                                    );
                                    await shopCategoryRef
                                        .push()
                                        .set(shopCategoryModel.toJson());
                                    EasyLoading.showSuccess('تم الإضافة بنجاح!');
                                    // ignore: unused_result
                                    ref.refresh(shopCategoryProvider);
                                    Future.delayed(
                                        const Duration(milliseconds: 100), () {
                                      if (context.mounted) Navigator.pop(context);
                                    });
                                  } catch (e) {
                                    EasyLoading.dismiss();
                                    EasyLoading.showError('فشل: ${e.toString()}');
                                  }
                                }
                              } else if (names.contains(categoryNameController
                                  .text
                                  .toLowerCase()
                                  .removeAllWhiteSpace())) {
                                EasyLoading.showError('اسم الفئة موجود بالفعل');
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: kMainColor,
                              foregroundColor: Colors.white,
                              elevation: 2,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'حفظ',
                              style: kTextStyle.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              )),
        );
      },
    );
  }
}
