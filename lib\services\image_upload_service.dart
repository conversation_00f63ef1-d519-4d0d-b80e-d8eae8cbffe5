import 'package:flutter/foundation.dart';
import 'gofile_upload_service.dart';

/// خدمة رفع الصور المحسنة للويب
class ImageUploadService {
  /// رفع صورة باستخدام GoFile (محسن للويب)
  static Future<Map<String, dynamic>> uploadImage(
    Uint8List imageBytes, {
    String fileName = 'image.jpg',
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 رفع صورة باستخدام GoFile...');
      }

      // التحقق من صحة الصورة
      if (imageBytes.isEmpty) {
        return {
          'success': false,
          'error': 'الصورة فارغة',
        };
      }

      // التحقق من حجم الصورة (الحد الأقصى 10MB للصور)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (imageBytes.length > maxSize) {
        return {
          'success': false,
          'error': 'حجم الصورة كبير جداً (أكثر من 10MB)',
        };
      }

      // رفع الصورة على GoFile
      final result = await GoFileService.uploadFile(
        imageBytes,
        fileName,
        mimeType: 'image/jpeg',
      );

      if (result['success'] == true) {
        if (kDebugMode) {
          debugPrint('✅ تم رفع الصورة بنجاح على GoFile');
        }

        // تسجيل الإحصائيات
        final sizeGB = imageBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(true, sizeGB);

        return {
          'success': true,
          'service': 'gofile',
          'downloadPage': result['downloadPage'],
          'directLink': result['downloadPage'], // استخدام رابط التحميل
          'fileId': result['fileId'],
          'fileName': result['fileName'],
          'fileType': result['fileType'],
        };
      } else {
        // تسجيل فشل الرفع
        final sizeGB = imageBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(false, sizeGB);

        return {
          'success': false,
          'error': result['error'] ?? 'فشل في رفع الصورة',
          'service': 'gofile',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في رفع الصورة: $e');
      }
      return {
        'success': false,
        'error': 'خطأ في رفع الصورة: $e',
      };
    }
  }

  /// ضغط الصورة إذا كانت كبيرة
  static Uint8List? compressImage(Uint8List imageBytes) {
    try {
      // للآن سنعيد الصورة كما هي
      // يمكن إضافة مكتبة image package لاحقاً للضغط الفعلي
      
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (imageBytes.length <= maxSize) {
        if (kDebugMode) {
          final sizeKB = imageBytes.length / 1024;
          debugPrint('✅ صورة صغيرة، لا حاجة للضغط: ${sizeKB.toStringAsFixed(1)}KB');
        }
        return imageBytes;
      }

      if (kDebugMode) {
        final sizeMB = imageBytes.length / (1024 * 1024);
        debugPrint('🔄 ضغط صورة كبيرة: ${sizeMB.toStringAsFixed(1)}MB');
      }

      // للآن سنعيد الصورة كما هي حتى لو كانت كبيرة
      // GoFile يدعم حتى 5GB
      return imageBytes;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في ضغط الصورة: $e');
      }
      return imageBytes;
    }
  }

  /// التحقق من صحة الصورة
  static bool isValidImage(Uint8List imageBytes) {
    // التحقق من أن الصورة ليست فارغة
    if (imageBytes.isEmpty) {
      return false;
    }

    // التحقق من الحد الأقصى للحجم (50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (imageBytes.length > maxSize) {
      return false;
    }

    // التحقق من header الصورة (اختياري)
    try {
      // فحص بسيط لـ JPEG
      if (imageBytes.length >= 2) {
        if (imageBytes[0] == 0xFF && imageBytes[1] == 0xD8) {
          return true; // JPEG
        }
      }

      // فحص بسيط لـ PNG
      if (imageBytes.length >= 8) {
        if (imageBytes[0] == 0x89 && 
            imageBytes[1] == 0x50 && 
            imageBytes[2] == 0x4E && 
            imageBytes[3] == 0x47) {
          return true; // PNG
        }
      }

      // فحص بسيط لـ GIF
      if (imageBytes.length >= 6) {
        if (imageBytes[0] == 0x47 && 
            imageBytes[1] == 0x49 && 
            imageBytes[2] == 0x46) {
          return true; // GIF
        }
      }

      // فحص بسيط لـ WebP
      if (imageBytes.length >= 12) {
        if (imageBytes[0] == 0x52 && 
            imageBytes[1] == 0x49 && 
            imageBytes[2] == 0x46 && 
            imageBytes[3] == 0x46 &&
            imageBytes[8] == 0x57 && 
            imageBytes[9] == 0x45 && 
            imageBytes[10] == 0x42 && 
            imageBytes[11] == 0x50) {
          return true; // WebP
        }
      }

      // إذا لم نتمكن من التعرف على النوع، نقبل الملف
      return true;
    } catch (e) {
      // في حالة خطأ، نقبل الملف
      return true;
    }
  }

  /// حذف صورة
  static Future<bool> deleteImage(String fileId) async {
    try {
      return await GoFileService.deleteFile(fileId);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في حذف الصورة: $e');
      }
      return false;
    }
  }

  /// الحصول على إحصائيات الرفع
  static Map<String, dynamic> getStats() {
    return GoFileStats.getStats();
  }

  /// إعادة تعيين الإحصائيات
  static void resetStats() {
    GoFileStats.reset();
  }
}
