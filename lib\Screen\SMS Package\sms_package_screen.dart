import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:salespro_saas_admin/model/sms_subscription_plan_model.dart';
import '../../Provider/sms_package_provider.dart';
import '../../currency.dart';
import '../Widgets/Constant Data/constant.dart';
import '../Widgets/Constant Data/export_button.dart';

import '../Widgets/Topbar/topbar.dart';

class SMSPackage extends StatefulWidget {
  const SMSPackage({super.key});

  static const String route = '/sms_package';

  @override
  State<SMSPackage> createState() => _SMSPackageState();
}

class _SMSPackageState extends State<SMSPackage> {
  void newSubscriptionPlanAdd(
      {required WidgetRef ref, required List<String> allNames}) {
    GlobalKey<FormState> globalKey = GlobalKey<FormState>();
    SmsSubscriptionPlanModel smsSubscriptionPlanModel =
        SmsSubscriptionPlanModel(
      smsPackName: '',
      smsPackPrice: 0,
      smsPackOfferPrice: 0,
      numberOfSMS: 0,
      smsValidityInDay: 0,
    );

    bool validateAndSave() {
      final form = globalKey.currentState;
      if (form!.validate()) {
        form.save();
        return true;
      }
      return false;
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        double temp = 0;
        return StatefulBuilder(builder: (context, setState1) {
          return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: SingleChildScrollView(
                child: SizedBox(
                  width: 600,
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: globalKey,
                          child: Column(
                            children: [
                              ///________Name__________________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'اسم الباقة',
                                    hintText: 'اكتب اسم الباقة.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'اسم باقة الرسائل مطلوب.';
                                    } else if (allNames.contains(value
                                        ?.toLowerCase()
                                        .removeAllWhiteSpace())) {
                                      return 'اسم باقة الرسائل موجود بالفعل.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.smsPackName =
                                        value!;
                                  },
                                ),
                              ),

                              ///__________Price & Offer Price_______________________________
                              Row(
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        validator: (value) {
                                          if (value.isEmptyOrNull) {
                                            return 'سعر الباقة مطلوب';
                                          } else if (int.parse(value!) <= 0) {
                                            return 'سعر الباقة غير صحيح';
                                          }
                                          return null;
                                        },
                                        onChanged: (value) {
                                          temp = value.toDouble();
                                        },
                                        onSaved: (value) {
                                          smsSubscriptionPlanModel
                                              .smsPackPrice = value.toDouble();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر الباقة',
                                          hintText: 'اكتب السعر العادي للباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        validator: (value) {
                                          if (value.isEmptyOrNull) {
                                            return null;
                                          } else if (double.parse(value!) >=
                                              temp) {
                                            return 'سعر العرض مايقدرش يكون أكبر من السعر العادي';
                                          }
                                          return null;
                                        },
                                        onSaved: (value) {
                                          smsSubscriptionPlanModel
                                                  .smsPackOfferPrice =
                                              value.toDouble().abs();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر العرض',
                                          hintText: 'اكتب سعر عرض الباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              ///__________timer duration____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'صلاحية الرسائل بالأيام',
                                    hintText: 'اكتب الصلاحية بالأيام.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'الصلاحية مطلوبة.';
                                    } else if (int.parse(value!) <= 0) {
                                      return 'صلاحية الرسائل غير صحيحة';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.smsValidityInDay =
                                        value.toInt();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),

                              ///__________timer duration____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'عدد الرسائل',
                                    hintText: 'اكتب عدد الرسائل.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'عدد الرسائل مطلوب.';
                                    } else if (int.parse(value!) <= 0) {
                                      return 'عدد الرسائل غير صحيح';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.numberOfSMS =
                                        value.toInt();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///_______buttons__________________________________
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: (() => Navigator.pop(context)),
                              child: Container(
                                width: 100,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: Colors.red),
                                child: Column(
                                  children: [
                                    Text(
                                      'إلغاء',
                                      style: kTextStyle.copyWith(
                                          color: kWhiteTextColor),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),
                            GestureDetector(
                              onTap: () async {
                                if (validateAndSave()) {
                                  EasyLoading.show(
                                      status: 'بيحمل...', dismissOnTap: false);
                                  final DatabaseReference adRef =
                                      FirebaseDatabase.instance
                                          .ref()
                                          .child('Admin Panel')
                                          .child('Sms Package Plan');

                                  smsSubscriptionPlanModel.numberOfSMS == 0
                                      ? smsSubscriptionPlanModel.numberOfSMS =
                                          -202
                                      : null;
                                  await adRef
                                      .push()
                                      .set(smsSubscriptionPlanModel.toJson());
                                  EasyLoading.showSuccess('تم الإضافة بنجاح',
                                      duration:
                                          const Duration(milliseconds: 500));

                                  ///____provider_refresh____________________________________________
                                  // ignore: unused_result
                                  ref.refresh(smsPackageProvider);

                                  Future.delayed(
                                      const Duration(milliseconds: 100), () {
                                    if (context.mounted) Navigator.pop(context);
                                  });
                                }
                              },
                              child: Container(
                                width: 100,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: kBlueTextColor),
                                child: Column(
                                  children: [
                                    Text(
                                      'حفظ',
                                      style: kTextStyle.copyWith(
                                          color: kWhiteTextColor),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ));
        });
      },
    );
  }

  Future<void> deletePlan(
      {required WidgetRef updateRef, required String name}) async {
    showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext dialogContext) {
          return Center(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(
                  Radius.circular(15),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'عاوز تحذف الباقة دي؟',
                      style: TextStyle(fontSize: 22),
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GestureDetector(
                          child: Container(
                            width: 130,
                            height: 50,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.all(
                                Radius.circular(15),
                              ),
                            ),
                            child: const Center(
                              child: Text(
                                'إلغاء',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                          onTap: () {
                            Navigator.pop(dialogContext);
                          },
                        ),
                        const SizedBox(width: 30),
                        GestureDetector(
                          child: Container(
                            width: 130,
                            height: 50,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.all(
                                Radius.circular(15),
                              ),
                            ),
                            child: const Center(
                              child: Text(
                                'حذف',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                          onTap: () async {
                            EasyLoading.show(status: 'بيحذف..');
                            String imageKey = '';
                            await FirebaseDatabase.instance
                                .ref()
                                .child('Admin Panel')
                                .child('Sms Package Plan')
                                .orderByKey()
                                .get()
                                .then((value) async {
                              for (var element in value.children) {
                                var data =
                                    jsonDecode(jsonEncode(element.value));
                                if (data['smsPackName'].toString() == name) {
                                  imageKey = element.key.toString();
                                }
                              }
                            });
                            DatabaseReference ref = FirebaseDatabase.instance
                                .ref("Admin Panel/Sms Package Plan/$imageKey");
                            await ref.remove();
                            // ignore: unused_result
                            updateRef.refresh(smsPackageProvider);

                            EasyLoading.showSuccess('تم');
                            // ignore: use_build_context_synchronously
                            Navigator.pop(dialogContext);
                          },
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  void editSubscriptionPlan(
      {required WidgetRef updateRef,
      required List<String> allNames,
      required SmsSubscriptionPlanModel selectedOne}) {
    GlobalKey<FormState> globalKey = GlobalKey<FormState>();
    SmsSubscriptionPlanModel smsSubscriptionPlanModel =
        SmsSubscriptionPlanModel(
      smsPackName: '',
      smsPackPrice: 0,
      smsPackOfferPrice: 0,
      numberOfSMS: 0,
      smsValidityInDay: 0,
    );

    bool validateAndSave() {
      final form = globalKey.currentState;
      if (form!.validate()) {
        form.save();
        return true;
      }
      return false;
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState1) {
          return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: SingleChildScrollView(
                child: SizedBox(
                  width: 600,
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: globalKey,
                          child: Column(
                            children: [
                              ///________Name__________________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  initialValue: selectedOne.smsPackName,
                                  readOnly: selectedOne.smsPackName == 'Basic'
                                      ? true
                                      : false,
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'اسم الباقة',
                                    hintText: 'اكتب اسم الباقة.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'اسم باقة الرسائل مطلوب.';
                                    } else if (allNames.contains(value
                                            ?.toLowerCase()
                                            .removeAllWhiteSpace()) &&
                                        selectedOne.smsPackName != value) {
                                      return 'اسم الباقة موجود بالفعل.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.smsPackName =
                                        value!;
                                  },
                                ),
                              ),

                              ///__________Price & Offer Price_______________________________
                              Row(
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        initialValue:
                                            selectedOne.smsPackPrice.toString(),
                                        validator: (value) {
                                          if (value.isEmptyOrNull) {
                                            return 'سعر الباقة مطلوب';
                                          } else if (int.parse(value!) <= 0) {
                                            return 'سعر الباقة غير صحيح';
                                          }
                                          return null;
                                        },
                                        onSaved: (value) {
                                          smsSubscriptionPlanModel
                                                  .smsPackPrice =
                                              value.toDouble().abs();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر الباقة',
                                          hintText: 'اكتب السعر العادي للباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        initialValue: selectedOne
                                            .smsPackOfferPrice
                                            .toString(),
                                        validator: (value) {
                                          return null;
                                        },
                                        onSaved: (value) {
                                          smsSubscriptionPlanModel
                                                  .smsPackOfferPrice =
                                              value.toDouble().abs();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر العرض',
                                          hintText: 'اكتب سعر عرض الباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              ///__________timer duration____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  initialValue:
                                      selectedOne.smsValidityInDay.toString(),
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'صلاحية الرسائل بالأيام',
                                    hintText: 'اكتب صلاحية الرسائل بالأيام.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'صلاحية الرسائل مطلوبة.';
                                    } else if (int.parse(value!) <= 0) {
                                      return 'صلاحية الرسائل غير صحيحة';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.smsValidityInDay =
                                        value.toInt().abs();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),

                              ///__________Number Of Sms____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  initialValue:
                                      selectedOne.smsValidityInDay.toString(),
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'عدد الرسائل',
                                    hintText: 'اكتب عدد الرسائل.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'عدد الرسائل مطلوب.';
                                    } else if (int.parse(value!) <= 0) {
                                      return 'عدد الرسائل غير صحيح';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    smsSubscriptionPlanModel.numberOfSMS =
                                        value.toInt().abs();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///_______buttons__________________________________
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: (() => Navigator.pop(context)),
                              child: Container(
                                width: 100,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: Colors.red),
                                child: Column(
                                  children: [
                                    Text(
                                      'إلغاء',
                                      style: kTextStyle.copyWith(
                                          color: kWhiteTextColor),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),
                            GestureDetector(
                              onTap: () async {
                                if (validateAndSave()) {
                                  EasyLoading.show(status: 'بيعدل');
                                  String imageKey = '';
                                  await FirebaseDatabase.instance
                                      .ref()
                                      .child('Admin Panel')
                                      .child('Sms Package Plan')
                                      .orderByKey()
                                      .get()
                                      .then((value) async {
                                    for (var element in value.children) {
                                      var data =
                                          jsonDecode(jsonEncode(element.value));
                                      if (data['smsPackName'].toString() ==
                                          selectedOne.smsPackName) {
                                        imageKey = element.key.toString();
                                      }
                                    }
                                  });
                                  DatabaseReference ref =
                                      FirebaseDatabase.instance.ref(
                                          "Admin Panel/Sms Package Plan/$imageKey");
                                  await ref.update({
                                    'smsPackName':
                                        smsSubscriptionPlanModel.smsPackName,
                                    'smsPackPrice':
                                        smsSubscriptionPlanModel.smsPackPrice,
                                    'smsPackOfferPrice':
                                        smsSubscriptionPlanModel
                                            .smsPackOfferPrice,
                                    'numberOfSMS':
                                        smsSubscriptionPlanModel.numberOfSMS,
                                    'smsValidityInDay': smsSubscriptionPlanModel
                                        .smsValidityInDay,
                                  });
                                  EasyLoading.showSuccess('تم التحديث بنجاح!');

                                  ///____provider_refresh____________________________________________
                                  // ignore: unused_result
                                  updateRef.refresh(smsPackageProvider);

                                  Future.delayed(
                                      const Duration(milliseconds: 100), () {
                                    if (context.mounted) Navigator.pop(context);
                                  });
                                }
                              },
                              child: Container(
                                width: 100,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: kBlueTextColor),
                                child: Column(
                                  children: [
                                    Text(
                                      'حفظ',
                                      style: kTextStyle.copyWith(
                                          color: kWhiteTextColor),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ));
        });
      },
    );
  }

  void postBasicPlan({required WidgetRef ref}) async {
    SmsSubscriptionPlanModel smsSubscriptionPlanModel =
        SmsSubscriptionPlanModel(
      smsPackName: 'أساسي',
      smsPackPrice: 50,
      smsPackOfferPrice: 40,
      numberOfSMS: 500,
      smsValidityInDay: 30,
    );
    final DatabaseReference adRef = FirebaseDatabase.instance
        .ref()
        .child('Admin Panel')
        .child('Sms Package Plan');
    await adRef.push().set(smsSubscriptionPlanModel.toJson());

    ///____provider_refresh____________________________________________
    // ignore: unused_result
    ref.refresh(smsPackageProvider);
  }

  int counter = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      body: Consumer(
        builder: (_, ref, watch) {
          final reports = ref.watch(smsPackageProvider);
          return reports.when(data: (data) {
            List<String> names = [];
            for (var element in data) {
              names
                  .add(element.smsPackName.removeAllWhiteSpace().toLowerCase());
            }
            if (data.isNotEmpty) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6.0),
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: kWhiteTextColor,
                      ),
                      child: const TopBar(),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Container(
                        padding: const EdgeInsets.all(10.0),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.0),
                            color: kWhiteTextColor),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'باقات الرسائل النصية',
                                  style: kTextStyle.copyWith(
                                      color: kTitleColor,
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold),
                                ),
                                const Spacer(),
                                GestureDetector(
                                  onTap: (() => newSubscriptionPlanAdd(
                                      ref: ref, allNames: names)),
                                  child: Container(
                                    padding: const EdgeInsets.all(5.0),
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        color: kBlueTextColor),
                                    child: Column(
                                      children: [
                                        Text(
                                          'إضافة باقة رسائل جديدة',
                                          style: kTextStyle.copyWith(
                                              color: kWhiteTextColor),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                            const SizedBox(height: 10.0),
                            const Divider(
                              height: 1,
                              color: Colors.black12,
                            ),
                            const SizedBox(height: 10.0),
                            Row(
                              children: [
                                SizedBox(
                                  height: 40,
                                  width:
                                      MediaQuery.of(context).size.width * .25,
                                  child: TextField(
                                    showCursor: true,
                                    cursorColor: kTitleColor,
                                    decoration: kInputDecoration.copyWith(
                                      hintText: 'ابحث عن أي حاجة...',
                                      suffixIcon: Padding(
                                        padding: const EdgeInsets.all(4.0),
                                        child: Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                            color: kBlueTextColor,
                                          ),
                                          child: const Icon(FeatherIcons.search,
                                              color: kWhiteTextColor),
                                        ),
                                      ),
                                      hintStyle: kTextStyle.copyWith(
                                          color: kLitGreyColor),
                                      contentPadding: const EdgeInsets.all(4.0),
                                      enabledBorder: const OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(8.0),
                                        ),
                                        borderSide: BorderSide(
                                            color: kBorderColorTextField,
                                            width: 1),
                                      ),
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                const ExportButton()
                              ],
                            ).visible(false),
                            const SizedBox(height: 10.0).visible(false),
                            SizedBox(
                              height: 320,
                              child: ListView.builder(
                                physics: const ClampingScrollPhysics(),
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                itemCount: data.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: Column(
                                      children: [
                                        SizedBox(
                                          width: 200,
                                          child: Stack(
                                            alignment: Alignment.bottomCenter,
                                            children: [
                                              Container(
                                                height: 250,
                                                width: 200,
                                                decoration: BoxDecoration(
                                                  color: kGreenTextColor
                                                      .withValues(alpha: 0.1),
                                                  borderRadius:
                                                      const BorderRadius.all(
                                                    Radius.circular(10),
                                                  ),
                                                  border: Border.all(
                                                    width: 1,
                                                    color: kGreenTextColor,
                                                  ),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const SizedBox(height: 5),
                                                    Text(
                                                      data[index].smsPackName,
                                                      style: const TextStyle(
                                                          fontSize: 22,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color:
                                                              kGreenTextColor),
                                                    ),
                                                    const SizedBox(height: 5),
                                                    Text(
                                                      '$currency${data[index].smsPackOfferPrice > 0 ? data[index].smsPackOfferPrice : data[index].smsPackPrice}',
                                                      style: const TextStyle(
                                                          fontSize: 20,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color:
                                                              kGreenTextColor),
                                                    ),
                                                    Text(
                                                      '$currency${data[index].smsPackPrice}',
                                                      style: const TextStyle(
                                                          decoration:
                                                              TextDecoration
                                                                  .lineThrough,
                                                          fontSize: 14,
                                                          color: Colors.grey),
                                                    ).visible(data[index]
                                                            .smsPackOfferPrice >
                                                        0),
                                                    const SizedBox(height: 15),
                                                    Text(
                                                      'عدد الرسائل: ${data[index].numberOfSMS}',
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 5),
                                                    Text(
                                                      'الصلاحية ${data[index].smsValidityInDay} يوم',
                                                      style: const TextStyle(
                                                          color:
                                                              kGreyTextColor),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Positioned(
                                                top: 0,
                                                left: 0,
                                                child: Container(
                                                  height: 25,
                                                  width: 70,
                                                  decoration:
                                                      const BoxDecoration(
                                                    color: kGreenTextColor,
                                                    borderRadius:
                                                        BorderRadius.only(
                                                      topLeft:
                                                          Radius.circular(10),
                                                      bottomRight:
                                                          Radius.circular(10),
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      data[index].smsPackOfferPrice ==
                                                              data[index]
                                                                  .smsPackPrice
                                                          ? ""
                                                          : 'وفر ${(100 - ((data[index].smsPackOfferPrice * 100) / data[index].smsPackPrice)).toInt().toString()}%',
                                                      style: const TextStyle(
                                                          color: Colors.white),
                                                    ),
                                                  ),
                                                ),
                                              ).visible(data[index]
                                                      .smsPackOfferPrice >
                                                  0),
                                            ],
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Row(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  editSubscriptionPlan(
                                                      updateRef: ref,
                                                      allNames: names,
                                                      selectedOne: data[index]);
                                                },
                                                child: Container(
                                                  height: 40,
                                                  width: 40,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        const BorderRadius.all(
                                                            Radius.circular(
                                                                90)),
                                                    border: Border.all(
                                                        width: 1,
                                                        color: kGreenTextColor),
                                                    color: kGreenTextColor
                                                        .withValues(alpha: 0.1),
                                                  ),
                                                  child: const Center(
                                                    child: Icon(
                                                      Icons.edit,
                                                      color: kGreenTextColor,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 20).visible(
                                                  data[index].smsPackName !=
                                                      'Free'),
                                              GestureDetector(
                                                onTap: () {
                                                  deletePlan(
                                                      updateRef: ref,
                                                      name: data[index]
                                                          .smsPackName);
                                                },
                                                child: Container(
                                                  height: 40,
                                                  width: 40,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        const BorderRadius.all(
                                                            Radius.circular(
                                                                90)),
                                                    border: Border.all(
                                                        width: 1,
                                                        color:
                                                            Colors.redAccent),
                                                    color: Colors.redAccent
                                                        .withValues(alpha: 0.1),
                                                  ),
                                                  child: const Center(
                                                    child: Icon(
                                                      Icons.delete_forever,
                                                      color: Colors.redAccent,
                                                    ),
                                                  ),
                                                ),
                                              ).visible(
                                                  data[index].smsPackName !=
                                                      'Basic'),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else {
              counter == 0 ? postBasicPlan(ref: ref) : null;
              counter++;
              return Container();
            }
          }, error: (e, stack) {
            return Center(
              child: Text(e.toString()),
            );
          }, loading: () {
            return const Center(
              child: CircularProgressIndicator(),
            );
          });
        },
      ),
    );
  }
}
