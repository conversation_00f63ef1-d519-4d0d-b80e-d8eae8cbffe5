/// نموذج الإشعارات الإدارية البسيط (للتوافق مع النظام القديم)
class AdminNotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? actionUrl;
  final String? imageUrl;
  final Map<String, dynamic> customData;

  AdminNotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.actionUrl,
    this.imageUrl,
    this.customData = const {},
  });

  factory AdminNotificationModel.fromJson(Map<String, dynamic> json) {
    return AdminNotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'info',
      isActive: json['isActive'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      actionUrl: json['actionUrl'],
      imageUrl: json['imageUrl'],
      customData: Map<String, dynamic>.from(json['customData'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'actionUrl': actionUrl,
      'imageUrl': imageUrl,
      'customData': customData,
    };
  }

  AdminNotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? actionUrl,
    String? imageUrl,
    Map<String, dynamic>? customData,
  }) {
    return AdminNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      customData: customData ?? this.customData,
    );
  }

  bool get isEmergency => type == 'emergency';
  bool get isExpired => false; // مبسط للآن

  /// الحصول على لون الإشعار حسب النوع
  String get colorCode {
    switch (type) {
      case 'emergency':
        return '#FF0000';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'success':
        return '#4CAF50';
      case 'info':
        return '#2196F3';
      case 'maintenance':
        return '#9C27B0';
      case 'update':
        return '#00BCD4';
      case 'promotional':
        return '#FF5722';
      default:
        return '#757575';
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  String get iconCode {
    switch (type) {
      case 'emergency':
        return '🚨';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'maintenance':
        return '🔧';
      case 'update':
        return '🔄';
      case 'promotional':
        return '🎉';
      default:
        return '📢';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminNotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج مركز الإرسال المتقدم والشامل
class AdvancedNotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // محتوى متقدم
  final String? actionUrl;
  final String? imageUrl;
  final String? audioUrl;
  final String? videoUrl;
  final Map<String, dynamic> customData;

  // استهداف متقدم
  final NotificationTarget target;
  final List<String> specificUserIds;
  final List<String> specificDeviceIds;
  final List<String> targetRegions;
  final List<String> targetSubscriptionTypes;
  final List<String> customGroups;

  // جدولة متقدمة
  final SchedulingOptions scheduling;

  // إحصائيات
  final NotificationStats stats;

  AdvancedNotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.actionUrl,
    this.imageUrl,
    this.audioUrl,
    this.videoUrl,
    this.customData = const {},
    required this.target,
    this.specificUserIds = const [],
    this.specificDeviceIds = const [],
    this.targetRegions = const [],
    this.targetSubscriptionTypes = const [],
    this.customGroups = const [],
    required this.scheduling,
    required this.stats,
  });

  /// إنشاء نموذج من JSON
  factory AdvancedNotificationModel.fromJson(Map<String, dynamic> json) {
    return AdvancedNotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'info',
      isActive: json['isActive'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      actionUrl: json['actionUrl'],
      imageUrl: json['imageUrl'],
      audioUrl: json['audioUrl'],
      videoUrl: json['videoUrl'],
      customData: Map<String, dynamic>.from(json['customData'] ?? {}),
      target: NotificationTarget.fromJson(json['target'] ?? {}),
      specificUserIds: List<String>.from(json['specificUserIds'] ?? []),
      specificDeviceIds: List<String>.from(json['specificDeviceIds'] ?? []),
      targetRegions: List<String>.from(json['targetRegions'] ?? []),
      targetSubscriptionTypes:
          List<String>.from(json['targetSubscriptionTypes'] ?? []),
      customGroups: List<String>.from(json['customGroups'] ?? []),
      scheduling: SchedulingOptions.fromJson(json['scheduling'] ?? {}),
      stats: NotificationStats.fromJson(json['stats'] ?? {}),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'actionUrl': actionUrl,
      'imageUrl': imageUrl,
      'audioUrl': audioUrl,
      'videoUrl': videoUrl,
      'customData': customData,
      'target': target.toJson(),
      'specificUserIds': specificUserIds,
      'specificDeviceIds': specificDeviceIds,
      'targetRegions': targetRegions,
      'targetSubscriptionTypes': targetSubscriptionTypes,
      'customGroups': customGroups,
      'scheduling': scheduling.toJson(),
      'stats': stats.toJson(),
    };
  }

  /// نسخ النموذج مع تعديل بعض القيم
  AdvancedNotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? actionUrl,
    String? imageUrl,
    String? audioUrl,
    String? videoUrl,
    Map<String, dynamic>? customData,
    NotificationTarget? target,
    List<String>? specificUserIds,
    List<String>? specificDeviceIds,
    List<String>? targetRegions,
    List<String>? targetSubscriptionTypes,
    List<String>? customGroups,
    SchedulingOptions? scheduling,
    NotificationStats? stats,
  }) {
    return AdvancedNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      customData: customData ?? this.customData,
      target: target ?? this.target,
      specificUserIds: specificUserIds ?? this.specificUserIds,
      specificDeviceIds: specificDeviceIds ?? this.specificDeviceIds,
      targetRegions: targetRegions ?? this.targetRegions,
      targetSubscriptionTypes:
          targetSubscriptionTypes ?? this.targetSubscriptionTypes,
      customGroups: customGroups ?? this.customGroups,
      scheduling: scheduling ?? this.scheduling,
      stats: stats ?? this.stats,
    );
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return id.isNotEmpty && title.isNotEmpty && message.isNotEmpty;
  }

  /// التحقق من كون الإشعار طارئ
  bool get isEmergency {
    return type == 'emergency' || customData['emergency'] == true;
  }

  /// التحقق من كون الإشعار ترويجي
  bool get isPromotional {
    return type == 'promotional' || customData['promotional'] == true;
  }

  /// التحقق من انتهاء صلاحية الإشعار
  bool get isExpired {
    if (customData['expiryDate'] != null) {
      final expiryDate = DateTime.parse(customData['expiryDate']);
      return DateTime.now().isAfter(expiryDate);
    }
    return false;
  }

  /// الحصول على أولوية الإشعار
  String get priority {
    if (isEmergency) return 'high';
    if (type == 'warning' || type == 'error') return 'medium';
    return customData['priority'] ?? 'normal';
  }

  /// الحصول على لون الإشعار حسب النوع
  String get colorCode {
    switch (type) {
      case 'emergency':
        return '#FF0000';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'success':
        return '#4CAF50';
      case 'info':
        return '#2196F3';
      case 'maintenance':
        return '#9C27B0';
      case 'update':
        return '#00BCD4';
      case 'promotional':
        return '#FF5722';
      default:
        return '#757575';
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  String get iconCode {
    switch (type) {
      case 'emergency':
        return '🚨';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'maintenance':
        return '🔧';
      case 'update':
        return '🔄';
      case 'promotional':
        return '🎉';
      default:
        return '📢';
    }
  }

  /// تحويل النموذج إلى نص
  @override
  String toString() {
    return 'AdvancedNotificationModel(id: $id, title: $title, type: $type, target: ${target.type})';
  }

  /// مقارنة النماذج
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdvancedNotificationModel && other.id == id;
  }

  /// الحصول على hash code
  @override
  int get hashCode => id.hashCode;
}

/// نموذج استهداف الإشعارات
class NotificationTarget {
  final String
      type; // all, specific_users, specific_devices, regions, subscription_types, custom_groups
  final String description;

  NotificationTarget({
    required this.type,
    required this.description,
  });

  factory NotificationTarget.fromJson(Map<String, dynamic> json) {
    return NotificationTarget(
      type: json['type'] ?? 'all',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'description': description,
    };
  }

  /// أنواع الاستهداف المتاحة
  static const String all = 'all';
  static const String specificUsers = 'specific_users';
  static const String specificDevices = 'specific_devices';
  static const String regions = 'regions';
  static const String subscriptionTypes = 'subscription_types';
  static const String customGroups = 'custom_groups';

  /// الحصول على الوصف بالعربية
  String get arabicDescription {
    switch (type) {
      case all:
        return 'جميع المستخدمين';
      case specificUsers:
        return 'مستخدمين محددين';
      case specificDevices:
        return 'أجهزة محددة';
      case regions:
        return 'مناطق جغرافية';
      case subscriptionTypes:
        return 'أنواع اشتراك';
      case customGroups:
        return 'مجموعات مخصصة';
      default:
        return 'غير محدد';
    }
  }
}

/// نموذج خيارات الجدولة
class SchedulingOptions {
  final String type; // immediate, scheduled, recurring
  final DateTime? scheduledTime;
  final String? recurringPattern; // daily, weekly, monthly
  final DateTime? endDate;
  final bool isActive;

  SchedulingOptions({
    required this.type,
    this.scheduledTime,
    this.recurringPattern,
    this.endDate,
    this.isActive = true,
  });

  factory SchedulingOptions.fromJson(Map<String, dynamic> json) {
    return SchedulingOptions(
      type: json['type'] ?? 'immediate',
      scheduledTime: json['scheduledTime'] != null
          ? DateTime.parse(json['scheduledTime'])
          : null,
      recurringPattern: json['recurringPattern'],
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'recurringPattern': recurringPattern,
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
    };
  }

  /// أنواع الجدولة
  static const String immediate = 'immediate';
  static const String scheduled = 'scheduled';
  static const String recurring = 'recurring';

  /// أنماط التكرار
  static const String daily = 'daily';
  static const String weekly = 'weekly';
  static const String monthly = 'monthly';

  /// الحصول على الوصف بالعربية
  String get arabicDescription {
    switch (type) {
      case immediate:
        return 'إرسال فوري';
      case scheduled:
        return 'إرسال مجدول';
      case recurring:
        return 'إرسال متكرر';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على وصف نمط التكرار
  String get recurringArabicDescription {
    switch (recurringPattern) {
      case daily:
        return 'يومي';
      case weekly:
        return 'أسبوعي';
      case monthly:
        return 'شهري';
      default:
        return 'غير محدد';
    }
  }
}

/// نموذج إحصائيات الإشعار
class NotificationStats {
  final int totalSent;
  final int delivered;
  final int opened;
  final int clicked;
  final int failed;
  final DateTime lastSent;
  final Map<String, int> deviceStats;
  final Map<String, int> regionStats;

  NotificationStats({
    this.totalSent = 0,
    this.delivered = 0,
    this.opened = 0,
    this.clicked = 0,
    this.failed = 0,
    DateTime? lastSent,
    this.deviceStats = const {},
    this.regionStats = const {},
  }) : lastSent = lastSent ?? DateTime.now();

  factory NotificationStats.fromJson(Map<String, dynamic> json) {
    return NotificationStats(
      totalSent: json['totalSent'] ?? 0,
      delivered: json['delivered'] ?? 0,
      opened: json['opened'] ?? 0,
      clicked: json['clicked'] ?? 0,
      failed: json['failed'] ?? 0,
      lastSent: json['lastSent'] != null
          ? DateTime.parse(json['lastSent'])
          : DateTime.now(),
      deviceStats: Map<String, int>.from(json['deviceStats'] ?? {}),
      regionStats: Map<String, int>.from(json['regionStats'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSent': totalSent,
      'delivered': delivered,
      'opened': opened,
      'clicked': clicked,
      'failed': failed,
      'lastSent': lastSent.toIso8601String(),
      'deviceStats': deviceStats,
      'regionStats': regionStats,
    };
  }

  /// معدل التسليم
  double get deliveryRate {
    if (totalSent == 0) return 0.0;
    return (delivered / totalSent) * 100;
  }

  /// معدل الفتح
  double get openRate {
    if (delivered == 0) return 0.0;
    return (opened / delivered) * 100;
  }

  /// معدل النقر
  double get clickRate {
    if (opened == 0) return 0.0;
    return (clicked / opened) * 100;
  }

  /// معدل الفشل
  double get failureRate {
    if (totalSent == 0) return 0.0;
    return (failed / totalSent) * 100;
  }
}

/// أنواع الإشعارات المتقدمة
class AdvancedNotificationTypes {
  static const String info = 'info';
  static const String warning = 'warning';
  static const String error = 'error';
  static const String success = 'success';
  static const String emergency = 'emergency';
  static const String maintenance = 'maintenance';
  static const String update = 'update';
  static const String promotional = 'promotional';
  static const String survey = 'survey';
  static const String announcement = 'announcement';

  static const List<String> all = [
    info,
    warning,
    error,
    success,
    emergency,
    maintenance,
    update,
    promotional,
    survey,
    announcement,
  ];

  /// الحصول على اسم النوع بالعربية
  static String getArabicName(String type) {
    switch (type) {
      case info:
        return 'معلومات';
      case warning:
        return 'تحذير';
      case error:
        return 'خطأ';
      case success:
        return 'نجاح';
      case emergency:
        return 'طوارئ';
      case maintenance:
        return 'صيانة';
      case update:
        return 'تحديث';
      case promotional:
        return 'ترويجي';
      case survey:
        return 'استطلاع';
      case announcement:
        return 'إعلان';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على وصف النوع
  static String getDescription(String type) {
    switch (type) {
      case info:
        return 'إشعار معلوماتي عام';
      case warning:
        return 'تحذير مهم للمستخدمين';
      case error:
        return 'إشعار خطأ أو مشكلة';
      case success:
        return 'إشعار نجاح عملية';
      case emergency:
        return 'إشعار طوارئ عاجل';
      case maintenance:
        return 'إشعار صيانة مجدولة';
      case update:
        return 'إشعار تحديث التطبيق';
      case promotional:
        return 'إشعار ترويجي أو عرض';
      case survey:
        return 'استطلاع رأي المستخدمين';
      case announcement:
        return 'إعلان مهم';
      default:
        return 'نوع غير محدد';
    }
  }
}
