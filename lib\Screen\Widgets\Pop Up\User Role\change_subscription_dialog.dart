import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../Constant Data/constant.dart';
import '../../../../model/seller_info_model.dart';
import '../../../../model/subscription_plan_model.dart';
import '../../../../model/subscription_model.dart';
import '../../../../Provider/subacription_plan_provider.dart';
import '../../../../Provider/seller_info_provider.dart';

class ChangeSubscriptionDialog extends ConsumerStatefulWidget {
  final SellerInfoModel seller;

  const ChangeSubscriptionDialog({
    super.key,
    required this.seller,
  });

  @override
  ConsumerState<ChangeSubscriptionDialog> createState() => _ChangeSubscriptionDialogState();
}

class _ChangeSubscriptionDialogState extends ConsumerState<ChangeSubscriptionDialog> {
  SubscriptionPlanModel? _selectedPlan;
  int _durationMonths = 1;
  final _noteController = TextEditingController();

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: kMainColor.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    HugeIcons.strokeRoundedLoyaltyCard,
                    color: kMainColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'تغيير باقة الاشتراك',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: kMainColor,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: Colors.grey.shade600,
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // User Info
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: kMainColor.withValues(alpha: 0.1),
                            child: Text(
                              (widget.seller.companyName?.isNotEmpty == true)
                                ? widget.seller.companyName![0].toUpperCase()
                                : 'U',
                              style: TextStyle(
                                color: kMainColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.seller.companyName ?? 'غير محدد',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'الباقة الحالية: ${widget.seller.subscriptionName ?? 'غير محدد'}',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Plan Selection
                    Text(
                      'اختر الباقة الجديدة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),

                    Consumer(
                      builder: (context, ref, child) {
                        final plansAsync = ref.watch(subscriptionPlanProvider);

                        return plansAsync.when(
                          data: (plans) {
                            return Column(
                              children: plans.map((plan) {
                                final isSelected = _selectedPlan == plan;
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedPlan = plan;
                                    });
                                  },
                                  child: Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: isSelected ? kMainColor : Colors.grey.shade300,
                                        width: isSelected ? 2 : 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                      color: isSelected ? kMainColor.withValues(alpha: 0.05) : null,
                                    ),
                                    child: Row(
                                      children: [
                                        Radio<SubscriptionPlanModel>(
                                          value: plan,
                                          groupValue: _selectedPlan,
                                          onChanged: (value) {
                                            setState(() {
                                              _selectedPlan = value;
                                            });
                                          },
                                          activeColor: kMainColor,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                plan.subscriptionName,
                                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                  color: isSelected ? kMainColor : null,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                'المدة: ${plan.duration} يوم | السعر: ${plan.subscriptionPrice} جنيه',
                                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                            );
                          },
                          loading: () => const Center(child: CircularProgressIndicator()),
                          error: (error, stack) => Text('خطأ في تحميل الباقات: $error'),
                        );
                      },
                    ),

                    const SizedBox(height: 24),

                    // Duration Selection
                    Text(
                      'مدة الاشتراك (بالأشهر)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [1, 3, 6, 12].map((months) {
                        final isSelected = _durationMonths == months;
                        return Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _durationMonths = months;
                              });
                            },
                            child: Container(
                              margin: const EdgeInsets.only(left: 8),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                color: isSelected ? kMainColor : Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isSelected ? kMainColor : Colors.grey.shade300,
                                ),
                              ),
                              child: Text(
                                '$months ${months == 1 ? 'شهر' : 'أشهر'}',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: isSelected ? Colors.white : Colors.grey.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 24),

                    // Notes
                    TextField(
                      controller: _noteController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أضف أي ملاحظات حول تغيير الباقة',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade100,
                        foregroundColor: Colors.grey.shade700,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedPlan != null ? _changeSubscription : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        elevation: 2,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('تغيير الباقة'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _changeSubscription() async {
    if (_selectedPlan == null) return;

    try {
      EasyLoading.show(status: 'جاري تغيير الباقة...');

      // Calculate new subscription end date
      final now = DateTime.now();

      // Create new subscription model
      final subscription = SubscriptionModel(
        subscriptionName: _selectedPlan!.subscriptionName,
        subscriptionDate: now.toString(),
        saleNumber: _selectedPlan!.saleNumber,
        purchaseNumber: _selectedPlan!.purchaseNumber,
        partiesNumber: _selectedPlan!.partiesNumber,
        dueNumber: _selectedPlan!.dueNumber,
        duration: _selectedPlan!.duration * _durationMonths,
        products: _selectedPlan!.products,
      );

      // Update user subscription in Firebase
      if (widget.seller.userID != null) {
        final subscriptionRef = FirebaseDatabase.instance
            .ref()
            .child(widget.seller.userID!)
            .child('Subscription');

        await subscriptionRef.set(subscription.toJson());

        // Update seller info
        final sellerRef = FirebaseDatabase.instance
            .ref()
            .child('Admin Panel')
            .child('Seller List');

        // Find and update seller record
        final snapshot = await sellerRef.orderByChild('userId').equalTo(widget.seller.userID).get();
        if (snapshot.exists) {
          for (var child in snapshot.children) {
            await child.ref.update({
              'subscriptionName': _selectedPlan!.subscriptionName,
              'subscriptionDate': now.toString(),
            });
          }
        }

        EasyLoading.showSuccess('تم تغيير الباقة بنجاح!');

        // Refresh providers
        ref.invalidate(sellerInfoProvider);

        if (mounted) {
          Navigator.pop(context);
        }
      } else {
        EasyLoading.showError('خطأ: معرف المستخدم غير موجود');
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showError('فشل في تغيير الباقة: ${e.toString()}');
    }
  }
}
