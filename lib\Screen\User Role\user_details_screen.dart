import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hugeicons/hugeicons.dart';
import '../Widgets/Constant Data/constant.dart';
import '../../Provider/seller_info_provider.dart';
import '../../Provider/user_role_provider.dart';
import '../../model/seller_info_model.dart';
import '../../model/user_role_model.dart';

class UserDetailsScreen extends StatefulWidget {
  const UserDetailsScreen({super.key});

  static const String route = '/user_details';

  @override
  State<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends State<UserDetailsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, active, inactive

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: kWhiteTextColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: kMainColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10.0),
                    topRight: Radius.circular(10.0),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedUserMultiple,
                      color: kMainColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'تفاصيل المستخدمين',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: kMainColor,
                      ),
                    ),
                    const Spacer(),
                    // Filter Dropdown
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        underline: const SizedBox(),
                        onChanged: (value) {
                          setState(() {
                            _selectedFilter = value!;
                          });
                        },
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستخدمين')),
                          DropdownMenuItem(value: 'active', child: Text('المستخدمين النشطين')),
                          DropdownMenuItem(value: 'inactive', child: Text('المستخدمين غير النشطين')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Search Bar
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'البحث في المستخدمين...',
                    prefixIcon: const Icon(HugeIcons.strokeRoundedSearch01),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: kMainColor),
                    ),
                  ),
                ),
              ),

              // Users List
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final sellersAsync = ref.watch(sellerInfoProvider);
                    final userRolesAsync = ref.watch(allAdminUserRoleProvider);

                    return sellersAsync.when(
                      data: (sellers) {
                        print('Loaded ${sellers.length} sellers'); // Debug
                        return userRolesAsync.when(
                          data: (userRoles) {
                            print('Loaded ${userRoles.length} user roles'); // Debug
                            final filteredSellers = sellers.where((seller) {
                              final matchesSearch = (seller.companyName?.toLowerCase() ?? '').contains(_searchQuery) ||
                                                   (seller.phoneNumber?.toLowerCase() ?? '').contains(_searchQuery) ||
                                                   (seller.email?.toLowerCase() ?? '').contains(_searchQuery);

                              if (!matchesSearch) return false;

                              // Apply filter
                              if (_selectedFilter == 'all') return true;

                              final hasRoles = userRoles.any((role) => role.databaseId == seller.userID);
                              return _selectedFilter == 'active' ? hasRoles : !hasRoles;
                            }).toList();

                            if (filteredSellers.isEmpty) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      HugeIcons.strokeRoundedSearchRemove,
                                      size: 64,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      _searchQuery.isEmpty
                                        ? 'لا توجد مستخدمين'
                                        : 'لا توجد نتائج للبحث',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'إجمالي المستخدمين: ${sellers.length}\nأدوار المستخدمين: ${userRoles.length}\nالفلتر: $_selectedFilter',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.grey.shade500,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              );
                            }

                            return ListView.builder(
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              itemCount: filteredSellers.length,
                              itemBuilder: (context, index) {
                                final seller = filteredSellers[index];
                                final userRole = userRoles.firstWhere(
                                  (role) => role.databaseId == seller.userID,
                                  orElse: () => UserRoleModel(
                                    email: seller.email ?? '',
                                    userTitle: 'بدون دور',
                                    databaseId: seller.userID ?? '',
                                    salePermission: false,
                                    partiesPermission: false,
                                    purchasePermission: false,
                                    productPermission: false,
                                    profileEditPermission: false,
                                    addExpensePermission: false,
                                    lossProfitPermission: false,
                                    dueListPermission: false,
                                    stockPermission: false,
                                    reportsPermission: false,
                                    salesListPermission: false,
                                    purchaseListPermission: false,
                                  ),
                                );
                                return _buildUserCard(context, seller, userRole, ref);
                              },
                            );
                          },
                          loading: () => const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(),
                                SizedBox(height: 16),
                                Text('جاري تحميل أدوار المستخدمين...'),
                              ],
                            ),
                          ),
                          error: (error, stack) => Center(
                            child: Text('خطأ في تحميل أدوار المستخدمين: $error'),
                          ),
                        );
                      },
                      loading: () => const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('جاري تحميل بيانات المستخدمين...'),
                          ],
                        ),
                      ),
                      error: (error, stack) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              HugeIcons.strokeRoundedAlert02,
                              size: 64,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'حدث خطأ في تحميل البيانات',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.red.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              error.toString(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserCard(BuildContext context, SellerInfoModel seller, UserRoleModel userRole, WidgetRef ref) {
    final hasRole = userRole.userTitle != 'بدون دور';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: hasRole ? kMainColor.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                  child: Text(
                    (seller.companyName?.isNotEmpty == true) ? seller.companyName![0].toUpperCase() : 'U',
                    style: TextStyle(
                      color: hasRole ? kMainColor : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        seller.companyName ?? 'غير محدد',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        seller.email ?? 'غير محدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        seller.phoneNumber?.toString() ?? 'غير محدد',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: hasRole ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    hasRole ? 'نشط' : 'غير نشط',
                    style: TextStyle(
                      color: hasRole ? Colors.green : Colors.orange,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(context, value, seller, userRole, ref),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedView),
                          SizedBox(width: 8),
                          Text('عرض التفاصيل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'assign_role',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedUserSettings01),
                          SizedBox(width: 8),
                          Text('تعيين دور'),
                        ],
                      ),
                    ),
                    if (hasRole)
                      const PopupMenuItem(
                        value: 'edit_permissions',
                        child: Row(
                          children: [
                            Icon(HugeIcons.strokeRoundedSettings02),
                            SizedBox(width: 8),
                            Text('تعديل الصلاحيات'),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // User Info
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('الدور الحالي', userRole.userTitle),
                ),
                Expanded(
                  child: _buildInfoItem('تاريخ التسجيل', seller.subscriptionDate ?? 'غير محدد'),
                ),
              ],
            ),

            if (hasRole) ...[
              const SizedBox(height: 12),
              Text(
                'الصلاحيات النشطة:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: _getActivePermissions(userRole).map((permission) {
                  return Chip(
                    label: Text(
                      permission,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: kMainColor.withValues(alpha: 0.1),
                    labelStyle: TextStyle(color: kMainColor),
                    side: BorderSide.none,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  List<String> _getActivePermissions(UserRoleModel role) {
    List<String> permissions = [];

    if (role.salePermission) permissions.add('المبيعات');
    if (role.partiesPermission) permissions.add('العملاء');
    if (role.purchasePermission) permissions.add('المشتريات');
    if (role.productPermission) permissions.add('المنتجات');
    if (role.profileEditPermission) permissions.add('تعديل الملف الشخصي');
    if (role.addExpensePermission) permissions.add('إضافة المصروفات');
    if (role.lossProfitPermission) permissions.add('الأرباح والخسائر');
    if (role.dueListPermission) permissions.add('قائمة المستحقات');
    if (role.stockPermission) permissions.add('المخزون');
    if (role.reportsPermission) permissions.add('التقارير');
    if (role.salesListPermission) permissions.add('قائمة المبيعات');
    if (role.purchaseListPermission) permissions.add('قائمة المشتريات');

    return permissions;
  }

  void _handleMenuAction(BuildContext context, String action, SellerInfoModel seller, UserRoleModel userRole, WidgetRef ref) {
    switch (action) {
      case 'view':
        _showUserDetailsDialog(context, seller, userRole);
        break;
      case 'assign_role':
        _showAssignRoleDialog(context, seller);
        break;
      case 'edit_permissions':
        _showEditPermissionsDialog(context, userRole);
        break;
    }
  }

  void _showUserDetailsDialog(BuildContext context, SellerInfoModel seller, UserRoleModel userRole) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل: ${seller.companyName ?? 'غير محدد'}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailItem('الشركة', seller.companyName ?? 'غير محدد'),
              _buildDetailItem('البريد الإلكتروني', seller.email ?? 'غير محدد'),
              _buildDetailItem('رقم الهاتف', seller.phoneNumber ?? 'غير محدد'),
              _buildDetailItem('الدور', userRole.userTitle),
              _buildDetailItem('تاريخ التسجيل', seller.subscriptionDate ?? 'غير محدد'),
              _buildDetailItem('الباقة', seller.subscriptionName ?? 'غير محدد'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showAssignRoleDialog(BuildContext context, SellerInfoModel seller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعيين دور'),
        content: Text('تعيين دور جديد للمستخدم: ${seller.companyName ?? 'غير محدد'}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('ميزة تعيين الأدوار ستكون متاحة قريباً')),
              );
            },
            child: const Text('تعيين'),
          ),
        ],
      ),
    );
  }

  void _showEditPermissionsDialog(BuildContext context, UserRoleModel userRole) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل صلاحيات: ${userRole.userTitle}'),
        content: const Text('ميزة تعديل الصلاحيات ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
