import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../services/file_upload_service.dart';

import '../../services/image_compression_service.dart';
import '../Widgets/Constant Data/constant.dart';

/// لوحة تحكم خدمات الرفع والإحصائيات
class UploadServicesDashboard extends ConsumerStatefulWidget {
  static const String route = '/upload-services';
  const UploadServicesDashboard({super.key});

  @override
  ConsumerState<UploadServicesDashboard> createState() =>
      _UploadServicesDashboardState();
}

class _UploadServicesDashboardState
    extends ConsumerState<UploadServicesDashboard> {
  Map<String, bool> _servicesStatus = {};
  bool _isCheckingStatus = false;

  @override
  void initState() {
    super.initState();
    _checkServicesStatus();
  }

  Future<void> _checkServicesStatus() async {
    setState(() {
      _isCheckingStatus = true;
    });

    try {
      final status = await FileUploadService.checkServicesStatus();
      setState(() {
        _servicesStatus = status;
      });
    } catch (e) {
      debugPrint('خطأ في فحص حالة الخدمات: $e');
    } finally {
      setState(() {
        _isCheckingStatus = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child:
                  const Icon(Icons.cloud_upload, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مركز خدمات الرفع',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'إحصائيات وإدارة خدمات رفع الملفات',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: _isCheckingStatus
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.refresh, color: Colors.white),
              onPressed: _isCheckingStatus ? null : _checkServicesStatus,
              tooltip: 'فحص حالة الخدمات',
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.clear_all, color: Colors.white),
              onPressed: _resetAllStats,
              tooltip: 'إعادة تعيين الإحصائيات',
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildServicesStatusSection(),
              const SizedBox(height: 24),
              _buildStatsSection(),
              const SizedBox(height: 24),
              _buildCompressionStatsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServicesStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حالة الخدمات',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildServiceStatusCard(
                'Imgur',
                'خدمة الصور السريعة',
                Icons.image,
                Colors.green,
                _servicesStatus['imgur'] ?? false,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildServiceStatusCard(
                'GoFile',
                'خدمة الملفات الكبيرة',
                Icons.folder,
                Colors.blue,
                _servicesStatus['gofile'] ?? false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildServiceStatusCard(
    String serviceName,
    String description,
    IconData icon,
    Color color,
    bool isOnline,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  serviceName,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: isOnline ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isOnline ? 'متصل' : 'غير متصل',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isOnline ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final allStats = FileUploadService.getAllStats();
    final imageStats = allStats['images'] as Map<String, dynamic>;
    final gofileStats = allStats['gofile'] as Map<String, dynamic>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات الرفع',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),

        // إحصائيات الصور
        _buildServiceStatsCard(
          'إحصائيات رفع الصور',
          imageStats,
          Colors.green,
          'GB',
        ),

        const SizedBox(height: 16),

        // إحصائيات GoFile
        _buildServiceStatsCard(
          'إحصائيات GoFile العامة',
          gofileStats,
          Colors.blue,
          'GB',
        ),
      ],
    );
  }

  Widget _buildServiceStatsCard(
    String title,
    Map<String, dynamic> stats,
    Color color,
    String sizeUnit,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي الرفعات',
                  '${stats['totalUploads']}',
                  Icons.upload,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'نجح',
                  '${stats['successfulUploads']}',
                  Icons.check_circle,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'فشل',
                  '${stats['failedUploads']}',
                  Icons.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'معدل النجاح',
                  '${stats['successRate'].toStringAsFixed(1)}%',
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'إجمالي الحجم',
                  '${stats['totalSize$sizeUnit'].toStringAsFixed(2)} $sizeUnit',
                  Icons.storage,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCompressionStatsSection() {
    final compressionStats = CompressionStats.getStats();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات ضغط الصور',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'عدد الضغطات',
                  '${compressionStats['totalCompressions']}',
                  Icons.compress,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'توفير البيانات',
                  '${compressionStats['savedSizeMB'].toStringAsFixed(2)} MB',
                  Icons.save,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'نسبة الضغط',
                  '${compressionStats['compressionRatio'].toStringAsFixed(1)}%',
                  Icons.trending_down,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _resetAllStats() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد إعادة التعيين', style: GoogleFonts.cairo()),
        content: Text(
          'هل أنت متأكد من إعادة تعيين جميع الإحصائيات؟ لن يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () {
              FileUploadService.resetAllStats();
              CompressionStats.reset();
              Navigator.pop(context);
              setState(() {}); // تحديث الواجهة
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين جميع الإحصائيات'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
