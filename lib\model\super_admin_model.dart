import 'advanced_admin_permissions_model.dart';

/// نموذج الأدمن الشامل (Super Admin)
/// يجمع بين الصلاحيات الأساسية والمتقدمة للتحكم الكامل في النظام
class SuperAdminModel {
  late String email, userTitle, databaseId;
  String? profilePicture;
  late bool isActive;
  String? userKey;

  // ========== الصلاحيات الأساسية ==========
  late bool salePermission;
  late bool partiesPermission;
  late bool purchasePermission;
  late bool productPermission;
  late bool profileEditPermission;
  late bool addExpensePermission;
  late bool lossProfitPermission;
  late bool dueListPermission;
  late bool stockPermission;
  late bool reportsPermission;
  late bool salesListPermission;
  late bool purchaseListPermission;

  // ========== الصلاحيات الحديثة الموجودة ==========
  late bool aiChatPermission;
  late bool aiAssistantPermission;
  late bool voiceAssistantPermission;
  late bool treasuryPermission;
  late bool cashBoxPermission;
  late bool deliveryManagementPermission;
  late bool hrmPermission;
  late bool employeesPermission;
  late bool designationPermission;
  late bool salariesPermission;
  late bool financialReportsPermission;
  late bool inventoryManagementPermission;
  late bool customerManagementPermission;
  late bool supplierManagementPermission;
  late bool expenseManagementPermission;
  late bool incomeManagementPermission;
  late bool bankingPermission;
  late bool loanManagementPermission;
  late bool assetManagementPermission;
  late bool budgetManagementPermission;
  late bool taxManagementPermission;
  late bool auditPermission;
  late bool compliancePermission;
  late bool riskManagementPermission;
  late bool projectManagementPermission;
  late bool taskManagementPermission;
  late bool timeTrackingPermission;
  late bool documentManagementPermission;
  late bool communicationPermission;
  late bool marketingPermission;
  late bool crmPermission;
  late bool analyticsPermission;
  late bool dashboardPermission;
  late bool settingsPermission;
  late bool backupPermission;
  late bool securityPermission;
  late bool integrationPermission;
  late bool apiPermission;
  late bool mobileAppPermission;
  late bool webAppPermission;
  late bool desktopAppPermission;
  late bool multiLocationPermission;
  late bool franchisePermission;
  late bool brandingPermission;
  late bool customizationPermission;
  late bool themePermission;
  late bool languagePermission;
  late bool currencyPermission;
  late bool timezonePermission;
  late bool notificationPermission;
  late bool emailPermission;
  late bool smsPermission;
  late bool pushNotificationPermission;
  late bool socialMediaPermission;
  late bool ecommercePermission;
  late bool posPermission;
  late bool onlineStorePermission;
  late bool mobileStorePermission;
  late bool wholesalePermission;
  late bool retailPermission;
  late bool distributionPermission;
  late bool manufacturingPermission;
  late bool servicePermission;
  late bool subscriptionPermission;
  late bool membershipPermission;
  late bool loyaltyPermission;
  late bool discountPermission;
  late bool couponPermission;
  late bool promotionPermission;
  late bool campaignPermission;
  late bool surveyPermission;
  late bool feedbackPermission;
  late bool reviewPermission;
  late bool ratingPermission;
  late bool testimonialPermission;
  late bool caseStudyPermission;
  late bool blogPermission;
  late bool newsPermission;
  late bool eventPermission;
  late bool webinarPermission;
  late bool trainingPermission;
  late bool certificationPermission;
  late bool knowledgeBasePermission;
  late bool faqPermission;
  late bool helpDeskPermission;
  late bool ticketPermission;
  late bool chatPermission;
  late bool videoCallPermission;
  late bool screenSharingPermission;
  late bool remoteAccessPermission;
  late bool fileManagementPermission;
  late bool cloudStoragePermission;
  late bool dataImportPermission;
  late bool dataExportPermission;
  late bool dataBackupPermission;
  late bool dataRestorePermission;
  late bool dataMigrationPermission;
  late bool dataCleaningPermission;
  late bool dataValidationPermission;
  late bool dataAnalysisPermission;
  late bool dataVisualizationPermission;
  late bool reportingPermission;
  late bool forecastingPermission;
  late bool planningPermission;
  late bool budgetingPermission;
  late bool schedulingPermission;
  late bool calendarPermission;
  late bool reminderPermission;
  late bool alertPermission;
  late bool warningPermission;
  late bool errorHandlingPermission;
  late bool debuggingPermission;
  late bool testingPermission;
  late bool qualityAssurancePermission;
  late bool performanceMonitoringPermission;
  late bool systemHealthPermission;
  late bool uptimeMonitoringPermission;
  late bool loadBalancingPermission;
  late bool scalingPermission;
  late bool optimizationPermission;
  late bool cacheManagementPermission;
  late bool databaseOptimizationPermission;
  late bool queryOptimizationPermission;
  late bool indexManagementPermission;
  late bool partitioningPermission;
  late bool replicationPermission;
  late bool clusteringPermission;
  late bool shardingPermission;
  late bool loadTestingPermission;
  late bool stressTestingPermission;
  late bool securityTestingPermission;
  late bool penetrationTestingPermission;
  late bool vulnerabilityAssessmentPermission;
  late bool complianceAuditPermission;
  late bool riskAssessmentPermission;
  late bool incidentManagementPermission;
  late bool disasterRecoveryPermission;
  late bool businessContinuityPermission;
  late bool changeManagementPermission;
  late bool versionControlPermission;
  late bool releaseManagementPermission;
  late bool deploymentPermission;
  late bool rollbackPermission;
  late bool hotfixPermission;
  late bool patchManagementPermission;
  late bool updateManagementPermission;
  late bool licenseManagementPermission;
  late bool assetTrackingPermission;
  late bool inventoryTrackingPermission;
  late bool locationTrackingPermission;
  late bool gpsTrackingPermission;
  late bool geofencingPermission;
  late bool routeOptimizationPermission;
  late bool deliveryTrackingPermission;
  late bool shippingPermission;
  late bool logisticsPermission;
  late bool warehousePermission;
  late bool stockManagementPermission;
  late bool orderManagementPermission;
  late bool fulfillmentPermission;
  late bool returnManagementPermission;
  late bool refundPermission;
  late bool exchangePermission;
  late bool warrantyPermission;
  late bool serviceContractPermission;
  late bool maintenancePermission;
  late bool repairPermission;
  late bool replacementPermission;
  late bool upgradePermission;
  late bool migrationPermission;
  late bool conversionPermission;
  late bool transformationPermission;
  late bool automationPermission;
  late bool workflowPermission;
  late bool processManagementPermission;
  late bool procedureManagementPermission;
  late bool policyManagementPermission;
  late bool standardsManagementPermission;
  late bool guidelinesManagementPermission;
  late bool bestPracticesPermission;
  late bool knowledgeManagementPermission;
  late bool learningManagementPermission;
  late bool skillManagementPermission;
  late bool competencyManagementPermission;
  late bool performanceManagementPermission;
  late bool goalManagementPermission;
  late bool objectiveManagementPermission;
  late bool kpiManagementPermission;
  late bool metricManagementPermission;
  late bool benchmarkingPermission;
  late bool comparisonPermission;
  late bool competitiveAnalysisPermission;
  late bool marketAnalysisPermission;
  late bool trendAnalysisPermission;
  late bool sentimentAnalysisPermission;
  late bool behaviorAnalysisPermission;
  late bool usageAnalysisPermission;
  late bool patternAnalysisPermission;
  late bool anomalyDetectionPermission;
  late bool fraudDetectionPermission;
  late bool riskDetectionPermission;
  late bool threatDetectionPermission;
  late bool intrusionDetectionPermission;
  late bool malwareDetectionPermission;
  late bool virusDetectionPermission;
  late bool spamDetectionPermission;
  late bool phishingDetectionPermission;
  late bool socialEngineeringDetectionPermission;
  late bool dataLeakageDetectionPermission;
  late bool privacyProtectionPermission;
  late bool gdprCompliancePermission;
  late bool hipaaCompliancePermission;
  late bool soxCompliancePermission;
  late bool pciCompliancePermission;
  late bool iso27001CompliancePermission;
  late bool nistCompliancePermission;
  late bool cobiTCompliancePermission;
  late bool itilCompliancePermission;
  late bool agileCompliancePermission;
  late bool devopsCompliancePermission;
  late bool cloudCompliancePermission;
  late bool containerCompliancePermission;
  late bool microservicesCompliancePermission;
  late bool apiCompliancePermission;
  late bool webserviceCompliancePermission;
  late bool restCompliancePermission;
  late bool soapCompliancePermission;
  late bool graphqlCompliancePermission;
  late bool grpcCompliancePermission;
  late bool websocketCompliancePermission;
  late bool mqttCompliancePermission;
  late bool amqpCompliancePermission;
  late bool kafkaCompliancePermission;
  late bool redisCompliancePermission;
  late bool elasticsearchCompliancePermission;
  late bool mongodbCompliancePermission;
  late bool postgresqlCompliancePermission;
  late bool mysqlCompliancePermission;
  late bool oracleCompliancePermission;
  late bool sqlServerCompliancePermission;
  late bool db2CompliancePermission;
  late bool cassandraCompliancePermission;
  late bool dynamodbCompliancePermission;
  late bool cosmosdbCompliancePermission;
  late bool firebaseCompliancePermission;
  late bool supabaseCompliancePermission;
  late bool awsCompliancePermission;
  late bool azureCompliancePermission;
  late bool gcpCompliancePermission;
  late bool alibabaCloudCompliancePermission;
  late bool ibmCloudCompliancePermission;
  late bool oracleCloudCompliancePermission;
  late bool digitalOceanCompliancePermission;
  late bool linodeCompliancePermission;

  // الصلاحيات المتقدمة
  late AdvancedAdminPermissionsModel advancedPermissions;

  SuperAdminModel({
    required this.email,
    required this.userTitle,
    required this.databaseId,
    this.profilePicture,
    this.isActive = true,
    this.userKey,

    // الصلاحيات الأساسية
    this.salePermission = false,
    this.partiesPermission = false,
    this.purchasePermission = false,
    this.productPermission = false,
    this.profileEditPermission = false,
    this.addExpensePermission = false,
    this.lossProfitPermission = false,
    this.dueListPermission = false,
    this.stockPermission = false,
    this.reportsPermission = false,
    this.salesListPermission = false,
    this.purchaseListPermission = false,

    // الصلاحيات الحديثة
    this.aiChatPermission = false,
    this.aiAssistantPermission = false,
    this.voiceAssistantPermission = false,
    this.treasuryPermission = false,
    this.cashBoxPermission = false,
    this.deliveryManagementPermission = false,
    this.hrmPermission = false,
    this.employeesPermission = false,
    this.designationPermission = false,
    this.salariesPermission = false,
    this.financialReportsPermission = false,
    this.inventoryManagementPermission = false,
    this.customerManagementPermission = false,
    this.supplierManagementPermission = false,
    this.expenseManagementPermission = false,
    this.incomeManagementPermission = false,
    this.bankingPermission = false,
    this.loanManagementPermission = false,
    this.assetManagementPermission = false,
    this.budgetManagementPermission = false,
    this.taxManagementPermission = false,
    this.auditPermission = false,
    this.compliancePermission = false,
    this.riskManagementPermission = false,
    this.projectManagementPermission = false,
    this.taskManagementPermission = false,
    this.timeTrackingPermission = false,
    this.documentManagementPermission = false,
    this.communicationPermission = false,
    this.marketingPermission = false,
    this.crmPermission = false,
    this.analyticsPermission = false,
    this.dashboardPermission = false,
    this.settingsPermission = false,
    this.backupPermission = false,
    this.securityPermission = false,
    this.integrationPermission = false,
    this.apiPermission = false,
    this.mobileAppPermission = false,
    this.webAppPermission = false,
    this.desktopAppPermission = false,
    this.multiLocationPermission = false,
    this.franchisePermission = false,
    this.brandingPermission = false,
    this.customizationPermission = false,
    this.themePermission = false,
    this.languagePermission = false,
    this.currencyPermission = false,
    this.timezonePermission = false,
    this.notificationPermission = false,
    this.emailPermission = false,
    this.smsPermission = false,
    this.pushNotificationPermission = false,
    this.socialMediaPermission = false,
    this.ecommercePermission = false,
    this.posPermission = false,
    this.onlineStorePermission = false,
    this.mobileStorePermission = false,
    this.wholesalePermission = false,
    this.retailPermission = false,
    this.distributionPermission = false,
    this.manufacturingPermission = false,
    this.servicePermission = false,
    this.subscriptionPermission = false,
    this.membershipPermission = false,
    this.loyaltyPermission = false,
    this.discountPermission = false,
    this.couponPermission = false,
    this.promotionPermission = false,
    this.campaignPermission = false,
    this.surveyPermission = false,
    this.feedbackPermission = false,
    this.reviewPermission = false,
    this.ratingPermission = false,
    this.testimonialPermission = false,
    this.caseStudyPermission = false,
    this.blogPermission = false,
    this.newsPermission = false,
    this.eventPermission = false,
    this.webinarPermission = false,
    this.trainingPermission = false,
    this.certificationPermission = false,
    this.knowledgeBasePermission = false,
    this.faqPermission = false,
    this.helpDeskPermission = false,
    this.ticketPermission = false,
    this.chatPermission = false,
    this.videoCallPermission = false,
    this.screenSharingPermission = false,
    this.remoteAccessPermission = false,
    this.fileManagementPermission = false,
    this.cloudStoragePermission = false,
    this.dataImportPermission = false,
    this.dataExportPermission = false,
    this.dataBackupPermission = false,
    this.dataRestorePermission = false,
    this.dataMigrationPermission = false,
    this.dataCleaningPermission = false,
    this.dataValidationPermission = false,
    this.dataAnalysisPermission = false,
    this.dataVisualizationPermission = false,
    this.reportingPermission = false,
    this.forecastingPermission = false,
    this.planningPermission = false,
    this.budgetingPermission = false,
    this.schedulingPermission = false,
    this.calendarPermission = false,
    this.reminderPermission = false,
    this.alertPermission = false,
    this.warningPermission = false,
    this.errorHandlingPermission = false,
    this.debuggingPermission = false,
    this.testingPermission = false,
    this.qualityAssurancePermission = false,
    this.performanceMonitoringPermission = false,
    this.systemHealthPermission = false,
    this.uptimeMonitoringPermission = false,
    this.loadBalancingPermission = false,
    this.scalingPermission = false,
    this.optimizationPermission = false,
    this.cacheManagementPermission = false,
    this.databaseOptimizationPermission = false,
    this.queryOptimizationPermission = false,
    this.indexManagementPermission = false,
    this.partitioningPermission = false,
    this.replicationPermission = false,
    this.clusteringPermission = false,
    this.shardingPermission = false,
    this.loadTestingPermission = false,
    this.stressTestingPermission = false,
    this.securityTestingPermission = false,
    this.penetrationTestingPermission = false,
    this.vulnerabilityAssessmentPermission = false,
    this.complianceAuditPermission = false,
    this.riskAssessmentPermission = false,
    this.incidentManagementPermission = false,
    this.disasterRecoveryPermission = false,
    this.businessContinuityPermission = false,
    this.changeManagementPermission = false,
    this.versionControlPermission = false,
    this.releaseManagementPermission = false,
    this.deploymentPermission = false,
    this.rollbackPermission = false,
    this.hotfixPermission = false,
    this.patchManagementPermission = false,
    this.updateManagementPermission = false,
    this.licenseManagementPermission = false,
    this.assetTrackingPermission = false,
    this.inventoryTrackingPermission = false,
    this.locationTrackingPermission = false,
    this.gpsTrackingPermission = false,
    this.geofencingPermission = false,
    this.routeOptimizationPermission = false,
    this.deliveryTrackingPermission = false,
    this.shippingPermission = false,
    this.logisticsPermission = false,
    this.warehousePermission = false,
    this.stockManagementPermission = false,
    this.orderManagementPermission = false,
    this.fulfillmentPermission = false,
    this.returnManagementPermission = false,
    this.refundPermission = false,
    this.exchangePermission = false,
    this.warrantyPermission = false,
    this.serviceContractPermission = false,
    this.maintenancePermission = false,
    this.repairPermission = false,
    this.replacementPermission = false,
    this.upgradePermission = false,
    this.migrationPermission = false,
    this.conversionPermission = false,
    this.transformationPermission = false,
    this.automationPermission = false,
    this.workflowPermission = false,
    this.processManagementPermission = false,
    this.procedureManagementPermission = false,
    this.policyManagementPermission = false,
    this.standardsManagementPermission = false,
    this.guidelinesManagementPermission = false,
    this.bestPracticesPermission = false,
    this.knowledgeManagementPermission = false,
    this.learningManagementPermission = false,
    this.skillManagementPermission = false,
    this.competencyManagementPermission = false,
    this.performanceManagementPermission = false,
    this.goalManagementPermission = false,
    this.objectiveManagementPermission = false,
    this.kpiManagementPermission = false,
    this.metricManagementPermission = false,
    this.benchmarkingPermission = false,
    this.comparisonPermission = false,
    this.competitiveAnalysisPermission = false,
    this.marketAnalysisPermission = false,
    this.trendAnalysisPermission = false,
    this.sentimentAnalysisPermission = false,
    this.behaviorAnalysisPermission = false,
    this.usageAnalysisPermission = false,
    this.patternAnalysisPermission = false,
    this.anomalyDetectionPermission = false,
    this.fraudDetectionPermission = false,
    this.riskDetectionPermission = false,
    this.threatDetectionPermission = false,
    this.intrusionDetectionPermission = false,
    this.malwareDetectionPermission = false,
    this.virusDetectionPermission = false,
    this.spamDetectionPermission = false,
    this.phishingDetectionPermission = false,
    this.socialEngineeringDetectionPermission = false,
    this.dataLeakageDetectionPermission = false,
    this.privacyProtectionPermission = false,
    this.gdprCompliancePermission = false,
    this.hipaaCompliancePermission = false,
    this.soxCompliancePermission = false,
    this.pciCompliancePermission = false,
    this.iso27001CompliancePermission = false,
    this.nistCompliancePermission = false,
    this.cobiTCompliancePermission = false,
    this.itilCompliancePermission = false,
    this.agileCompliancePermission = false,
    this.devopsCompliancePermission = false,
    this.cloudCompliancePermission = false,
    this.containerCompliancePermission = false,
    this.microservicesCompliancePermission = false,
    this.apiCompliancePermission = false,
    this.webserviceCompliancePermission = false,
    this.restCompliancePermission = false,
    this.soapCompliancePermission = false,
    this.graphqlCompliancePermission = false,
    this.grpcCompliancePermission = false,
    this.websocketCompliancePermission = false,
    this.mqttCompliancePermission = false,
    this.amqpCompliancePermission = false,
    this.kafkaCompliancePermission = false,
    this.redisCompliancePermission = false,
    this.elasticsearchCompliancePermission = false,
    this.mongodbCompliancePermission = false,
    this.postgresqlCompliancePermission = false,
    this.mysqlCompliancePermission = false,
    this.oracleCompliancePermission = false,
    this.sqlServerCompliancePermission = false,
    this.db2CompliancePermission = false,
    this.cassandraCompliancePermission = false,
    this.dynamodbCompliancePermission = false,
    this.cosmosdbCompliancePermission = false,
    this.firebaseCompliancePermission = false,
    this.supabaseCompliancePermission = false,
    this.awsCompliancePermission = false,
    this.azureCompliancePermission = false,
    this.gcpCompliancePermission = false,
    this.alibabaCloudCompliancePermission = false,
    this.ibmCloudCompliancePermission = false,
    this.oracleCloudCompliancePermission = false,
    this.digitalOceanCompliancePermission = false,
    this.linodeCompliancePermission = false,

    // الصلاحيات المتقدمة
    AdvancedAdminPermissionsModel? advancedPermissions,
  }) : advancedPermissions =
            advancedPermissions ?? AdvancedAdminPermissionsModel();

  /// إنشاء من JSON
  SuperAdminModel.fromJson(Map<dynamic, dynamic> json)
      : email = json['email'] ?? '',
        userTitle = json['userTitle'] ?? '',
        databaseId = json['databaseId'] ?? '',
        profilePicture = json['profilePicture'],
        isActive = json['isActive'] ?? true,
        userKey = json['userKey'],

        // الصلاحيات الأساسية
        salePermission = json['salePermission'] ?? false,
        partiesPermission = json['partiesPermission'] ?? false,
        purchasePermission = json['purchasePermission'] ?? false,
        productPermission = json['productPermission'] ?? false,
        profileEditPermission = json['profileEditPermission'] ?? false,
        addExpensePermission = json['addExpensePermission'] ?? false,
        lossProfitPermission = json['lossProfitPermission'] ?? false,
        dueListPermission = json['dueListPermission'] ?? false,
        stockPermission = json['stockPermission'] ?? false,
        reportsPermission = json['reportsPermission'] ?? false,
        salesListPermission = json['salesListPermission'] ?? false,
        purchaseListPermission = json['purchaseListPermission'] ?? false,

        // الصلاحيات الحديثة
        aiChatPermission = json['aiChatPermission'] ?? false,
        aiAssistantPermission = json['aiAssistantPermission'] ?? false,
        voiceAssistantPermission = json['voiceAssistantPermission'] ?? false,
        treasuryPermission = json['treasuryPermission'] ?? false,
        cashBoxPermission = json['cashBoxPermission'] ?? false,
        deliveryManagementPermission =
            json['deliveryManagementPermission'] ?? false,
        hrmPermission = json['hrmPermission'] ?? false,
        employeesPermission = json['employeesPermission'] ?? false,
        designationPermission = json['designationPermission'] ?? false,
        salariesPermission = json['salariesPermission'] ?? false,
        financialReportsPermission =
            json['financialReportsPermission'] ?? false,
        inventoryManagementPermission =
            json['inventoryManagementPermission'] ?? false,
        customerManagementPermission =
            json['customerManagementPermission'] ?? false,
        supplierManagementPermission =
            json['supplierManagementPermission'] ?? false,
        expenseManagementPermission =
            json['expenseManagementPermission'] ?? false,
        incomeManagementPermission =
            json['incomeManagementPermission'] ?? false,
        bankingPermission = json['bankingPermission'] ?? false,
        loanManagementPermission = json['loanManagementPermission'] ?? false,
        assetManagementPermission = json['assetManagementPermission'] ?? false,
        budgetManagementPermission =
            json['budgetManagementPermission'] ?? false,
        taxManagementPermission = json['taxManagementPermission'] ?? false,
        auditPermission = json['auditPermission'] ?? false,
        compliancePermission = json['compliancePermission'] ?? false,
        riskManagementPermission = json['riskManagementPermission'] ?? false,
        projectManagementPermission =
            json['projectManagementPermission'] ?? false,
        taskManagementPermission = json['taskManagementPermission'] ?? false,
        timeTrackingPermission = json['timeTrackingPermission'] ?? false,
        documentManagementPermission =
            json['documentManagementPermission'] ?? false,
        communicationPermission = json['communicationPermission'] ?? false,
        marketingPermission = json['marketingPermission'] ?? false,
        crmPermission = json['crmPermission'] ?? false,
        analyticsPermission = json['analyticsPermission'] ?? false,
        dashboardPermission = json['dashboardPermission'] ?? false,
        settingsPermission = json['settingsPermission'] ?? false,
        backupPermission = json['backupPermission'] ?? false,
        securityPermission = json['securityPermission'] ?? false,
        integrationPermission = json['integrationPermission'] ?? false,
        apiPermission = json['apiPermission'] ?? false,
        mobileAppPermission = json['mobileAppPermission'] ?? false,
        webAppPermission = json['webAppPermission'] ?? false,
        desktopAppPermission = json['desktopAppPermission'] ?? false,
        multiLocationPermission = json['multiLocationPermission'] ?? false,
        franchisePermission = json['franchisePermission'] ?? false,
        brandingPermission = json['brandingPermission'] ?? false,
        customizationPermission = json['customizationPermission'] ?? false,
        themePermission = json['themePermission'] ?? false,
        languagePermission = json['languagePermission'] ?? false,
        currencyPermission = json['currencyPermission'] ?? false,
        timezonePermission = json['timezonePermission'] ?? false,
        notificationPermission = json['notificationPermission'] ?? false,
        emailPermission = json['emailPermission'] ?? false,
        smsPermission = json['smsPermission'] ?? false,
        pushNotificationPermission =
            json['pushNotificationPermission'] ?? false,
        socialMediaPermission = json['socialMediaPermission'] ?? false,
        ecommercePermission = json['ecommercePermission'] ?? false,
        posPermission = json['posPermission'] ?? false,
        onlineStorePermission = json['onlineStorePermission'] ?? false,
        mobileStorePermission = json['mobileStorePermission'] ?? false,
        wholesalePermission = json['wholesalePermission'] ?? false,
        retailPermission = json['retailPermission'] ?? false,
        distributionPermission = json['distributionPermission'] ?? false,
        manufacturingPermission = json['manufacturingPermission'] ?? false,
        servicePermission = json['servicePermission'] ?? false,
        subscriptionPermission = json['subscriptionPermission'] ?? false,
        membershipPermission = json['membershipPermission'] ?? false,
        loyaltyPermission = json['loyaltyPermission'] ?? false,
        discountPermission = json['discountPermission'] ?? false,
        couponPermission = json['couponPermission'] ?? false,
        promotionPermission = json['promotionPermission'] ?? false,
        campaignPermission = json['campaignPermission'] ?? false,
        surveyPermission = json['surveyPermission'] ?? false,
        feedbackPermission = json['feedbackPermission'] ?? false,
        reviewPermission = json['reviewPermission'] ?? false,
        ratingPermission = json['ratingPermission'] ?? false,
        testimonialPermission = json['testimonialPermission'] ?? false,
        caseStudyPermission = json['caseStudyPermission'] ?? false,
        blogPermission = json['blogPermission'] ?? false,
        newsPermission = json['newsPermission'] ?? false,
        eventPermission = json['eventPermission'] ?? false,
        webinarPermission = json['webinarPermission'] ?? false,
        trainingPermission = json['trainingPermission'] ?? false,
        certificationPermission = json['certificationPermission'] ?? false,
        knowledgeBasePermission = json['knowledgeBasePermission'] ?? false,
        faqPermission = json['faqPermission'] ?? false,
        helpDeskPermission = json['helpDeskPermission'] ?? false,
        ticketPermission = json['ticketPermission'] ?? false,
        chatPermission = json['chatPermission'] ?? false,
        videoCallPermission = json['videoCallPermission'] ?? false,
        screenSharingPermission = json['screenSharingPermission'] ?? false,
        remoteAccessPermission = json['remoteAccessPermission'] ?? false,
        fileManagementPermission = json['fileManagementPermission'] ?? false,
        cloudStoragePermission = json['cloudStoragePermission'] ?? false,
        dataImportPermission = json['dataImportPermission'] ?? false,
        dataExportPermission = json['dataExportPermission'] ?? false,
        dataBackupPermission = json['dataBackupPermission'] ?? false,
        dataRestorePermission = json['dataRestorePermission'] ?? false,
        dataMigrationPermission = json['dataMigrationPermission'] ?? false,
        dataCleaningPermission = json['dataCleaningPermission'] ?? false,
        dataValidationPermission = json['dataValidationPermission'] ?? false,
        dataAnalysisPermission = json['dataAnalysisPermission'] ?? false,
        dataVisualizationPermission =
            json['dataVisualizationPermission'] ?? false,
        reportingPermission = json['reportingPermission'] ?? false,
        forecastingPermission = json['forecastingPermission'] ?? false,
        planningPermission = json['planningPermission'] ?? false,
        budgetingPermission = json['budgetingPermission'] ?? false,
        schedulingPermission = json['schedulingPermission'] ?? false,
        calendarPermission = json['calendarPermission'] ?? false,
        reminderPermission = json['reminderPermission'] ?? false,
        alertPermission = json['alertPermission'] ?? false,
        warningPermission = json['warningPermission'] ?? false,
        errorHandlingPermission = json['errorHandlingPermission'] ?? false,
        debuggingPermission = json['debuggingPermission'] ?? false,
        testingPermission = json['testingPermission'] ?? false,
        qualityAssurancePermission =
            json['qualityAssurancePermission'] ?? false,
        performanceMonitoringPermission =
            json['performanceMonitoringPermission'] ?? false,
        systemHealthPermission = json['systemHealthPermission'] ?? false,
        uptimeMonitoringPermission =
            json['uptimeMonitoringPermission'] ?? false,
        loadBalancingPermission = json['loadBalancingPermission'] ?? false,
        scalingPermission = json['scalingPermission'] ?? false,
        optimizationPermission = json['optimizationPermission'] ?? false,
        cacheManagementPermission = json['cacheManagementPermission'] ?? false,
        databaseOptimizationPermission =
            json['databaseOptimizationPermission'] ?? false,
        queryOptimizationPermission =
            json['queryOptimizationPermission'] ?? false,
        indexManagementPermission = json['indexManagementPermission'] ?? false,
        partitioningPermission = json['partitioningPermission'] ?? false,
        replicationPermission = json['replicationPermission'] ?? false,
        clusteringPermission = json['clusteringPermission'] ?? false,
        shardingPermission = json['shardingPermission'] ?? false,
        loadTestingPermission = json['loadTestingPermission'] ?? false,
        stressTestingPermission = json['stressTestingPermission'] ?? false,
        securityTestingPermission = json['securityTestingPermission'] ?? false,
        penetrationTestingPermission =
            json['penetrationTestingPermission'] ?? false,
        vulnerabilityAssessmentPermission =
            json['vulnerabilityAssessmentPermission'] ?? false,
        complianceAuditPermission = json['complianceAuditPermission'] ?? false,
        riskAssessmentPermission = json['riskAssessmentPermission'] ?? false,
        incidentManagementPermission =
            json['incidentManagementPermission'] ?? false,
        disasterRecoveryPermission =
            json['disasterRecoveryPermission'] ?? false,
        businessContinuityPermission =
            json['businessContinuityPermission'] ?? false,
        changeManagementPermission =
            json['changeManagementPermission'] ?? false,
        versionControlPermission = json['versionControlPermission'] ?? false,
        releaseManagementPermission =
            json['releaseManagementPermission'] ?? false,
        deploymentPermission = json['deploymentPermission'] ?? false,
        rollbackPermission = json['rollbackPermission'] ?? false,
        hotfixPermission = json['hotfixPermission'] ?? false,
        patchManagementPermission = json['patchManagementPermission'] ?? false,
        updateManagementPermission =
            json['updateManagementPermission'] ?? false,
        licenseManagementPermission =
            json['licenseManagementPermission'] ?? false,
        assetTrackingPermission = json['assetTrackingPermission'] ?? false,
        inventoryTrackingPermission =
            json['inventoryTrackingPermission'] ?? false,
        locationTrackingPermission =
            json['locationTrackingPermission'] ?? false,
        gpsTrackingPermission = json['gpsTrackingPermission'] ?? false,
        geofencingPermission = json['geofencingPermission'] ?? false,
        routeOptimizationPermission =
            json['routeOptimizationPermission'] ?? false,
        deliveryTrackingPermission =
            json['deliveryTrackingPermission'] ?? false,
        shippingPermission = json['shippingPermission'] ?? false,
        logisticsPermission = json['logisticsPermission'] ?? false,
        warehousePermission = json['warehousePermission'] ?? false,
        stockManagementPermission = json['stockManagementPermission'] ?? false,
        orderManagementPermission = json['orderManagementPermission'] ?? false,
        fulfillmentPermission = json['fulfillmentPermission'] ?? false,
        returnManagementPermission =
            json['returnManagementPermission'] ?? false,
        refundPermission = json['refundPermission'] ?? false,
        exchangePermission = json['exchangePermission'] ?? false,
        warrantyPermission = json['warrantyPermission'] ?? false,
        serviceContractPermission = json['serviceContractPermission'] ?? false,
        maintenancePermission = json['maintenancePermission'] ?? false,
        repairPermission = json['repairPermission'] ?? false,
        replacementPermission = json['replacementPermission'] ?? false,
        upgradePermission = json['upgradePermission'] ?? false,
        migrationPermission = json['migrationPermission'] ?? false,
        conversionPermission = json['conversionPermission'] ?? false,
        transformationPermission = json['transformationPermission'] ?? false,
        automationPermission = json['automationPermission'] ?? false,
        workflowPermission = json['workflowPermission'] ?? false,
        processManagementPermission =
            json['processManagementPermission'] ?? false,
        procedureManagementPermission =
            json['procedureManagementPermission'] ?? false,
        policyManagementPermission =
            json['policyManagementPermission'] ?? false,
        standardsManagementPermission =
            json['standardsManagementPermission'] ?? false,
        guidelinesManagementPermission =
            json['guidelinesManagementPermission'] ?? false,
        bestPracticesPermission = json['bestPracticesPermission'] ?? false,
        knowledgeManagementPermission =
            json['knowledgeManagementPermission'] ?? false,
        learningManagementPermission =
            json['learningManagementPermission'] ?? false,
        skillManagementPermission = json['skillManagementPermission'] ?? false,
        competencyManagementPermission =
            json['competencyManagementPermission'] ?? false,
        performanceManagementPermission =
            json['performanceManagementPermission'] ?? false,
        goalManagementPermission = json['goalManagementPermission'] ?? false,
        objectiveManagementPermission =
            json['objectiveManagementPermission'] ?? false,
        kpiManagementPermission = json['kpiManagementPermission'] ?? false,
        metricManagementPermission =
            json['metricManagementPermission'] ?? false,
        benchmarkingPermission = json['benchmarkingPermission'] ?? false,
        comparisonPermission = json['comparisonPermission'] ?? false,
        competitiveAnalysisPermission =
            json['competitiveAnalysisPermission'] ?? false,
        marketAnalysisPermission = json['marketAnalysisPermission'] ?? false,
        trendAnalysisPermission = json['trendAnalysisPermission'] ?? false,
        sentimentAnalysisPermission =
            json['sentimentAnalysisPermission'] ?? false,
        behaviorAnalysisPermission =
            json['behaviorAnalysisPermission'] ?? false,
        usageAnalysisPermission = json['usageAnalysisPermission'] ?? false,
        patternAnalysisPermission = json['patternAnalysisPermission'] ?? false,
        anomalyDetectionPermission =
            json['anomalyDetectionPermission'] ?? false,
        fraudDetectionPermission = json['fraudDetectionPermission'] ?? false,
        riskDetectionPermission = json['riskDetectionPermission'] ?? false,
        threatDetectionPermission = json['threatDetectionPermission'] ?? false,
        intrusionDetectionPermission =
            json['intrusionDetectionPermission'] ?? false,
        malwareDetectionPermission =
            json['malwareDetectionPermission'] ?? false,
        virusDetectionPermission = json['virusDetectionPermission'] ?? false,
        spamDetectionPermission = json['spamDetectionPermission'] ?? false,
        phishingDetectionPermission =
            json['phishingDetectionPermission'] ?? false,
        socialEngineeringDetectionPermission =
            json['socialEngineeringDetectionPermission'] ?? false,
        dataLeakageDetectionPermission =
            json['dataLeakageDetectionPermission'] ?? false,
        privacyProtectionPermission =
            json['privacyProtectionPermission'] ?? false,
        gdprCompliancePermission = json['gdprCompliancePermission'] ?? false,
        hipaaCompliancePermission = json['hipaaCompliancePermission'] ?? false,
        soxCompliancePermission = json['soxCompliancePermission'] ?? false,
        pciCompliancePermission = json['pciCompliancePermission'] ?? false,
        iso27001CompliancePermission =
            json['iso27001CompliancePermission'] ?? false,
        nistCompliancePermission = json['nistCompliancePermission'] ?? false,
        cobiTCompliancePermission = json['cobiTCompliancePermission'] ?? false,
        itilCompliancePermission = json['itilCompliancePermission'] ?? false,
        agileCompliancePermission = json['agileCompliancePermission'] ?? false,
        devopsCompliancePermission =
            json['devopsCompliancePermission'] ?? false,
        cloudCompliancePermission = json['cloudCompliancePermission'] ?? false,
        containerCompliancePermission =
            json['containerCompliancePermission'] ?? false,
        microservicesCompliancePermission =
            json['microservicesCompliancePermission'] ?? false,
        apiCompliancePermission = json['apiCompliancePermission'] ?? false,
        webserviceCompliancePermission =
            json['webserviceCompliancePermission'] ?? false,
        restCompliancePermission = json['restCompliancePermission'] ?? false,
        soapCompliancePermission = json['soapCompliancePermission'] ?? false,
        graphqlCompliancePermission =
            json['graphqlCompliancePermission'] ?? false,
        grpcCompliancePermission = json['grpcCompliancePermission'] ?? false,
        websocketCompliancePermission =
            json['websocketCompliancePermission'] ?? false,
        mqttCompliancePermission = json['mqttCompliancePermission'] ?? false,
        amqpCompliancePermission = json['amqpCompliancePermission'] ?? false,
        kafkaCompliancePermission = json['kafkaCompliancePermission'] ?? false,
        redisCompliancePermission = json['redisCompliancePermission'] ?? false,
        elasticsearchCompliancePermission =
            json['elasticsearchCompliancePermission'] ?? false,
        mongodbCompliancePermission =
            json['mongodbCompliancePermission'] ?? false,
        postgresqlCompliancePermission =
            json['postgresqlCompliancePermission'] ?? false,
        mysqlCompliancePermission = json['mysqlCompliancePermission'] ?? false,
        oracleCompliancePermission =
            json['oracleCompliancePermission'] ?? false,
        sqlServerCompliancePermission =
            json['sqlServerCompliancePermission'] ?? false,
        db2CompliancePermission = json['db2CompliancePermission'] ?? false,
        cassandraCompliancePermission =
            json['cassandraCompliancePermission'] ?? false,
        dynamodbCompliancePermission =
            json['dynamodbCompliancePermission'] ?? false,
        cosmosdbCompliancePermission =
            json['cosmosdbCompliancePermission'] ?? false,
        firebaseCompliancePermission =
            json['firebaseCompliancePermission'] ?? false,
        supabaseCompliancePermission =
            json['supabaseCompliancePermission'] ?? false,
        awsCompliancePermission = json['awsCompliancePermission'] ?? false,
        azureCompliancePermission = json['azureCompliancePermission'] ?? false,
        gcpCompliancePermission = json['gcpCompliancePermission'] ?? false,
        alibabaCloudCompliancePermission =
            json['alibabaCloudCompliancePermission'] ?? false,
        ibmCloudCompliancePermission =
            json['ibmCloudCompliancePermission'] ?? false,
        oracleCloudCompliancePermission =
            json['oracleCloudCompliancePermission'] ?? false,
        digitalOceanCompliancePermission =
            json['digitalOceanCompliancePermission'] ?? false,
        linodeCompliancePermission =
            json['linodeCompliancePermission'] ?? false,

        // الصلاحيات المتقدمة
        advancedPermissions = json['advancedPermissions'] != null
            ? AdvancedAdminPermissionsModel.fromJson(
                json['advancedPermissions'])
            : AdvancedAdminPermissionsModel();

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => {
        'email': email,
        'userTitle': userTitle,
        'databaseId': databaseId,
        'profilePicture': profilePicture,
        'isActive': isActive,
        'userKey': userKey,

        // الصلاحيات الأساسية
        'salePermission': salePermission,
        'partiesPermission': partiesPermission,
        'purchasePermission': purchasePermission,
        'productPermission': productPermission,
        'profileEditPermission': profileEditPermission,
        'addExpensePermission': addExpensePermission,
        'lossProfitPermission': lossProfitPermission,
        'dueListPermission': dueListPermission,
        'stockPermission': stockPermission,
        'reportsPermission': reportsPermission,
        'salesListPermission': salesListPermission,
        'purchaseListPermission': purchaseListPermission,

        // الصلاحيات الحديثة
        'aiChatPermission': aiChatPermission,
        'aiAssistantPermission': aiAssistantPermission,
        'voiceAssistantPermission': voiceAssistantPermission,
        'treasuryPermission': treasuryPermission,
        'cashBoxPermission': cashBoxPermission,
        'deliveryManagementPermission': deliveryManagementPermission,
        'hrmPermission': hrmPermission,
        'employeesPermission': employeesPermission,
        'designationPermission': designationPermission,
        'salariesPermission': salariesPermission,
        'financialReportsPermission': financialReportsPermission,
        'inventoryManagementPermission': inventoryManagementPermission,
        'customerManagementPermission': customerManagementPermission,
        'supplierManagementPermission': supplierManagementPermission,
        'expenseManagementPermission': expenseManagementPermission,
        'incomeManagementPermission': incomeManagementPermission,
        'bankingPermission': bankingPermission,
        'loanManagementPermission': loanManagementPermission,
        'assetManagementPermission': assetManagementPermission,
        'budgetManagementPermission': budgetManagementPermission,
        'taxManagementPermission': taxManagementPermission,
        'auditPermission': auditPermission,
        'compliancePermission': compliancePermission,
        'riskManagementPermission': riskManagementPermission,
        'projectManagementPermission': projectManagementPermission,
        'taskManagementPermission': taskManagementPermission,
        'timeTrackingPermission': timeTrackingPermission,
        'documentManagementPermission': documentManagementPermission,
        'communicationPermission': communicationPermission,
        'marketingPermission': marketingPermission,
        'crmPermission': crmPermission,
        'analyticsPermission': analyticsPermission,
        'dashboardPermission': dashboardPermission,
        'settingsPermission': settingsPermission,
        'backupPermission': backupPermission,
        'securityPermission': securityPermission,
        'integrationPermission': integrationPermission,
        'apiPermission': apiPermission,
        'mobileAppPermission': mobileAppPermission,
        'webAppPermission': webAppPermission,
        'desktopAppPermission': desktopAppPermission,
        'multiLocationPermission': multiLocationPermission,
        'franchisePermission': franchisePermission,
        'brandingPermission': brandingPermission,
        'customizationPermission': customizationPermission,
        'themePermission': themePermission,
        'languagePermission': languagePermission,
        'currencyPermission': currencyPermission,
        'timezonePermission': timezonePermission,
        'notificationPermission': notificationPermission,
        'emailPermission': emailPermission,
        'smsPermission': smsPermission,
        'pushNotificationPermission': pushNotificationPermission,
        'socialMediaPermission': socialMediaPermission,
        'ecommercePermission': ecommercePermission,
        'posPermission': posPermission,
        'onlineStorePermission': onlineStorePermission,
        'mobileStorePermission': mobileStorePermission,
        'wholesalePermission': wholesalePermission,
        'retailPermission': retailPermission,
        'distributionPermission': distributionPermission,
        'manufacturingPermission': manufacturingPermission,
        'servicePermission': servicePermission,
        'subscriptionPermission': subscriptionPermission,
        'membershipPermission': membershipPermission,
        'loyaltyPermission': loyaltyPermission,
        'discountPermission': discountPermission,
        'couponPermission': couponPermission,
        'promotionPermission': promotionPermission,
        'campaignPermission': campaignPermission,
        'surveyPermission': surveyPermission,
        'feedbackPermission': feedbackPermission,
        'reviewPermission': reviewPermission,
        'ratingPermission': ratingPermission,
        'testimonialPermission': testimonialPermission,
        'caseStudyPermission': caseStudyPermission,
        'blogPermission': blogPermission,
        'newsPermission': newsPermission,
        'eventPermission': eventPermission,
        'webinarPermission': webinarPermission,
        'trainingPermission': trainingPermission,
        'certificationPermission': certificationPermission,
        'knowledgeBasePermission': knowledgeBasePermission,
        'faqPermission': faqPermission,
        'helpDeskPermission': helpDeskPermission,
        'ticketPermission': ticketPermission,
        'chatPermission': chatPermission,
        'videoCallPermission': videoCallPermission,
        'screenSharingPermission': screenSharingPermission,
        'remoteAccessPermission': remoteAccessPermission,
        'fileManagementPermission': fileManagementPermission,
        'cloudStoragePermission': cloudStoragePermission,
        'dataImportPermission': dataImportPermission,
        'dataExportPermission': dataExportPermission,
        'dataBackupPermission': dataBackupPermission,
        'dataRestorePermission': dataRestorePermission,
        'dataMigrationPermission': dataMigrationPermission,
        'dataCleaningPermission': dataCleaningPermission,
        'dataValidationPermission': dataValidationPermission,
        'dataAnalysisPermission': dataAnalysisPermission,
        'dataVisualizationPermission': dataVisualizationPermission,
        'reportingPermission': reportingPermission,
        'forecastingPermission': forecastingPermission,
        'planningPermission': planningPermission,
        'budgetingPermission': budgetingPermission,
        'schedulingPermission': schedulingPermission,
        'calendarPermission': calendarPermission,
        'reminderPermission': reminderPermission,
        'alertPermission': alertPermission,
        'warningPermission': warningPermission,
        'errorHandlingPermission': errorHandlingPermission,
        'debuggingPermission': debuggingPermission,
        'testingPermission': testingPermission,
        'qualityAssurancePermission': qualityAssurancePermission,
        'performanceMonitoringPermission': performanceMonitoringPermission,
        'systemHealthPermission': systemHealthPermission,
        'uptimeMonitoringPermission': uptimeMonitoringPermission,
        'loadBalancingPermission': loadBalancingPermission,
        'scalingPermission': scalingPermission,
        'optimizationPermission': optimizationPermission,
        'cacheManagementPermission': cacheManagementPermission,
        'databaseOptimizationPermission': databaseOptimizationPermission,
        'queryOptimizationPermission': queryOptimizationPermission,
        'indexManagementPermission': indexManagementPermission,
        'partitioningPermission': partitioningPermission,
        'replicationPermission': replicationPermission,
        'clusteringPermission': clusteringPermission,
        'shardingPermission': shardingPermission,
        'loadTestingPermission': loadTestingPermission,
        'stressTestingPermission': stressTestingPermission,
        'securityTestingPermission': securityTestingPermission,
        'penetrationTestingPermission': penetrationTestingPermission,
        'vulnerabilityAssessmentPermission': vulnerabilityAssessmentPermission,
        'complianceAuditPermission': complianceAuditPermission,
        'riskAssessmentPermission': riskAssessmentPermission,
        'incidentManagementPermission': incidentManagementPermission,
        'disasterRecoveryPermission': disasterRecoveryPermission,
        'businessContinuityPermission': businessContinuityPermission,
        'changeManagementPermission': changeManagementPermission,
        'versionControlPermission': versionControlPermission,
        'releaseManagementPermission': releaseManagementPermission,
        'deploymentPermission': deploymentPermission,
        'rollbackPermission': rollbackPermission,
        'hotfixPermission': hotfixPermission,
        'patchManagementPermission': patchManagementPermission,
        'updateManagementPermission': updateManagementPermission,
        'licenseManagementPermission': licenseManagementPermission,
        'assetTrackingPermission': assetTrackingPermission,
        'inventoryTrackingPermission': inventoryTrackingPermission,
        'locationTrackingPermission': locationTrackingPermission,
        'gpsTrackingPermission': gpsTrackingPermission,
        'geofencingPermission': geofencingPermission,
        'routeOptimizationPermission': routeOptimizationPermission,
        'deliveryTrackingPermission': deliveryTrackingPermission,
        'shippingPermission': shippingPermission,
        'logisticsPermission': logisticsPermission,
        'warehousePermission': warehousePermission,
        'stockManagementPermission': stockManagementPermission,
        'orderManagementPermission': orderManagementPermission,
        'fulfillmentPermission': fulfillmentPermission,
        'returnManagementPermission': returnManagementPermission,
        'refundPermission': refundPermission,
        'exchangePermission': exchangePermission,
        'warrantyPermission': warrantyPermission,
        'serviceContractPermission': serviceContractPermission,
        'maintenancePermission': maintenancePermission,
        'repairPermission': repairPermission,
        'replacementPermission': replacementPermission,
        'upgradePermission': upgradePermission,
        'migrationPermission': migrationPermission,
        'conversionPermission': conversionPermission,
        'transformationPermission': transformationPermission,
        'automationPermission': automationPermission,
        'workflowPermission': workflowPermission,
        'processManagementPermission': processManagementPermission,
        'procedureManagementPermission': procedureManagementPermission,
        'policyManagementPermission': policyManagementPermission,
        'standardsManagementPermission': standardsManagementPermission,
        'guidelinesManagementPermission': guidelinesManagementPermission,
        'bestPracticesPermission': bestPracticesPermission,
        'knowledgeManagementPermission': knowledgeManagementPermission,
        'learningManagementPermission': learningManagementPermission,
        'skillManagementPermission': skillManagementPermission,
        'competencyManagementPermission': competencyManagementPermission,
        'performanceManagementPermission': performanceManagementPermission,
        'goalManagementPermission': goalManagementPermission,
        'objectiveManagementPermission': objectiveManagementPermission,
        'kpiManagementPermission': kpiManagementPermission,
        'metricManagementPermission': metricManagementPermission,
        'benchmarkingPermission': benchmarkingPermission,
        'comparisonPermission': comparisonPermission,
        'competitiveAnalysisPermission': competitiveAnalysisPermission,
        'marketAnalysisPermission': marketAnalysisPermission,
        'trendAnalysisPermission': trendAnalysisPermission,
        'sentimentAnalysisPermission': sentimentAnalysisPermission,
        'behaviorAnalysisPermission': behaviorAnalysisPermission,
        'usageAnalysisPermission': usageAnalysisPermission,
        'patternAnalysisPermission': patternAnalysisPermission,
        'anomalyDetectionPermission': anomalyDetectionPermission,
        'fraudDetectionPermission': fraudDetectionPermission,
        'riskDetectionPermission': riskDetectionPermission,
        'threatDetectionPermission': threatDetectionPermission,
        'intrusionDetectionPermission': intrusionDetectionPermission,
        'malwareDetectionPermission': malwareDetectionPermission,
        'virusDetectionPermission': virusDetectionPermission,
        'spamDetectionPermission': spamDetectionPermission,
        'phishingDetectionPermission': phishingDetectionPermission,
        'socialEngineeringDetectionPermission':
            socialEngineeringDetectionPermission,
        'dataLeakageDetectionPermission': dataLeakageDetectionPermission,
        'privacyProtectionPermission': privacyProtectionPermission,
        'gdprCompliancePermission': gdprCompliancePermission,
        'hipaaCompliancePermission': hipaaCompliancePermission,
        'soxCompliancePermission': soxCompliancePermission,
        'pciCompliancePermission': pciCompliancePermission,
        'iso27001CompliancePermission': iso27001CompliancePermission,
        'nistCompliancePermission': nistCompliancePermission,
        'cobiTCompliancePermission': cobiTCompliancePermission,
        'itilCompliancePermission': itilCompliancePermission,
        'agileCompliancePermission': agileCompliancePermission,
        'devopsCompliancePermission': devopsCompliancePermission,
        'cloudCompliancePermission': cloudCompliancePermission,
        'containerCompliancePermission': containerCompliancePermission,
        'microservicesCompliancePermission': microservicesCompliancePermission,
        'apiCompliancePermission': apiCompliancePermission,
        'webserviceCompliancePermission': webserviceCompliancePermission,
        'restCompliancePermission': restCompliancePermission,
        'soapCompliancePermission': soapCompliancePermission,
        'graphqlCompliancePermission': graphqlCompliancePermission,
        'grpcCompliancePermission': grpcCompliancePermission,
        'websocketCompliancePermission': websocketCompliancePermission,
        'mqttCompliancePermission': mqttCompliancePermission,
        'amqpCompliancePermission': amqpCompliancePermission,
        'kafkaCompliancePermission': kafkaCompliancePermission,
        'redisCompliancePermission': redisCompliancePermission,
        'elasticsearchCompliancePermission': elasticsearchCompliancePermission,
        'mongodbCompliancePermission': mongodbCompliancePermission,
        'postgresqlCompliancePermission': postgresqlCompliancePermission,
        'mysqlCompliancePermission': mysqlCompliancePermission,
        'oracleCompliancePermission': oracleCompliancePermission,
        'sqlServerCompliancePermission': sqlServerCompliancePermission,
        'db2CompliancePermission': db2CompliancePermission,
        'cassandraCompliancePermission': cassandraCompliancePermission,
        'dynamodbCompliancePermission': dynamodbCompliancePermission,
        'cosmosdbCompliancePermission': cosmosdbCompliancePermission,
        'firebaseCompliancePermission': firebaseCompliancePermission,
        'supabaseCompliancePermission': supabaseCompliancePermission,
        'awsCompliancePermission': awsCompliancePermission,
        'azureCompliancePermission': azureCompliancePermission,
        'gcpCompliancePermission': gcpCompliancePermission,
        'alibabaCloudCompliancePermission': alibabaCloudCompliancePermission,
        'ibmCloudCompliancePermission': ibmCloudCompliancePermission,
        'oracleCloudCompliancePermission': oracleCloudCompliancePermission,
        'digitalOceanCompliancePermission': digitalOceanCompliancePermission,
        'linodeCompliancePermission': linodeCompliancePermission,

        // الصلاحيات المتقدمة
        'advancedPermissions': advancedPermissions.toJson(),
      };

  /// تعيين جميع الصلاحيات إلى قيمة محددة
  void setAllPermissions(bool value) {
    // الصلاحيات الأساسية
    salePermission = value;
    partiesPermission = value;
    purchasePermission = value;
    productPermission = value;
    profileEditPermission = value;
    addExpensePermission = value;
    lossProfitPermission = value;
    dueListPermission = value;
    stockPermission = value;
    reportsPermission = value;
    salesListPermission = value;
    purchaseListPermission = value;

    // الصلاحيات الحديثة
    aiChatPermission = value;
    aiAssistantPermission = value;
    voiceAssistantPermission = value;
    treasuryPermission = value;
    cashBoxPermission = value;
    deliveryManagementPermission = value;
    hrmPermission = value;
    employeesPermission = value;
    designationPermission = value;
    salariesPermission = value;
    financialReportsPermission = value;
    inventoryManagementPermission = value;
    customerManagementPermission = value;
    supplierManagementPermission = value;
    expenseManagementPermission = value;
    incomeManagementPermission = value;
    bankingPermission = value;
    loanManagementPermission = value;
    assetManagementPermission = value;
    budgetManagementPermission = value;
    taxManagementPermission = value;
    auditPermission = value;
    compliancePermission = value;
    riskManagementPermission = value;
    projectManagementPermission = value;
    taskManagementPermission = value;
    timeTrackingPermission = value;
    documentManagementPermission = value;
    communicationPermission = value;
    marketingPermission = value;
    crmPermission = value;
    analyticsPermission = value;
    dashboardPermission = value;
    settingsPermission = value;
    backupPermission = value;
    securityPermission = value;
    integrationPermission = value;
    apiPermission = value;
    mobileAppPermission = value;
    webAppPermission = value;
    desktopAppPermission = value;
    multiLocationPermission = value;
    franchisePermission = value;
    brandingPermission = value;
    customizationPermission = value;
    themePermission = value;
    languagePermission = value;
    currencyPermission = value;
    timezonePermission = value;
    notificationPermission = value;
    emailPermission = value;
    smsPermission = value;
    pushNotificationPermission = value;
    socialMediaPermission = value;
    ecommercePermission = value;
    posPermission = value;
    onlineStorePermission = value;
    mobileStorePermission = value;
    wholesalePermission = value;
    retailPermission = value;
    distributionPermission = value;
    manufacturingPermission = value;
    servicePermission = value;
    subscriptionPermission = value;
    membershipPermission = value;
    loyaltyPermission = value;
    discountPermission = value;
    couponPermission = value;
    promotionPermission = value;
    campaignPermission = value;
    surveyPermission = value;
    feedbackPermission = value;
    reviewPermission = value;
    ratingPermission = value;
    testimonialPermission = value;
    caseStudyPermission = value;
    blogPermission = value;
    newsPermission = value;
    eventPermission = value;
    webinarPermission = value;
    trainingPermission = value;
    certificationPermission = value;
    knowledgeBasePermission = value;
    faqPermission = value;
    helpDeskPermission = value;
    ticketPermission = value;
    chatPermission = value;
    videoCallPermission = value;
    screenSharingPermission = value;
    remoteAccessPermission = value;
    fileManagementPermission = value;
    cloudStoragePermission = value;
    dataImportPermission = value;
    dataExportPermission = value;
    dataBackupPermission = value;
    dataRestorePermission = value;
    dataMigrationPermission = value;
    dataCleaningPermission = value;
    dataValidationPermission = value;
    dataAnalysisPermission = value;
    dataVisualizationPermission = value;
    reportingPermission = value;
    forecastingPermission = value;
    planningPermission = value;
    budgetingPermission = value;
    schedulingPermission = value;
    calendarPermission = value;
    reminderPermission = value;
    alertPermission = value;
    warningPermission = value;
    errorHandlingPermission = value;
    debuggingPermission = value;
    testingPermission = value;
    qualityAssurancePermission = value;
    performanceMonitoringPermission = value;
    systemHealthPermission = value;
    uptimeMonitoringPermission = value;
    loadBalancingPermission = value;
    scalingPermission = value;
    optimizationPermission = value;
    cacheManagementPermission = value;
    databaseOptimizationPermission = value;
    queryOptimizationPermission = value;
    indexManagementPermission = value;
    partitioningPermission = value;
    replicationPermission = value;
    clusteringPermission = value;
    shardingPermission = value;
    loadTestingPermission = value;
    stressTestingPermission = value;
    securityTestingPermission = value;
    penetrationTestingPermission = value;
    vulnerabilityAssessmentPermission = value;
    complianceAuditPermission = value;
    riskAssessmentPermission = value;
    incidentManagementPermission = value;
    disasterRecoveryPermission = value;
    businessContinuityPermission = value;
    changeManagementPermission = value;
    versionControlPermission = value;
    releaseManagementPermission = value;
    deploymentPermission = value;
    rollbackPermission = value;
    hotfixPermission = value;
    patchManagementPermission = value;
    updateManagementPermission = value;
    licenseManagementPermission = value;
    assetTrackingPermission = value;
    inventoryTrackingPermission = value;
    locationTrackingPermission = value;
    gpsTrackingPermission = value;
    geofencingPermission = value;
    routeOptimizationPermission = value;
    deliveryTrackingPermission = value;
    shippingPermission = value;
    logisticsPermission = value;
    warehousePermission = value;
    stockManagementPermission = value;
    orderManagementPermission = value;
    fulfillmentPermission = value;
    returnManagementPermission = value;
    refundPermission = value;
    exchangePermission = value;
    warrantyPermission = value;
    serviceContractPermission = value;
    maintenancePermission = value;
    repairPermission = value;
    replacementPermission = value;
    upgradePermission = value;
    migrationPermission = value;
    conversionPermission = value;
    transformationPermission = value;
    automationPermission = value;
    workflowPermission = value;
    processManagementPermission = value;
    procedureManagementPermission = value;
    policyManagementPermission = value;
    standardsManagementPermission = value;
    guidelinesManagementPermission = value;
    bestPracticesPermission = value;
    knowledgeManagementPermission = value;
    learningManagementPermission = value;
    skillManagementPermission = value;
    competencyManagementPermission = value;
    performanceManagementPermission = value;
    goalManagementPermission = value;
    objectiveManagementPermission = value;
    kpiManagementPermission = value;
    metricManagementPermission = value;
    benchmarkingPermission = value;
    comparisonPermission = value;
    competitiveAnalysisPermission = value;
    marketAnalysisPermission = value;
    trendAnalysisPermission = value;
    sentimentAnalysisPermission = value;
    behaviorAnalysisPermission = value;
    usageAnalysisPermission = value;
    patternAnalysisPermission = value;
    anomalyDetectionPermission = value;
    fraudDetectionPermission = value;
    riskDetectionPermission = value;
    threatDetectionPermission = value;
    intrusionDetectionPermission = value;
    malwareDetectionPermission = value;
    virusDetectionPermission = value;
    spamDetectionPermission = value;
    phishingDetectionPermission = value;
    socialEngineeringDetectionPermission = value;
    dataLeakageDetectionPermission = value;
    privacyProtectionPermission = value;
    gdprCompliancePermission = value;
    hipaaCompliancePermission = value;
    soxCompliancePermission = value;
    pciCompliancePermission = value;
    iso27001CompliancePermission = value;
    nistCompliancePermission = value;
    cobiTCompliancePermission = value;
    itilCompliancePermission = value;
    agileCompliancePermission = value;
    devopsCompliancePermission = value;
    cloudCompliancePermission = value;
    containerCompliancePermission = value;
    microservicesCompliancePermission = value;
    apiCompliancePermission = value;
    webserviceCompliancePermission = value;
    restCompliancePermission = value;
    soapCompliancePermission = value;
    graphqlCompliancePermission = value;
    grpcCompliancePermission = value;
    websocketCompliancePermission = value;
    mqttCompliancePermission = value;
    amqpCompliancePermission = value;
    kafkaCompliancePermission = value;
    redisCompliancePermission = value;
    elasticsearchCompliancePermission = value;
    mongodbCompliancePermission = value;
    postgresqlCompliancePermission = value;
    mysqlCompliancePermission = value;
    oracleCompliancePermission = value;
    sqlServerCompliancePermission = value;
    db2CompliancePermission = value;
    cassandraCompliancePermission = value;
    dynamodbCompliancePermission = value;
    cosmosdbCompliancePermission = value;
    firebaseCompliancePermission = value;
    supabaseCompliancePermission = value;
    awsCompliancePermission = value;
    azureCompliancePermission = value;
    gcpCompliancePermission = value;
    alibabaCloudCompliancePermission = value;
    ibmCloudCompliancePermission = value;
    oracleCloudCompliancePermission = value;
    digitalOceanCompliancePermission = value;
    linodeCompliancePermission = value;

    // الصلاحيات المتقدمة
    advancedPermissions.setAllPermissions(value);
  }

  /// التحقق من وجود أي صلاحية
  bool hasAnyPermission() {
    return salePermission ||
        partiesPermission ||
        purchasePermission ||
        productPermission ||
        profileEditPermission ||
        addExpensePermission ||
        lossProfitPermission ||
        dueListPermission ||
        stockPermission ||
        reportsPermission ||
        salesListPermission ||
        purchaseListPermission ||
        aiChatPermission ||
        aiAssistantPermission ||
        voiceAssistantPermission ||
        treasuryPermission ||
        cashBoxPermission ||
        deliveryManagementPermission ||
        hrmPermission ||
        employeesPermission ||
        designationPermission ||
        salariesPermission ||
        financialReportsPermission ||
        inventoryManagementPermission ||
        customerManagementPermission ||
        supplierManagementPermission ||
        expenseManagementPermission ||
        incomeManagementPermission ||
        bankingPermission ||
        loanManagementPermission ||
        assetManagementPermission ||
        budgetManagementPermission ||
        taxManagementPermission ||
        auditPermission ||
        compliancePermission ||
        riskManagementPermission ||
        projectManagementPermission ||
        taskManagementPermission ||
        timeTrackingPermission ||
        documentManagementPermission ||
        communicationPermission ||
        marketingPermission ||
        crmPermission ||
        analyticsPermission ||
        dashboardPermission ||
        settingsPermission ||
        backupPermission ||
        securityPermission ||
        integrationPermission ||
        apiPermission ||
        mobileAppPermission ||
        webAppPermission ||
        desktopAppPermission ||
        multiLocationPermission ||
        franchisePermission ||
        brandingPermission ||
        customizationPermission ||
        themePermission ||
        languagePermission ||
        currencyPermission ||
        timezonePermission ||
        notificationPermission ||
        emailPermission ||
        smsPermission ||
        pushNotificationPermission ||
        socialMediaPermission ||
        ecommercePermission ||
        posPermission ||
        onlineStorePermission ||
        mobileStorePermission ||
        wholesalePermission ||
        retailPermission ||
        distributionPermission ||
        manufacturingPermission ||
        servicePermission ||
        subscriptionPermission ||
        membershipPermission ||
        loyaltyPermission ||
        discountPermission ||
        couponPermission ||
        promotionPermission ||
        campaignPermission ||
        surveyPermission ||
        feedbackPermission ||
        reviewPermission ||
        ratingPermission ||
        testimonialPermission ||
        caseStudyPermission ||
        blogPermission ||
        newsPermission ||
        eventPermission ||
        webinarPermission ||
        trainingPermission ||
        certificationPermission ||
        knowledgeBasePermission ||
        faqPermission ||
        helpDeskPermission ||
        ticketPermission ||
        chatPermission ||
        videoCallPermission ||
        screenSharingPermission ||
        remoteAccessPermission ||
        fileManagementPermission ||
        cloudStoragePermission ||
        dataImportPermission ||
        dataExportPermission ||
        dataBackupPermission ||
        dataRestorePermission ||
        dataMigrationPermission ||
        dataCleaningPermission ||
        dataValidationPermission ||
        dataAnalysisPermission ||
        dataVisualizationPermission ||
        reportingPermission ||
        forecastingPermission ||
        planningPermission ||
        budgetingPermission ||
        schedulingPermission ||
        calendarPermission ||
        reminderPermission ||
        alertPermission ||
        warningPermission ||
        errorHandlingPermission ||
        debuggingPermission ||
        testingPermission ||
        qualityAssurancePermission ||
        performanceMonitoringPermission ||
        systemHealthPermission ||
        uptimeMonitoringPermission ||
        loadBalancingPermission ||
        scalingPermission ||
        optimizationPermission ||
        cacheManagementPermission ||
        databaseOptimizationPermission ||
        queryOptimizationPermission ||
        indexManagementPermission ||
        partitioningPermission ||
        replicationPermission ||
        clusteringPermission ||
        shardingPermission ||
        loadTestingPermission ||
        stressTestingPermission ||
        securityTestingPermission ||
        penetrationTestingPermission ||
        vulnerabilityAssessmentPermission ||
        complianceAuditPermission ||
        riskAssessmentPermission ||
        incidentManagementPermission ||
        disasterRecoveryPermission ||
        businessContinuityPermission ||
        changeManagementPermission ||
        versionControlPermission ||
        releaseManagementPermission ||
        deploymentPermission ||
        rollbackPermission ||
        hotfixPermission ||
        patchManagementPermission ||
        updateManagementPermission ||
        licenseManagementPermission ||
        assetTrackingPermission ||
        inventoryTrackingPermission ||
        locationTrackingPermission ||
        gpsTrackingPermission ||
        geofencingPermission ||
        routeOptimizationPermission ||
        deliveryTrackingPermission ||
        shippingPermission ||
        logisticsPermission ||
        warehousePermission ||
        stockManagementPermission ||
        orderManagementPermission ||
        fulfillmentPermission ||
        returnManagementPermission ||
        refundPermission ||
        exchangePermission ||
        warrantyPermission ||
        serviceContractPermission ||
        maintenancePermission ||
        repairPermission ||
        replacementPermission ||
        upgradePermission ||
        migrationPermission ||
        conversionPermission ||
        transformationPermission ||
        automationPermission ||
        workflowPermission ||
        processManagementPermission ||
        procedureManagementPermission ||
        policyManagementPermission ||
        standardsManagementPermission ||
        guidelinesManagementPermission ||
        bestPracticesPermission ||
        knowledgeManagementPermission ||
        learningManagementPermission ||
        skillManagementPermission ||
        competencyManagementPermission ||
        performanceManagementPermission ||
        goalManagementPermission ||
        objectiveManagementPermission ||
        kpiManagementPermission ||
        metricManagementPermission ||
        benchmarkingPermission ||
        comparisonPermission ||
        competitiveAnalysisPermission ||
        marketAnalysisPermission ||
        trendAnalysisPermission ||
        sentimentAnalysisPermission ||
        behaviorAnalysisPermission ||
        usageAnalysisPermission ||
        patternAnalysisPermission ||
        anomalyDetectionPermission ||
        fraudDetectionPermission ||
        riskDetectionPermission ||
        threatDetectionPermission ||
        intrusionDetectionPermission ||
        malwareDetectionPermission ||
        virusDetectionPermission ||
        spamDetectionPermission ||
        phishingDetectionPermission ||
        socialEngineeringDetectionPermission ||
        dataLeakageDetectionPermission ||
        privacyProtectionPermission ||
        gdprCompliancePermission ||
        hipaaCompliancePermission ||
        soxCompliancePermission ||
        pciCompliancePermission ||
        iso27001CompliancePermission ||
        nistCompliancePermission ||
        cobiTCompliancePermission ||
        itilCompliancePermission ||
        agileCompliancePermission ||
        devopsCompliancePermission ||
        cloudCompliancePermission ||
        containerCompliancePermission ||
        microservicesCompliancePermission ||
        apiCompliancePermission ||
        webserviceCompliancePermission ||
        restCompliancePermission ||
        soapCompliancePermission ||
        graphqlCompliancePermission ||
        grpcCompliancePermission ||
        websocketCompliancePermission ||
        mqttCompliancePermission ||
        amqpCompliancePermission ||
        kafkaCompliancePermission ||
        redisCompliancePermission ||
        elasticsearchCompliancePermission ||
        mongodbCompliancePermission ||
        postgresqlCompliancePermission ||
        mysqlCompliancePermission ||
        oracleCompliancePermission ||
        sqlServerCompliancePermission ||
        db2CompliancePermission ||
        cassandraCompliancePermission ||
        dynamodbCompliancePermission ||
        cosmosdbCompliancePermission ||
        firebaseCompliancePermission ||
        supabaseCompliancePermission ||
        awsCompliancePermission ||
        azureCompliancePermission ||
        gcpCompliancePermission ||
        alibabaCloudCompliancePermission ||
        ibmCloudCompliancePermission ||
        oracleCloudCompliancePermission ||
        digitalOceanCompliancePermission ||
        linodeCompliancePermission ||
        advancedPermissions.hasAnyPermission();
  }

  /// التحقق من كونه أدمن شامل (يملك جميع الصلاحيات)
  bool isSuperAdmin() {
    return hasAllBasicPermissions() &&
        hasAllAdvancedPermissions() &&
        advancedPermissions.hasAnyPermission();
  }

  /// التحقق من وجود جميع الصلاحيات الأساسية
  bool hasAllBasicPermissions() {
    return salePermission &&
        partiesPermission &&
        purchasePermission &&
        productPermission &&
        profileEditPermission &&
        addExpensePermission &&
        lossProfitPermission &&
        dueListPermission &&
        stockPermission &&
        reportsPermission &&
        salesListPermission &&
        purchaseListPermission;
  }

  /// التحقق من وجود جميع الصلاحيات المتقدمة
  bool hasAllAdvancedPermissions() {
    return aiChatPermission &&
        aiAssistantPermission &&
        voiceAssistantPermission &&
        treasuryPermission &&
        cashBoxPermission &&
        deliveryManagementPermission &&
        hrmPermission &&
        employeesPermission &&
        designationPermission &&
        salariesPermission &&
        financialReportsPermission;
  }

  /// إنشاء أدمن شامل بجميع الصلاحيات
  static SuperAdminModel createSuperAdmin({
    required String email,
    required String userTitle,
    required String databaseId,
    String? profilePicture,
    String? userKey,
  }) {
    final superAdmin = SuperAdminModel(
      email: email,
      userTitle: userTitle,
      databaseId: databaseId,
      profilePicture: profilePicture,
      userKey: userKey,
      isActive: true,
    );

    // تعيين جميع الصلاحيات
    superAdmin.setAllPermissions(true);

    return superAdmin;
  }
}
