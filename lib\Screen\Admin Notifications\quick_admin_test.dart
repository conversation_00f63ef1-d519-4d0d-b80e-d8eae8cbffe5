import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../Provider/admin_notifications_provider.dart';
import '../../Screen/Widgets/Constant Data/constant.dart';

/// شاشة اختبار سريع للإشعارات الإدارية
class QuickAdminTest extends ConsumerWidget {
  static const String route = '/quick-admin-test';
  const QuickAdminTest({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provider = ref.watch(adminNotificationsProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'اختبار سريع للإشعارات',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: provider.isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبار سريع لنظام الإشعارات',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختبر إرسال أنواع مختلفة من الإشعارات بنقرة واحدة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // أزرار الاختبار السريع
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'إشعار معلوماتي',
                      subtitle: 'إشعار عام للمستخدمين',
                      icon: 'ℹ️',
                      color: Colors.blue,
                      onPressed: () => _sendTestNotification(
                        ref,
                        'إشعار تجريبي',
                        'هذا إشعار تجريبي من لوحة التحكم الإدارية',
                        'info',
                      ),
                    ),
                    
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'تحذير مهم',
                      subtitle: 'تحذير للمستخدمين',
                      icon: '⚠️',
                      color: Colors.orange,
                      onPressed: () => _sendTestNotification(
                        ref,
                        'تحذير مهم',
                        'يرجى الانتباه لهذا التحذير المهم',
                        'warning',
                      ),
                    ),
                    
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'إشعار نجاح',
                      subtitle: 'إشعار نجاح عملية',
                      icon: '✅',
                      color: Colors.green,
                      onPressed: () => _sendTestNotification(
                        ref,
                        'تم بنجاح',
                        'تمت العملية بنجاح',
                        'success',
                      ),
                    ),
                    
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'إشعار طوارئ',
                      subtitle: 'إشعار طوارئ عاجل',
                      icon: '🚨',
                      color: Colors.red,
                      onPressed: () => _sendEmergencyNotification(ref),
                    ),
                    
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'إشعار صيانة',
                      subtitle: 'إشعار صيانة مجدولة',
                      icon: '🔧',
                      color: Colors.purple,
                      onPressed: () => _sendMaintenanceNotification(ref),
                    ),
                    
                    _buildQuickTestButton(
                      context: context,
                      ref: ref,
                      title: 'إشعار ترويجي',
                      subtitle: 'إشعار عرض أو ترويج',
                      icon: '🎉',
                      color: Colors.deepOrange,
                      onPressed: () => _sendPromotionalNotification(ref),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // إحصائيات سريعة
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: kMainColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: kMainColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إحصائيات سريعة',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: kMainColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildStatItem(
                                  'إجمالي الإشعارات',
                                  '${provider.stats['total'] ?? 0}',
                                  Icons.notifications,
                                ),
                              ),
                              Expanded(
                                child: _buildStatItem(
                                  'النشطة',
                                  '${provider.stats['active'] ?? 0}',
                                  Icons.notifications_active,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildQuickTestButton({
    required BuildContext context,
    required WidgetRef ref,
    required String title,
    required String subtitle,
    required String icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    icon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.send,
                  color: color,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: kMainColor, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _sendTestNotification(
    WidgetRef ref,
    String title,
    String message,
    String type,
  ) async {
    final provider = ref.read(adminNotificationsProvider);
    
    final success = await provider.sendNotification(
      title: title,
      message: message,
      type: type,
    );

    if (ref.context.mounted) {
      ScaffoldMessenger.of(ref.context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم إرسال الإشعار بنجاح' : 'فشل في إرسال الإشعار',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _sendEmergencyNotification(WidgetRef ref) async {
    final provider = ref.read(adminNotificationsProvider);
    
    final success = await provider.sendEmergencyNotification(
      title: 'إشعار طوارئ عاجل',
      message: 'هذا إشعار طوارئ تجريبي - يرجى الانتباه',
    );

    if (ref.context.mounted) {
      ScaffoldMessenger.of(ref.context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم إرسال إشعار الطوارئ بنجاح' : 'فشل في إرسال إشعار الطوارئ',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _sendMaintenanceNotification(WidgetRef ref) async {
    final provider = ref.read(adminNotificationsProvider);
    
    final success = await provider.sendMaintenanceNotification(
      title: 'صيانة مجدولة',
      message: 'سيتم إجراء صيانة على النظام قريباً',
      scheduledTime: DateTime.now().add(const Duration(hours: 2)),
      estimatedDuration: 'ساعة واحدة',
    );

    if (ref.context.mounted) {
      ScaffoldMessenger.of(ref.context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم إرسال إشعار الصيانة بنجاح' : 'فشل في إرسال إشعار الصيانة',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _sendPromotionalNotification(WidgetRef ref) async {
    final provider = ref.read(adminNotificationsProvider);
    
    final success = await provider.sendPromotionalNotification(
      title: 'عرض خاص',
      message: 'عرض خاص لفترة محدودة - لا تفوت الفرصة!',
      expiryDate: DateTime.now().add(const Duration(days: 7)),
    );

    if (ref.context.mounted) {
      ScaffoldMessenger.of(ref.context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم إرسال الإشعار الترويجي بنجاح' : 'فشل في إرسال الإشعار الترويجي',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}
