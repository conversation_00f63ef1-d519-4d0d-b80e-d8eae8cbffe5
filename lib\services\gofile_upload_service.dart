import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// خدمة للتعامل مع رفع الملفات إلى Gofile.io
class GoFileService {
  // معلومات حساب GoFile
  static const String _accountToken = 'w4MTMPLpxFYsHoxndRQc5QoiBJeUAWKv';

  // عنوان الحصول على أفضل خادم
  static const String _getServerUrl = 'https://api.gofile.io/getServer';

  // عنوان رفع الملفات الافتراضي
  static const String _defaultUploadUrl = 'https://store1.gofile.io/uploadFile';

  /// الحصول على أفضل خادم للرفع
  static Future<String> getBestServer() async {
    try {
      final response = await http.get(Uri.parse(_getServerUrl));
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['status'] == 'ok') {
          final server = jsonResponse['data']['server'];
          if (kDebugMode) {
            debugPrint('تم الحصول على أفضل خادم: $server');
          }
          return 'https://$server.gofile.io/uploadFile';
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في الحصول على الخادم: $e');
      }
    }
    // العودة للخادم الافتراضي في حالة الخطأ
    return _defaultUploadUrl;
  }

  /// رفع ملف إلى Gofile.io (للويب - يستخدم Uint8List)
  static Future<Map<String, dynamic>> uploadFile(
      Uint8List fileBytes, String fileName,
      {String? mimeType}) async {
    try {
      if (kDebugMode) {
        debugPrint('جاري رفع الملف إلى GoFile...');
      }

      // التحقق من حجم الملف (الحد الأقصى 5GB)
      if (fileBytes.length > 5 * 1024 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جداً (الحد الأقصى 5GB)');
      }

      // الحصول على أفضل خادم
      final uploadUrl = await getBestServer();

      // إنشاء طلب متعدد الأجزاء
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));

      // إضافة معلومات الحساب
      request.fields['token'] = _accountToken;

      // إضافة الملف إلى الطلب
      final multipartFile = http.MultipartFile.fromBytes(
        'file',
        fileBytes,
        filename: fileName,
      );

      request.files.add(multipartFile);

      // إرسال الطلب
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (kDebugMode) {
        debugPrint('استجابة GoFile: ${response.statusCode}');
        if (response.statusCode != 200) {
          debugPrint('محتوى الاستجابة: ${response.body}');
        }
      }

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        if (jsonResponse['status'] == 'ok') {
          final data = jsonResponse['data'];

          // استخراج معلومات الملف
          final downloadPage = data['downloadPage'] ?? '';
          final fileId = data['fileId'] ?? '';
          final responseFileName = data['fileName'] ?? fileName;

          // تحديد نوع الملف
          final fileType = _getFileType(responseFileName);

          // استخراج كود الملف من رابط التحميل
          final fileCode = downloadPage.split('/').last;

          // استخراج اسم الخادم من البيانات أو من رابط التحميل
          String server = 'store1'; // القيمة الافتراضية
          if (data['server'] != null) {
            server = data['server'];
          } else if (downloadPage.isNotEmpty) {
            // استخراج الخادم من رابط التحميل
            final uri = Uri.parse(downloadPage);
            final hostParts = uri.host.split('.');
            if (hostParts.isNotEmpty) {
              server = hostParts[0];
            }
          }

          // بناء رابط مباشر للملف باستخدام معلومات الخادم
          // ملاحظة: الروابط المباشرة قد لا تعمل في الويب بسبب CORS
          final directLink =
              downloadPage; // استخدام رابط التحميل بدلاً من الرابط المباشر

          if (kDebugMode) {
            debugPrint('تم رفع الملف بنجاح: $downloadPage');
            debugPrint('الرابط المباشر: $directLink');
          }

          return {
            'success': true,
            'downloadPage': downloadPage,
            'directLink': directLink,
            'fileName': responseFileName,
            'fileId': fileId,
            'fileType': fileType,
            'server': server,
            'contentId': fileCode,
          };
        } else {
          final errorMessage = jsonResponse['data']?['error'] ??
              jsonResponse['status'] ??
              'خطأ غير معروف';
          throw Exception('فشل في رفع الملف: $errorMessage');
        }
      } else if (response.statusCode == 413) {
        throw Exception('حجم الملف كبير جداً');
      } else if (response.statusCode == 400) {
        throw Exception('بيانات الطلب غير صحيحة');
      } else if (response.statusCode == 401) {
        throw Exception('رمز المصادقة غير صحيح');
      } else if (response.statusCode == 429) {
        throw Exception('تم تجاوز الحد المسموح من الطلبات');
      } else {
        throw Exception(
            'فشل في رفع الملف: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الملف إلى GoFile: $e');
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// حذف ملف من GoFile
  static Future<bool> deleteFile(String fileId) async {
    try {
      final response = await http.delete(
        Uri.parse('https://api.gofile.io/deleteFile'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'token': _accountToken,
          'fileId': fileId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['status'] == 'ok';
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الملف من GoFile: $e');
      return false;
    }
  }

  /// التحقق من صحة الملف
  static bool isValidFile(Uint8List fileBytes) {
    // التحقق من الحد الأقصى للحجم (5GB)
    if (fileBytes.length > 5 * 1024 * 1024 * 1024) {
      return false;
    }

    // التحقق من أن الملف ليس فارغ
    if (fileBytes.isEmpty) {
      return false;
    }

    return true;
  }

  /// الحصول على نوع الملف بناءً على امتداده
  static String _getFileType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;

    // الصور
    if ([
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'bmp',
      'svg',
      'ico',
      'tiff',
      'tif'
    ].contains(extension)) {
      return 'image';
    }

    // الفيديو
    if ([
      'mp4',
      'avi',
      'mov',
      'wmv',
      'flv',
      'mkv',
      'webm',
      '3gp',
      'm4v',
      'mpg',
      'mpeg'
    ].contains(extension)) {
      return 'video';
    }

    // الصوت
    if (['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac', 'wma', 'amr', '3ga']
        .contains(extension)) {
      return 'audio';
    }

    // المستندات
    if ([
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
      'rtf',
      'odt',
      'ods',
      'odp',
      'csv'
    ].contains(extension)) {
      return 'document';
    }

    // ملفات مضغوطة
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(extension)) {
      return 'archive';
    }

    // أنواع أخرى
    return 'other';
  }

  /// إنشاء رابط مباشر للملف (للاستخدام الخارجي)
  static String createDirectLink(String downloadPage, String fileName,
      {String? server}) {
    try {
      final fileCode = downloadPage.split('/').last;
      final serverName = server ?? 'store1';
      return 'https://$serverName.gofile.io/download/$fileCode/${Uri.encodeComponent(fileName)}';
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في إنشاء الرابط المباشر: $e');
      }
      return downloadPage; // العودة لرابط التحميل الأصلي
    }
  }
}

/// إحصائيات استخدام GoFile
class GoFileStats {
  static int _uploadCount = 0;
  static int _successCount = 0;
  static int _errorCount = 0;
  static double _totalSizeGB = 0.0;

  static void recordUpload(bool success, double sizeGB) {
    _uploadCount++;
    _totalSizeGB += sizeGB;

    if (success) {
      _successCount++;
    } else {
      _errorCount++;
    }
  }

  static Map<String, dynamic> getStats() {
    return {
      'totalUploads': _uploadCount,
      'successfulUploads': _successCount,
      'failedUploads': _errorCount,
      'successRate':
          _uploadCount > 0 ? (_successCount / _uploadCount * 100) : 0.0,
      'totalSizeGB': _totalSizeGB,
      'averageSizeGB': _uploadCount > 0 ? (_totalSizeGB / _uploadCount) : 0.0,
    };
  }

  static void reset() {
    _uploadCount = 0;
    _successCount = 0;
    _errorCount = 0;
    _totalSizeGB = 0.0;
  }
}
