import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker_web/image_picker_web.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../Screen/Widgets/Constant Data/constant.dart';
import '../../model/advanced_notification_model.dart';

/// شاشة إنشاء رسالة متقدمة
class CreateAdvancedMessageScreen extends ConsumerStatefulWidget {
  final String? initialTargetType;
  final String? initialScheduleType;
  final AdminNotificationModel? editingNotification;

  const CreateAdvancedMessageScreen({
    super.key,
    this.initialTargetType,
    this.initialScheduleType,
    this.editingNotification,
  });

  @override
  ConsumerState<CreateAdvancedMessageScreen> createState() =>
      _CreateAdvancedMessageScreenState();
}

class _CreateAdvancedMessageScreenState
    extends ConsumerState<CreateAdvancedMessageScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  final _actionUrlController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _audioUrlController = TextEditingController();
  final _videoUrlController = TextEditingController();

  // State variables
  String _selectedType = AdvancedNotificationTypes.info;
  String _targetType = NotificationTarget.all;
  String _scheduleType = SchedulingOptions.immediate;
  DateTime? _scheduledTime;
  String? _recurringPattern;

  // Target specific data
  final List<String> _specificUserIds = [];
  final List<String> _specificDeviceIds = [];
  final List<String> _targetRegions = [];
  final List<String> _targetSubscriptionTypes = [];
  final List<String> _customGroups = [];

  // Image upload variables
  Uint8List? _selectedImageBytes;
  String? _uploadedImageUrl;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize with passed parameters
    if (widget.initialTargetType != null) {
      _targetType = widget.initialTargetType!;
    }
    if (widget.initialScheduleType != null) {
      _scheduleType = widget.initialScheduleType!;
    }

    // Load data for editing
    if (widget.editingNotification != null) {
      _loadEditingData();
    }
  }

  void _loadEditingData() {
    final notification = widget.editingNotification!;
    _titleController.text = notification.title;
    _messageController.text = notification.message;
    _selectedType = notification.type;
    _actionUrlController.text = notification.actionUrl ?? '';
    _imageUrlController.text = notification.imageUrl ?? '';
    _uploadedImageUrl = notification.imageUrl;
  }

  // دالة رفع الصورة
  Future<void> _uploadImage() async {
    if (kIsWeb) {
      try {
        setState(() {
          _isUploadingImage = true;
        });

        EasyLoading.show(
          status: 'جاري رفع الصورة...',
          dismissOnTap: false,
        );

        Uint8List? bytesFromPicker = await ImagePickerWeb.getImageAsBytes();

        if (bytesFromPicker != null) {
          // رفع الصورة إلى Firebase Storage
          var snapshot = await FirebaseStorage.instance
              .ref(
                  'Advanced Messages/${DateTime.now().millisecondsSinceEpoch}.jpg')
              .putData(
                bytesFromPicker,
                SettableMetadata(contentType: 'image/jpeg'),
              );

          var url = await snapshot.ref.getDownloadURL();

          setState(() {
            _selectedImageBytes = bytesFromPicker;
            _uploadedImageUrl = url;
            _imageUrlController.text = url;
          });

          EasyLoading.showSuccess('تم رفع الصورة بنجاح!');
        } else {
          EasyLoading.dismiss();
        }
      } catch (e) {
        EasyLoading.dismiss();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في رفع الصورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isUploadingImage = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _messageController.dispose();
    _actionUrlController.dispose();
    _imageUrlController.dispose();
    _audioUrlController.dispose();
    _videoUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'إنشاء رسالة متقدمة',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.preview, color: Colors.white),
            onPressed: _previewMessage,
            tooltip: 'معاينة',
          ),
          IconButton(
            icon: const Icon(Icons.send, color: Colors.white),
            onPressed: _sendMessage,
            tooltip: 'إرسال',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'المحتوى'),
            Tab(text: 'الاستهداف'),
            Tab(text: 'الجدولة'),
            Tab(text: 'المراجعة'),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Form(
          key: _formKey,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildContentTab(),
              _buildTargetingTab(),
              _buildSchedulingTab(),
              _buildReviewTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'محتوى الرسالة',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 24),

          // نوع الإشعار
          _buildNotificationTypeSelector(),
          const SizedBox(height: 20),

          // العنوان
          TextFormField(
            controller: _titleController,
            decoration: kInputDecoration.copyWith(
              labelText: 'عنوان الرسالة *',
              hintText: 'أدخل عنوان واضح وجذاب',
              prefixIcon: const Icon(Icons.title),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'عنوان الرسالة مطلوب';
              }
              if (value.trim().length < 3) {
                return 'العنوان يجب أن يكون 3 أحرف على الأقل';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // المحتوى
          TextFormField(
            controller: _messageController,
            maxLines: 4,
            decoration: kInputDecoration.copyWith(
              labelText: 'محتوى الرسالة *',
              hintText: 'اكتب محتوى الرسالة هنا...',
              prefixIcon: const Icon(Icons.message),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'محتوى الرسالة مطلوب';
              }
              if (value.trim().length < 10) {
                return 'المحتوى يجب أن يكون 10 أحرف على الأقل';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // المحتوى الإضافي
          _buildAdditionalContentSection(),
        ],
      ),
    );
  }

  Widget _buildNotificationTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الإشعار',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AdvancedNotificationTypes.all.map((type) {
            final isSelected = _selectedType == type;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedType = type;
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? kMainColor
                      : kMainColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: kMainColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getTypeIcon(type),
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      AdvancedNotificationTypes.getArabicName(type),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Colors.white : kMainColor,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAdditionalContentSection() {
    return ExpansionTile(
      title: Text(
        'محتوى إضافي (اختياري)',
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: kMainColor,
        ),
      ),
      children: [
        const SizedBox(height: 16),

        // رابط الإجراء
        TextFormField(
          controller: _actionUrlController,
          decoration: kInputDecoration.copyWith(
            labelText: 'رابط الإجراء',
            hintText: 'https://example.com',
            prefixIcon: const Icon(Icons.link),
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return 'رابط غير صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // قسم رفع الصورة
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.image, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    'صورة الرسالة',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // معاينة الصورة المرفوعة
              if (_selectedImageBytes != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      _selectedImageBytes!,
                      height: 150,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

              // أزرار رفع الصورة
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isUploadingImage ? null : _uploadImage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      icon: _isUploadingImage
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.upload),
                      label: Text(
                        _isUploadingImage ? 'جاري الرفع...' : 'رفع صورة',
                        style: GoogleFonts.cairo(),
                      ),
                    ),
                  ),
                  if (_uploadedImageUrl != null) ...[
                    const SizedBox(width: 12),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _selectedImageBytes = null;
                          _uploadedImageUrl = null;
                          _imageUrlController.clear();
                        });
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف الصورة',
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // حقل رابط الصورة (للإدخال اليدوي)
              TextFormField(
                controller: _imageUrlController,
                decoration: kInputDecoration.copyWith(
                  labelText: 'أو أدخل رابط الصورة يدوياً',
                  hintText: 'https://example.com/image.jpg',
                  prefixIcon: const Icon(Icons.link),
                ),
                onChanged: (value) {
                  if (value.isNotEmpty && _selectedImageBytes == null) {
                    setState(() {
                      _uploadedImageUrl = value;
                    });
                  }
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // رابط الصوت
        TextFormField(
          controller: _audioUrlController,
          decoration: kInputDecoration.copyWith(
            labelText: 'رابط الملف الصوتي',
            hintText: 'https://example.com/audio.mp3',
            prefixIcon: const Icon(Icons.audiotrack),
          ),
        ),
        const SizedBox(height: 16),

        // رابط الفيديو
        TextFormField(
          controller: _videoUrlController,
          decoration: kInputDecoration.copyWith(
            labelText: 'رابط الفيديو',
            hintText: 'https://example.com/video.mp4',
            prefixIcon: const Icon(Icons.video_library),
          ),
        ),
      ],
    );
  }

  Widget _buildTargetingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'استهداف الجمهور',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 24),

          // اختيار نوع الاستهداف
          _buildTargetTypeSelector(),
          const SizedBox(height: 20),

          // خيارات الاستهداف المحددة
          _buildSpecificTargetingOptions(),
        ],
      ),
    );
  }

  Widget _buildSchedulingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'جدولة الإرسال',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 24),

          // نوع الجدولة
          _buildScheduleTypeSelector(),
          const SizedBox(height: 20),

          // خيارات الجدولة المحددة
          _buildSpecificSchedulingOptions(),
        ],
      ),
    );
  }

  Widget _buildReviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مراجعة الرسالة',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 24),

          // معاينة الرسالة
          _buildMessagePreview(),
          const SizedBox(height: 20),

          // ملخص الاستهداف
          _buildTargetingSummary(),
          const SizedBox(height: 20),

          // ملخص الجدولة
          _buildSchedulingSummary(),
          const SizedBox(height: 32),

          // أزرار الإجراء
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildTargetTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الاستهداف',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 12),

        // قائمة أنواع الاستهداف
        _buildTargetOption(
          NotificationTarget.all,
          'جميع المستخدمين',
          'إرسال لجميع مستخدمي التطبيق',
          Icons.people,
        ),
        _buildTargetOption(
          NotificationTarget.specificUsers,
          'مستخدمين محددين',
          'اختيار مستخدمين بالاسم أو ID',
          Icons.person_pin,
        ),
        _buildTargetOption(
          NotificationTarget.specificDevices,
          'أجهزة محددة',
          'استهداف أجهزة معينة',
          Icons.devices,
        ),
        _buildTargetOption(
          NotificationTarget.regions,
          'مناطق جغرافية',
          'استهداف مناطق أو مدن',
          Icons.location_on,
        ),
        _buildTargetOption(
          NotificationTarget.subscriptionTypes,
          'أنواع اشتراك',
          'حسب نوع الاشتراك',
          Icons.card_membership,
        ),
        _buildTargetOption(
          NotificationTarget.customGroups,
          'مجموعات مخصصة',
          'مجموعات تم إنشاؤها مسبقاً',
          Icons.group,
        ),
      ],
    );
  }

  Widget _buildTargetOption(
    String value,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = _targetType == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _targetType = value;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? kMainColor.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? kMainColor : Colors.grey,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? kMainColor : Colors.grey,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? kMainColor : Colors.grey[700],
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: kMainColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecificTargetingOptions() {
    if (_targetType == NotificationTarget.all) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.info, color: Colors.green),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'سيتم إرسال الرسالة لجميع مستخدمي التطبيق',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.green[700],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.construction, color: Colors.orange, size: 32),
          const SizedBox(height: 8),
          Text(
            'خيارات الاستهداف المحددة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          Text(
            'قيد التطوير - سيتم إضافتها قريباً',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.orange[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الجدولة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 12),
        _buildScheduleOption(
          SchedulingOptions.immediate,
          'إرسال فوري',
          'إرسال الرسالة الآن مباشرة',
          Icons.send,
        ),
        _buildScheduleOption(
          SchedulingOptions.scheduled,
          'إرسال مجدول',
          'تحديد تاريخ ووقت محدد',
          Icons.calendar_today,
        ),
        _buildScheduleOption(
          SchedulingOptions.recurring,
          'إرسال متكرر',
          'إرسال يومي أو أسبوعي أو شهري',
          Icons.repeat,
        ),
      ],
    );
  }

  Widget _buildScheduleOption(
    String value,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = _scheduleType == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _scheduleType = value;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? kMainColor.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? kMainColor : Colors.grey,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? kMainColor : Colors.grey,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? kMainColor : Colors.grey[700],
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: kMainColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecificSchedulingOptions() {
    if (_scheduleType == SchedulingOptions.immediate) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.flash_on, color: Colors.green),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'سيتم إرسال الرسالة فور الضغط على زر الإرسال',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.green[700],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.construction, color: Colors.orange, size: 32),
          const SizedBox(height: 8),
          Text(
            'خيارات الجدولة المتقدمة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          Text(
            'قيد التطوير - سيتم إضافتها قريباً',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.orange[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagePreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: kMainColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: kMainColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getTypeIcon(_selectedType),
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Text(
                _titleController.text.isEmpty
                    ? 'عنوان الرسالة'
                    : _titleController.text,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _messageController.text.isEmpty
                ? 'محتوى الرسالة'
                : _messageController.text,
            style: GoogleFonts.cairo(fontSize: 14),
          ),
          const SizedBox(height: 8),

          // معاينة الصورة في المراجعة
          if (_uploadedImageUrl != null && _uploadedImageUrl!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _selectedImageBytes != null
                    ? Image.memory(
                        _selectedImageBytes!,
                        height: 120,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      )
                    : Image.network(
                        _uploadedImageUrl!,
                        height: 120,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 120,
                            color: Colors.grey[200],
                            child: const Center(
                              child:
                                  Icon(Icons.broken_image, color: Colors.grey),
                            ),
                          );
                        },
                      ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          Text(
            'النوع: ${AdvancedNotificationTypes.getArabicName(_selectedType)}',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTargetingSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الاستهداف',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getTargetDescription(),
            style: GoogleFonts.cairo(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulingSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الجدولة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getScheduleDescription(),
            style: GoogleFonts.cairo(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: kMainColor),
            ),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: kMainColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _sendMessage,
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              'إرسال الرسالة',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getTypeIcon(String type) {
    switch (type) {
      case 'emergency':
        return '🚨';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'maintenance':
        return '🔧';
      case 'update':
        return '🔄';
      case 'promotional':
        return '🎉';
      case 'survey':
        return '📊';
      case 'announcement':
        return '📢';
      default:
        return '📢';
    }
  }

  String _getTargetDescription() {
    switch (_targetType) {
      case NotificationTarget.all:
        return 'جميع مستخدمي التطبيق';
      case NotificationTarget.specificUsers:
        return 'مستخدمين محددين (${_specificUserIds.length} مستخدم)';
      case NotificationTarget.specificDevices:
        return 'أجهزة محددة (${_specificDeviceIds.length} جهاز)';
      case NotificationTarget.regions:
        return 'مناطق جغرافية (${_targetRegions.length} منطقة)';
      case NotificationTarget.subscriptionTypes:
        return 'أنواع اشتراك (${_targetSubscriptionTypes.length} نوع)';
      case NotificationTarget.customGroups:
        return 'مجموعات مخصصة (${_customGroups.length} مجموعة)';
      default:
        return 'غير محدد';
    }
  }

  String _getScheduleDescription() {
    switch (_scheduleType) {
      case SchedulingOptions.immediate:
        return 'إرسال فوري';
      case SchedulingOptions.scheduled:
        return _scheduledTime != null
            ? 'مجدول في: ${_formatDate(_scheduledTime!)}'
            : 'إرسال مجدول (لم يتم تحديد الوقت)';
      case SchedulingOptions.recurring:
        return _recurringPattern != null
            ? 'إرسال متكرر: $_recurringPattern'
            : 'إرسال متكرر (لم يتم تحديد النمط)';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _previewMessage() {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'معاينة الرسالة',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
        content: _buildMessagePreview(),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    //  تنفيذ إرسال الرسالة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الإرسال',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
        content: Text(
          'هل أنت متأكد من إرسال هذه الرسالة؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الرسالة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: kMainColor),
            child: Text(
              'إرسال',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
