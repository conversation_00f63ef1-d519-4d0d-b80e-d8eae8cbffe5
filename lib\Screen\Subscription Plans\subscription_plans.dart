// ignore_for_file: unused_result

import 'dart:convert';
import 'package:responsive_framework/responsive_framework.dart' as rf;
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:salespro_saas_admin/Provider/subacription_plan_provider.dart';
import 'package:salespro_saas_admin/Screen/Widgets/Constant%20Data/transparent_button.dart';
import 'package:salespro_saas_admin/Screen/Widgets/static_string/static_string.dart';
import 'package:salespro_saas_admin/model/subscription_plan_model.dart';
import '../../currency.dart';
import '../Widgets/Constant Data/constant.dart';
import '../Widgets/Constant Data/export_button.dart';

class SubscriptionPlans extends StatefulWidget {
  const SubscriptionPlans({super.key});

  static const String route = '/subscription_plans';

  @override
  State<SubscriptionPlans> createState() => _SubscriptionPlansState();
}

class _SubscriptionPlansState extends State<SubscriptionPlans> {
  void newSubscriptionPlanAdd(
      {required WidgetRef ref, required List<String> allNames}) {
    GlobalKey<FormState> globalKey = GlobalKey<FormState>();
    bool saleUnlimited = false;
    bool purchaseUnlimited = false;
    bool partisUnlimited = false;
    bool dueUnlimited = false;
    bool productUnlimited = false;
    SubscriptionPlanModel subscriptionPlansModel = SubscriptionPlanModel(
      subscriptionName: '',
      saleNumber: 0,
      purchaseNumber: 0,
      partiesNumber: 0,
      dueNumber: 0,
      duration: 0,
      products: 0,
      subscriptionPrice: 0,
      offerPrice: 0,
    );
    TextEditingController saleLimitController = TextEditingController();
    TextEditingController purchaseLimitController = TextEditingController();
    TextEditingController partisLimitController = TextEditingController();
    TextEditingController dueLimitController = TextEditingController();
    TextEditingController productLimitController = TextEditingController();

    bool validateAndSave() {
      final form = globalKey.currentState;
      if (form!.validate()) {
        form.save();
        return true;
      }
      return false;
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        final kMobileSize = MediaQuery.of(context).size.width < 490;
        return StatefulBuilder(builder: (context, setState1) {
          return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: SingleChildScrollView(
                child: SizedBox(
                  width: 600,
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: globalKey,
                          child: Column(
                            children: [
                              ///________Name__________________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'اسم الباقة',
                                    hintText: 'اكتب اسم الباقة.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'اسم باقة الاشتراك مطلوب.';
                                    } else if (allNames.contains(value
                                        ?.toLowerCase()
                                        .removeAllWhiteSpace())) {
                                      return 'اسم الباقة موجود فعلاً.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    subscriptionPlansModel.subscriptionName =
                                        value!;
                                  },
                                ),
                              ),

                              ///__________Price & Offer Price_______________________________
                              Row(
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        validator: (value) {
                                          if (value.isEmptyOrNull) {
                                            return 'سعر الباقة مطلوب';
                                          }
                                          return null;
                                        },
                                        onSaved: (value) {
                                          subscriptionPlansModel
                                                  .subscriptionPrice =
                                              value.toInt();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر الباقة',
                                          hintText: 'اكتب السعر العادي للباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        validator: (value) {
                                          return null;
                                        },
                                        onSaved: (value) {
                                          subscriptionPlansModel.offerPrice =
                                              value.toInt();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر العرض',
                                          hintText: 'اكتب سعر عرض الباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              ///__________timer duration____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'مدة الوقت بالأيام',
                                    hintText: 'اكتب مدة الوقت بالأيام.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'مدة الوقت مطلوبة.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    subscriptionPlansModel.duration =
                                        value.toInt();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),

                              ///__________sale_Limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: saleUnlimited,
                                    controller: saleLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.saleNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد المبيعات',
                                      hintText: 'اكتب حد المبيعات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: saleUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        saleUnlimited = value!;
                                                        value
                                                            ? saleLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : saleLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________Purchase_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: purchaseUnlimited,
                                    controller: purchaseLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.purchaseNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد المشتريات',
                                      hintText: 'اكتب حد المشتريات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: purchaseUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        purchaseUnlimited =
                                                            value!;
                                                        value
                                                            ? purchaseLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : purchaseLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________parties_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: partisUnlimited,
                                    controller: partisLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.partiesNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد الأطراف',
                                      hintText: 'اكتب حد الأطراف.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: partisUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        partisUnlimited =
                                                            value!;
                                                        value
                                                            ? partisLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : partisLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________due_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: dueUnlimited,
                                    controller: dueLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.dueNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد تحصيل المستحقات',
                                      hintText: 'اكتب حد تحصيل المستحقات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: dueUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        dueUnlimited = value!;
                                                        value
                                                            ? dueLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : dueLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________product_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: productUnlimited,
                                    controller: productLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.products =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد إضافة المنتجات',
                                      hintText: 'اكتب حد إضافة المنتجات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: productUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        productUnlimited =
                                                            value!;
                                                        value
                                                            ? productLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : productLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///_______buttons__________________________________
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: (() => Navigator.pop(context)),
                              child: Container(
                                width: kMobileSize ? 80 : 150,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: Colors.red),
                                child: SizedBox(
                                  child: Column(
                                    children: [
                                      Text(
                                        'إلغاء',
                                        style: kTextStyle.copyWith(
                                            color: kWhiteTextColor),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),
                            GestureDetector(
                              onTap: () async {
                                if (validateAndSave()) {
                                  EasyLoading.show(
                                      status: 'بيحمل...', dismissOnTap: false);
                                  final DatabaseReference adRef =
                                      FirebaseDatabase.instance
                                          .ref()
                                          .child('Admin Panel')
                                          .child('Subscription Plan');

                                  subscriptionPlansModel.dueNumber == 0
                                      ? subscriptionPlansModel.dueNumber = -202
                                      : null;
                                  subscriptionPlansModel.saleNumber == 0
                                      ? subscriptionPlansModel.saleNumber = -202
                                      : null;
                                  subscriptionPlansModel.products == 0
                                      ? subscriptionPlansModel.products = -202
                                      : null;
                                  subscriptionPlansModel.purchaseNumber == 0
                                      ? subscriptionPlansModel.purchaseNumber =
                                          -202
                                      : null;
                                  subscriptionPlansModel.partiesNumber == 0
                                      ? subscriptionPlansModel.partiesNumber =
                                          -202
                                      : null;
                                  await adRef
                                      .push()
                                      .set(subscriptionPlansModel.toJson());
                                  EasyLoading.showSuccess('تم الإضافة بنجاح',
                                      duration:
                                          const Duration(milliseconds: 500));

                                  ///____provider_refresh____________________________________________
                                  ref.refresh(subscriptionPlanProvider);

                                  Future.delayed(
                                      const Duration(milliseconds: 100), () {
                                    if (context.mounted) Navigator.pop(context);
                                  });
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                width: kMobileSize ? 80 : 150,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5.0),
                                    color: kBlueTextColor),
                                child: Text(
                                  'حفظ',
                                  style: kTextStyle.copyWith(
                                      color: kWhiteTextColor),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ));
        });
      },
    );
  }

  Future<void> deletePlan(
      {required WidgetRef updateRef, required String name}) async {
    showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext dialogContext) {
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Center(
              child: Container(
                width: 400,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(15),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'عاوز تحذف الإعلان ده؟',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 22),
                      ),
                      const SizedBox(height: 30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Expanded(
                            child: GestureDetector(
                              child: Container(
                                height: 50,
                                decoration: const BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(15),
                                  ),
                                ),
                                child: const Center(
                                  child: Text(
                                    'إلغاء',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                              onTap: () {
                                Navigator.pop(dialogContext);
                              },
                            ),
                          ),
                          const SizedBox(width: 30),
                          Expanded(
                            child: GestureDetector(
                              child: Container(
                                height: 50,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(15),
                                  ),
                                ),
                                child: const Center(
                                  child: Text(
                                    'حذف',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                              onTap: () async {
                                if (!isDemo) {
                                  EasyLoading.show(status: 'بيحذف..');
                                  String imageKey = '';
                                  await FirebaseDatabase.instance
                                      .ref()
                                      .child('Admin Panel')
                                      .child('Subscription Plan')
                                      .orderByKey()
                                      .get()
                                      .then((value) async {
                                    for (var element in value.children) {
                                      var data =
                                          jsonDecode(jsonEncode(element.value));
                                      if (data['subscriptionName'].toString() ==
                                          name) {
                                        imageKey = element.key.toString();
                                      }
                                    }
                                  });
                                  DatabaseReference ref =
                                      FirebaseDatabase.instance.ref(
                                          "Admin Panel/Subscription Plan/$imageKey");
                                  await ref.remove();
                                  updateRef.refresh(subscriptionPlanProvider);

                                  EasyLoading.showSuccess('تم');
                                  // ignore: use_build_context_synchronously
                                  Navigator.pop(dialogContext);
                                } else {
                                  EasyLoading.showInfo(demoText);
                                }
                              },
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }

  void editSubscriptionPlan(
      {required WidgetRef updateRef,
      required List<String> allNames,
      required SubscriptionPlanModel selectedOne}) {
    GlobalKey<FormState> globalKey = GlobalKey<FormState>();
    bool saleUnlimited = selectedOne.saleNumber == -202 ? true : false;
    bool purchaseUnlimited = selectedOne.purchaseNumber == -202 ? true : false;
    bool partisUnlimited = selectedOne.partiesNumber == -202 ? true : false;
    bool dueUnlimited = selectedOne.dueNumber == -202 ? true : false;
    bool productUnlimited = selectedOne.products == -202 ? true : false;
    SubscriptionPlanModel subscriptionPlansModel = SubscriptionPlanModel(
      subscriptionName: '',
      saleNumber: 0,
      purchaseNumber: 0,
      partiesNumber: 0,
      dueNumber: 0,
      duration: 0,
      products: 0,
      subscriptionPrice: 0,
      offerPrice: 0,
    );

    TextEditingController saleLimitController = TextEditingController(
        text: selectedOne.saleNumber == -202
            ? 'غير محدود'
            : selectedOne.saleNumber.toString());
    TextEditingController purchaseLimitController = TextEditingController(
        text: selectedOne.purchaseNumber == -202
            ? 'غير محدود'
            : selectedOne.purchaseNumber.toString());
    TextEditingController partisLimitController = TextEditingController(
        text: selectedOne.partiesNumber == -202
            ? 'غير محدود'
            : selectedOne.partiesNumber.toString());
    TextEditingController dueLimitController = TextEditingController(
        text: selectedOne.dueNumber == -202
            ? 'غير محدود'
            : selectedOne.dueNumber.toString());
    TextEditingController productLimitController = TextEditingController(
        text: selectedOne.products == -202
            ? 'غير محدود'
            : selectedOne.products.toString());

    bool validateAndSave() {
      final form = globalKey.currentState;
      if (form!.validate()) {
        form.save();
        return true;
      }
      return false;
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState1) {
          return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: SingleChildScrollView(
                child: SizedBox(
                  width: 600,
                  child: Padding(
                    padding: const EdgeInsets.all(30.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Form(
                          key: globalKey,
                          child: Column(
                            children: [
                              ///________Name__________________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  initialValue: selectedOne.subscriptionName,
                                  readOnly:
                                      selectedOne.subscriptionName == 'Free'
                                          ? true
                                          : false,
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'اسم الباقة',
                                    hintText: 'اكتب اسم الباقة.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'اسم باقة الاشتراك مطلوب.';
                                    } else if (allNames.contains(value
                                            ?.toLowerCase()
                                            .removeAllWhiteSpace()) &&
                                        selectedOne.subscriptionName != value) {
                                      return 'اسم الباقة موجود فعلاً.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    subscriptionPlansModel.subscriptionName =
                                        value!;
                                  },
                                ),
                              ),

                              ///__________Price & Offer Price_______________________________
                              Row(
                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        readOnly:
                                            selectedOne.subscriptionName ==
                                                    'Free'
                                                ? true
                                                : false,
                                        initialValue: selectedOne
                                            .subscriptionPrice
                                            .toString(),
                                        validator: (value) {
                                          if (value.isEmptyOrNull) {
                                            return 'سعر الباقة مطلوب';
                                          }
                                          return null;
                                        },
                                        onSaved: (value) {
                                          subscriptionPlansModel
                                                  .subscriptionPrice =
                                              value.toInt();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر الباقة',
                                          hintText: 'اكتب السعر العادي للباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: TextFormField(
                                        readOnly:
                                            selectedOne.subscriptionName ==
                                                    'Free'
                                                ? true
                                                : false,
                                        initialValue:
                                            selectedOne.offerPrice.toString(),
                                        validator: (value) {
                                          return null;
                                        },
                                        onSaved: (value) {
                                          subscriptionPlansModel.offerPrice =
                                              value.toInt();
                                        },
                                        decoration: const InputDecoration(
                                          floatingLabelBehavior:
                                              FloatingLabelBehavior.always,
                                          labelText: 'سعر العرض',
                                          hintText: 'اكتب سعر عرض الباقة.',
                                          border: OutlineInputBorder(),
                                        ),
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),

                              ///__________timer duration____________________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  initialValue: selectedOne.duration.toString(),
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    border: OutlineInputBorder(),
                                    labelText: 'مدة الوقت بالأيام',
                                    hintText: 'اكتب مدة الوقت بالأيام.',
                                  ),
                                  validator: (value) {
                                    if (value.isEmptyOrNull) {
                                      return 'مدة الوقت مطلوبة.';
                                    }
                                    return null;
                                  },
                                  onSaved: (value) {
                                    subscriptionPlansModel.duration =
                                        value.toInt();
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                  ],
                                ),
                              ),

                              ///__________sale_Limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: saleUnlimited,
                                    controller: saleLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.saleNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد المبيعات',
                                      hintText: 'اكتب حد المبيعات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: saleUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        saleUnlimited = value!;
                                                        value
                                                            ? saleLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : saleLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________Purchase_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: purchaseUnlimited,
                                    controller: purchaseLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.purchaseNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد المشتريات',
                                      hintText: 'اكتب حد المشتريات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: purchaseUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        purchaseUnlimited =
                                                            value!;
                                                        value
                                                            ? purchaseLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : purchaseLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________parties_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: partisUnlimited,
                                    controller: partisLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.partiesNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد الأطراف',
                                      hintText: 'اكتب حد الأطراف.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: partisUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        partisUnlimited =
                                                            value!;
                                                        value
                                                            ? partisLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : partisLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________due_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: dueUnlimited,
                                    controller: dueLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.dueNumber =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد تحصيل المستحقات',
                                      hintText: 'اكتب حد تحصيل المستحقات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: dueUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        dueUnlimited = value!;
                                                        value
                                                            ? dueLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : dueLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),

                              ///__________product_limit_______________________________
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SizedBox(
                                  height: 50,
                                  child: TextFormField(
                                    readOnly: productUnlimited,
                                    controller: productLimitController,
                                    validator: (value) {
                                      return null;
                                    },
                                    onSaved: (value) {
                                      subscriptionPlansModel.products =
                                          value.toInt();
                                    },
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: 'حد إضافة المنتجات',
                                      hintText: 'اكتب حد إضافة المنتجات.',
                                      border: const OutlineInputBorder(),
                                      suffix: SizedBox(
                                        width: 120,
                                        child: Center(
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              children: [
                                                const Text('غير محدود'),
                                                Checkbox(
                                                    value: productUnlimited,
                                                    onChanged: (value) {
                                                      setState1(() {
                                                        productUnlimited =
                                                            value!;
                                                        value
                                                            ? productLimitController
                                                                    .text =
                                                                'غير محدود'
                                                            : productLimitController
                                                                .text = '';
                                                      });
                                                    }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        ///_______buttons__________________________________
                        const SizedBox(height: 20),
                        // const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: (() => Navigator.pop(context)),
                                child: Container(
                                  padding: const EdgeInsets.all(10.0),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5.0),
                                      color: Colors.red),
                                  child: Column(
                                    children: [
                                      Text(
                                        'إلغاء',
                                        style: kTextStyle.copyWith(
                                            color: kWhiteTextColor),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),
                            Expanded(
                              child: GestureDetector(
                                onTap: () async {
                                  if (!isDemo) {
                                    if (validateAndSave()) {
                                      subscriptionPlansModel.dueNumber == 0
                                          ? subscriptionPlansModel.dueNumber =
                                              -202
                                          : null;
                                      subscriptionPlansModel.saleNumber == 0
                                          ? subscriptionPlansModel.saleNumber =
                                              -202
                                          : null;
                                      subscriptionPlansModel.products == 0
                                          ? subscriptionPlansModel.products =
                                              -202
                                          : null;
                                      subscriptionPlansModel.purchaseNumber == 0
                                          ? subscriptionPlansModel
                                              .purchaseNumber = -202
                                          : null;
                                      subscriptionPlansModel.partiesNumber == 0
                                          ? subscriptionPlansModel
                                              .partiesNumber = -202
                                          : null;

                                      EasyLoading.show(status: 'بيعدل');
                                      String imageKey = '';
                                      await FirebaseDatabase.instance
                                          .ref()
                                          .child('Admin Panel')
                                          .child('Subscription Plan')
                                          .orderByKey()
                                          .get()
                                          .then((value) async {
                                        for (var element in value.children) {
                                          var data = jsonDecode(
                                              jsonEncode(element.value));
                                          if (data['subscriptionName']
                                                  .toString() ==
                                              selectedOne.subscriptionName) {
                                            imageKey = element.key.toString();
                                          }
                                        }
                                      });
                                      DatabaseReference ref =
                                          FirebaseDatabase.instance.ref(
                                              "Admin Panel/Subscription Plan/$imageKey");
                                      await ref.update({
                                        'subscriptionName':
                                            subscriptionPlansModel
                                                .subscriptionName,
                                        'subscriptionPrice':
                                            subscriptionPlansModel
                                                .subscriptionPrice,
                                        'saleNumber':
                                            subscriptionPlansModel.saleNumber,
                                        'purchaseNumber': subscriptionPlansModel
                                            .purchaseNumber,
                                        'partiesNumber': subscriptionPlansModel
                                            .partiesNumber,
                                        'dueNumber':
                                            subscriptionPlansModel.dueNumber,
                                        'duration':
                                            subscriptionPlansModel.duration,
                                        'products': subscriptionPlansModel
                                            .partiesNumber,
                                        'offerPrice':
                                            subscriptionPlansModel.offerPrice,
                                      });
                                      EasyLoading.showSuccess(
                                          'تم التحديث بنجاح!');

                                      ///____provider_refresh____________________________________________
                                      updateRef
                                          .refresh(subscriptionPlanProvider);

                                      Future.delayed(
                                          const Duration(milliseconds: 100),
                                          () {
                                        if (context.mounted) {
                                          Navigator.pop(context);
                                        }
                                      });
                                    }
                                  } else {
                                    EasyLoading.showInfo(demoText);
                                  }
                                },
                                child: Container(
                                  width: 100,
                                  padding: const EdgeInsets.all(10.0),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5.0),
                                      color: kBlueTextColor),
                                  child: Column(
                                    children: [
                                      Text(
                                        'حفظ',
                                        style: kTextStyle.copyWith(
                                            color: kWhiteTextColor),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ));
        });
      },
    );
  }

  void postFreePlan({required WidgetRef ref}) async {
    SubscriptionPlanModel subscriptionPlansModel = SubscriptionPlanModel(
        subscriptionName: 'مجاني',
        saleNumber: 50,
        purchaseNumber: 50,
        partiesNumber: 50,
        dueNumber: 50,
        duration: 30,
        products: 50,
        subscriptionPrice: 0,
        offerPrice: 0);
    final DatabaseReference adRef = FirebaseDatabase.instance
        .ref()
        .child('Admin Panel')
        .child('Subscription Plan');
    await adRef.push().set(subscriptionPlansModel.toJson());

    ///____provider_refresh____________________________________________
    ref.refresh(subscriptionPlanProvider);
  }

  int counter = 0;

  @override
  void initState() {
    super.initState();
    checkCurrentUserAndRestartApp();
  }

  @override
  Widget build(BuildContext context) {
    bool isMobileScreen = rf.ResponsiveValue<bool>(context,
        defaultValue: false,
        conditionalValues: [
          rf.Condition.smallerThan(name: BreakpointName.SM.name, value: true)
        ]).value;
    bool isTabScreen = rf.ResponsiveValue<bool>(context,
        defaultValue: false,
        conditionalValues: [
          rf.Condition.smallerThan(name: BreakpointName.LG.name, value: true)
        ]).value;

    final kWidth = isMobileScreen
        ? MediaQuery.of(context).size.width / 1.2
        : isTabScreen
            ? MediaQuery.of(context).size.width / 2.2
            : MediaQuery.of(context).size.width / 6;

    final screenTextSize = rf.ResponsiveValue<bool>(context,
        defaultValue: false,
        conditionalValues: [
          const rf.Condition.between(start: 320, end: 450, value: true),
        ]).value;
    final kPlanButton = rf.ResponsiveValue<bool>(context,
        defaultValue: false,
        conditionalValues: [
          const rf.Condition.between(start: 320, end: 360, value: true),
        ]).value;
    return Scaffold(
      body: Consumer(
        builder: (_, ref, watch) {
          final reports = ref.watch(subscriptionPlanProvider);
          return reports.when(data: (data) {
            List<String> names = [];
            for (var element in data) {
              names.add(
                  element.subscriptionName.removeAllWhiteSpace().toLowerCase());
            }
            if (data.isNotEmpty) {
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Container(
                    // height: MediaQuery.of(context).size.height-160,
                    padding: const EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.0),
                        color: Colors.white),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'باقات الاشتراك',
                              style: kTextStyle.copyWith(
                                  color: kTitleColor,
                                  fontSize: screenTextSize ? 18 : 22,
                                  fontWeight: FontWeight.bold),
                            ),
                            const Spacer(),
                            GlobalTransparentButton(
                              buttonText: screenTextSize
                                  ? kPlanButton
                                      ? 'باقة'
                                      : 'باقة جديدة'
                                  : 'باقة اشتراك',
                              onpressed: (() => newSubscriptionPlanAdd(
                                  ref: ref, allNames: names)),
                            )
                            // GestureDetector(
                            //   onTap: (() => newSubscriptionPlanAdd(
                            //       ref: ref, allNames: names)),
                            //   child: Container(
                            //     padding: const EdgeInsets.all(5.0),
                            //     decoration: BoxDecoration(
                            //         borderRadius: BorderRadius.circular(5.0),
                            //         color: kBlueTextColor),
                            //     child: Column(
                            //       children: [
                            //         Text(
                            //           'Add New Subscription Plan',
                            //           style: kTextStyle.copyWith(
                            //               color: kWhiteTextColor),
                            //         ),
                            //       ],
                            //     ),
                            //   ),
                            // )
                          ],
                        ),
                        const SizedBox(height: 10.0),
                        const Divider(
                          height: 1,
                          color: Colors.black12,
                        ),
                        const SizedBox(height: 10.0),
                        Row(
                          children: [
                            SizedBox(
                              height: 40,
                              width: MediaQuery.of(context).size.width * .25,
                              child: TextField(
                                showCursor: true,
                                cursorColor: kTitleColor,
                                decoration: kInputDecoration.copyWith(
                                  hintText: 'ابحث عن أي حاجة...',
                                  suffixIcon: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        color: kBlueTextColor,
                                      ),
                                      child: const Icon(FeatherIcons.search,
                                          color: kWhiteTextColor),
                                    ),
                                  ),
                                  hintStyle:
                                      kTextStyle.copyWith(color: kLitGreyColor),
                                  contentPadding: const EdgeInsets.all(4.0),
                                  enabledBorder: const OutlineInputBorder(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(8.0),
                                    ),
                                    borderSide: BorderSide(
                                        color: kBorderColorTextField, width: 1),
                                  ),
                                ),
                              ),
                            ),
                            const Spacer(),
                            const ExportButton()
                          ],
                        ).visible(false),
                        const SizedBox(height: 10.0).visible(false),
                        GridView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                  childAspectRatio: kWidth / 370,
                                  crossAxisSpacing: 16,
                                  mainAxisSpacing: 16,
                                  crossAxisCount: isMobileScreen
                                      ? 1
                                      : isTabScreen
                                          ? 2
                                          : 4),
                          // scrollDirection: Axis.horizontal,
                          itemCount: data.length,
                          itemBuilder: (BuildContext context, int index) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Stack(
                                    alignment: Alignment.bottomCenter,
                                    children: [
                                      Container(
                                        height: 296,
                                        width: isMobileScreen
                                            ? MediaQuery.of(context)
                                                    .size
                                                    .width /
                                                1.4
                                            : isTabScreen
                                                ? MediaQuery.of(context)
                                                        .size
                                                        .width /
                                                    2.2
                                                : MediaQuery.of(context)
                                                        .size
                                                        .width /
                                                    4.2,
                                        decoration: BoxDecoration(
                                          color: kGreyTextColor.withValues(
                                              alpha: 0.1),
                                          borderRadius: const BorderRadius.all(
                                            Radius.circular(10),
                                          ),
                                          border: Border.all(
                                            width: 1,
                                            color: kBlueTextColor,
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            const SizedBox(height: 15),
                                            const Text(
                                              'Mobile App \n+\nDesktop',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontSize: 16,
                                              ),
                                            ),
                                            const SizedBox(height: 15),
                                            Text(
                                              data[index].subscriptionName,
                                              style: const TextStyle(
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.bold,
                                                  color: kGreyTextColor),
                                            ),
                                            const SizedBox(height: 5),
                                            Text(
                                              '$currency${data[index].offerPrice > 0 ? data[index].offerPrice : data[index].subscriptionPrice}',
                                              style: const TextStyle(
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.bold,
                                                  color: kGreyTextColor),
                                            ),
                                            Text(
                                              '$currency${data[index].subscriptionPrice}',
                                              style: const TextStyle(
                                                  decoration: TextDecoration
                                                      .lineThrough,
                                                  fontSize: 14,
                                                  color: Colors.grey),
                                            ).visible(
                                                data[index].offerPrice > 0),
                                            const SizedBox(height: 5),
                                            Text(
                                              'Duration ${data[index].duration} Day',
                                              style: const TextStyle(
                                                  color: kGreyTextColor),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Positioned(
                                        top: 0,
                                        left: 0,
                                        child: Container(
                                          height: 25,
                                          width: 70,
                                          decoration: const BoxDecoration(
                                            color: kBlueTextColor,
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(10),
                                              bottomRight: Radius.circular(10),
                                            ),
                                          ),
                                          child: Center(
                                            child: Text(
                                              data[index].offerPrice ==
                                                      data[index]
                                                          .subscriptionPrice
                                                  ? ""
                                                  : 'Save ${(100 - ((data[index].offerPrice * 100) / data[index].subscriptionPrice)).toInt().toString()}%',
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ),
                                          ),
                                        ),
                                      ).visible(data[index].offerPrice > 0),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            editSubscriptionPlan(
                                                updateRef: ref,
                                                allNames: names,
                                                selectedOne: data[index]);
                                          },
                                          child: Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  const BorderRadius.all(
                                                      Radius.circular(90)),
                                              border: Border.all(
                                                  width: 1,
                                                  color: kBlueTextColor),
                                              color: kBlueTextColor.withValues(
                                                  alpha: 0.1),
                                            ),
                                            child: const Center(
                                              child: Icon(
                                                Icons.edit,
                                                color: kBlueTextColor,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 20).visible(
                                            data[index].subscriptionName !=
                                                'Free'),
                                        GestureDetector(
                                          onTap: () {
                                            deletePlan(
                                                updateRef: ref,
                                                name: data[index]
                                                    .subscriptionName);
                                          },
                                          child: Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  const BorderRadius.all(
                                                      Radius.circular(90)),
                                              border: Border.all(
                                                  width: 1,
                                                  color: Colors.redAccent),
                                              color: Colors.redAccent
                                                  .withValues(alpha: 0.1),
                                            ),
                                            child: const Center(
                                              child: Icon(
                                                Icons.delete_forever,
                                                color: Colors.redAccent,
                                              ),
                                            ),
                                          ),
                                        ).visible(
                                            data[index].subscriptionName !=
                                                'Free'),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              counter == 0 ? postFreePlan(ref: ref) : null;
              counter++;
              return Container();
            }
          }, error: (e, stack) {
            return Center(
              child: Text(e.toString()),
            );
          }, loading: () {
            return const Center(
              child: CircularProgressIndicator(),
            );
          });
        },
      ),
    );
  }
}
