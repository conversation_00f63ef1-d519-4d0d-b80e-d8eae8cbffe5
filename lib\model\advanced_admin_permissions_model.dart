/// نموذج الصلاحيات المتقدمة للأدمن
/// يحتوي على جميع الصلاحيات الشاملة للتحكم الكامل في النظام
class AdvancedAdminPermissionsModel {
  // ========== صلاحيات إدارة الاشتراكات المتقدمة ==========
  late bool subscriptionManagementPermission; // إدارة الاشتراكات العامة
  late bool createCustomPlansPermission; // إنشاء خطط مخصصة
  late bool modifySubscriptionPlansPermission; // تعديل خطط الاشتراك
  late bool freezeSubscriptionsPermission; // تجميد الاشتراكات
  late bool cancelSubscriptionsPermission; // إلغاء الاشتراكات
  late bool grantFreeTrialsPermission; // منح فترات تجريبية مجانية
  late bool manageDiscountsPermission; // إدارة الخصومات والعروض
  late bool viewSubscriptionAnalyticsPermission; // عرض تحليلات الاشتراكات
  late bool bulkSubscriptionOperationsPermission; // العمليات المجمعة للاشتراكات

  // ========== صلاحيات إدارة النظام ==========
  late bool systemSettingsPermission; // إعدادات النظام العامة
  late bool databaseManagementPermission; // إدارة قاعدة البيانات
  late bool backupRestorePermission; // النسخ الاحتياطي والاستعادة
  late bool serverManagementPermission; // إدارة الخوادم
  late bool systemMonitoringPermission; // مراقبة النظام
  late bool performanceAnalyticsPermission; // تحليلات الأداء
  late bool systemAlertsPermission; // تنبيهات النظام
  late bool maintenanceModePermission; // وضع الصيانة

  // ========== صلاحيات الأمان والحماية ==========
  late bool securityManagementPermission; // إدارة الأمان العامة
  late bool passwordPolicyPermission; // سياسات كلمات المرور
  late bool auditLogsPermission; // سجلات التدقيق
  late bool sessionManagementPermission; // إدارة الجلسات
  late bool ipControlPermission; // التحكم في IP المسموح
  late bool twoFactorAuthPermission; // المصادقة الثنائية
  late bool encryptionManagementPermission; // إدارة التشفير
  late bool securityAlertsPermission; // تنبيهات الأمان

  // ========== صلاحيات التحكم المالي المتقدم ==========
  late bool advancedFinancialControlPermission; // التحكم المالي المتقدم
  late bool paymentGatewayManagementPermission; // إدارة بوابات الدفع
  late bool invoiceManagementPermission; // إدارة الفواتير
  late bool spendingLimitsPermission; // حدود الإنفاق
  late bool commissionManagementPermission; // إدارة العمولات
  late bool currencyManagementPermission; // إدارة العملات
  late bool taxManagementPermission; // إدارة الضرائب
  late bool financialReportsPermission; // التقارير المالية المتقدمة

  // ========== صلاحيات إدارة المحتوى والإعلانات ==========
  late bool contentManagementPermission; // إدارة المحتوى
  late bool advertisingManagementPermission; // إدارة الإعلانات
  late bool emailMarketingPermission; // التسويق عبر البريد الإلكتروني
  late bool smsMarketingPermission; // التسويق عبر الرسائل النصية
  late bool notificationManagementPermission; // إدارة الإشعارات
  late bool socialMediaManagementPermission; // إدارة وسائل التواصل الاجتماعي

  // ========== صلاحيات إدارة المتاجر المتقدمة ==========
  late bool advancedShopManagementPermission; // إدارة المتاجر المتقدمة
  late bool shopVerificationPermission; // التحقق من المتاجر
  late bool shopSuspensionPermission; // تعليق المتاجر
  late bool shopAnalyticsPermission; // تحليلات المتاجر
  late bool shopSettingsPermission; // إعدادات المتاجر
  late bool shopCategoriesManagementPermission; // إدارة فئات المتاجر

  // ========== صلاحيات إدارة المستخدمين المتقدمة ==========
  late bool advancedUserManagementPermission; // إدارة المستخدمين المتقدمة
  late bool userVerificationPermission; // التحقق من المستخدمين
  late bool userSuspensionPermission; // تعليق المستخدمين
  late bool userAnalyticsPermission; // تحليلات المستخدمين
  late bool massUserOperationsPermission; // العمليات المجمعة للمستخدمين
  late bool userRoleManagementPermission; // إدارة أدوار المستخدمين

  // ========== صلاحيات التقارير والتحليلات المتقدمة ==========
  late bool advancedReportsPermission; // التقارير المتقدمة
  late bool businessIntelligencePermission; // ذكاء الأعمال
  late bool dataExportPermission; // تصدير البيانات
  late bool customReportsPermission; // التقارير المخصصة
  late bool realTimeAnalyticsPermission; // التحليلات الفورية
  late bool predictiveAnalyticsPermission; // التحليلات التنبؤية

  // ========== صلاحيات إدارة API والتكاملات ==========
  late bool apiManagementPermission; // إدارة API
  late bool thirdPartyIntegrationsPermission; // التكاملات مع الطرف الثالث
  late bool webhookManagementPermission; // إدارة Webhooks
  late bool apiKeysManagementPermission; // إدارة مفاتيح API

  // ========== صلاحيات إدارة الجودة والدعم ==========
  late bool qualityControlPermission; // مراقبة الجودة
  late bool customerSupportManagementPermission; // إدارة دعم العملاء
  late bool ticketManagementPermission; // إدارة التذاكر
  late bool feedbackManagementPermission; // إدارة التعليقات

  // ========== صلاحيات الطوارئ ==========
  late bool emergencyAccessPermission; // الوصول في حالات الطوارئ
  late bool systemShutdownPermission; // إيقاف النظام
  late bool dataRecoveryPermission; // استعادة البيانات
  late bool emergencyNotificationsPermission; // إشعارات الطوارئ

  // ========== صلاحيات المطور ==========
  late bool developerModePermission; // وضع المطور
  late bool debugModePermission; // وضع التصحيح
  late bool logManagementPermission; // إدارة السجلات
  late bool codeDeploymentPermission; // نشر الكود

  // ========== صلاحيات إدارة المخزون المتقدمة ==========
  late bool advancedInventoryPermission; // إدارة المخزون المتقدمة
  late bool inventoryForecastingPermission; // توقعات المخزون
  late bool supplierManagementPermission; // إدارة الموردين
  late bool warehouseManagementPermission; // إدارة المستودعات

  // ========== صلاحيات إدارة الموارد البشرية المتقدمة ==========
  late bool advancedHRPermission; // إدارة الموارد البشرية المتقدمة
  late bool payrollManagementPermission; // إدارة الرواتب
  late bool attendanceManagementPermission; // إدارة الحضور
  late bool performanceManagementPermission; // إدارة الأداء

  AdvancedAdminPermissionsModel({
    // صلاحيات إدارة الاشتراكات المتقدمة
    this.subscriptionManagementPermission = false,
    this.createCustomPlansPermission = false,
    this.modifySubscriptionPlansPermission = false,
    this.freezeSubscriptionsPermission = false,
    this.cancelSubscriptionsPermission = false,
    this.grantFreeTrialsPermission = false,
    this.manageDiscountsPermission = false,
    this.viewSubscriptionAnalyticsPermission = false,
    this.bulkSubscriptionOperationsPermission = false,

    // صلاحيات إدارة النظام
    this.systemSettingsPermission = false,
    this.databaseManagementPermission = false,
    this.backupRestorePermission = false,
    this.serverManagementPermission = false,
    this.systemMonitoringPermission = false,
    this.performanceAnalyticsPermission = false,
    this.systemAlertsPermission = false,
    this.maintenanceModePermission = false,

    // صلاحيات الأمان والحماية
    this.securityManagementPermission = false,
    this.passwordPolicyPermission = false,
    this.auditLogsPermission = false,
    this.sessionManagementPermission = false,
    this.ipControlPermission = false,
    this.twoFactorAuthPermission = false,
    this.encryptionManagementPermission = false,
    this.securityAlertsPermission = false,

    // صلاحيات التحكم المالي المتقدم
    this.advancedFinancialControlPermission = false,
    this.paymentGatewayManagementPermission = false,
    this.invoiceManagementPermission = false,
    this.spendingLimitsPermission = false,
    this.commissionManagementPermission = false,
    this.currencyManagementPermission = false,
    this.taxManagementPermission = false,
    this.financialReportsPermission = false,

    // صلاحيات إدارة المحتوى والإعلانات
    this.contentManagementPermission = false,
    this.advertisingManagementPermission = false,
    this.emailMarketingPermission = false,
    this.smsMarketingPermission = false,
    this.notificationManagementPermission = false,
    this.socialMediaManagementPermission = false,

    // صلاحيات إدارة المتاجر المتقدمة
    this.advancedShopManagementPermission = false,
    this.shopVerificationPermission = false,
    this.shopSuspensionPermission = false,
    this.shopAnalyticsPermission = false,
    this.shopSettingsPermission = false,
    this.shopCategoriesManagementPermission = false,

    // صلاحيات إدارة المستخدمين المتقدمة
    this.advancedUserManagementPermission = false,
    this.userVerificationPermission = false,
    this.userSuspensionPermission = false,
    this.userAnalyticsPermission = false,
    this.massUserOperationsPermission = false,
    this.userRoleManagementPermission = false,

    // صلاحيات التقارير والتحليلات المتقدمة
    this.advancedReportsPermission = false,
    this.businessIntelligencePermission = false,
    this.dataExportPermission = false,
    this.customReportsPermission = false,
    this.realTimeAnalyticsPermission = false,
    this.predictiveAnalyticsPermission = false,

    // صلاحيات إدارة API والتكاملات
    this.apiManagementPermission = false,
    this.thirdPartyIntegrationsPermission = false,
    this.webhookManagementPermission = false,
    this.apiKeysManagementPermission = false,

    // صلاحيات إدارة الجودة والدعم
    this.qualityControlPermission = false,
    this.customerSupportManagementPermission = false,
    this.ticketManagementPermission = false,
    this.feedbackManagementPermission = false,

    // صلاحيات الطوارئ
    this.emergencyAccessPermission = false,
    this.systemShutdownPermission = false,
    this.dataRecoveryPermission = false,
    this.emergencyNotificationsPermission = false,

    // صلاحيات المطور
    this.developerModePermission = false,
    this.debugModePermission = false,
    this.logManagementPermission = false,
    this.codeDeploymentPermission = false,

    // صلاحيات إدارة المخزون المتقدمة
    this.advancedInventoryPermission = false,
    this.inventoryForecastingPermission = false,
    this.supplierManagementPermission = false,
    this.warehouseManagementPermission = false,

    // صلاحيات إدارة الموارد البشرية المتقدمة
    this.advancedHRPermission = false,
    this.payrollManagementPermission = false,
    this.attendanceManagementPermission = false,
    this.performanceManagementPermission = false,
  });

  /// إنشاء من JSON
  AdvancedAdminPermissionsModel.fromJson(Map<dynamic, dynamic> json)
      : subscriptionManagementPermission =
            json['subscriptionManagementPermission'] ?? false,
        createCustomPlansPermission =
            json['createCustomPlansPermission'] ?? false,
        modifySubscriptionPlansPermission =
            json['modifySubscriptionPlansPermission'] ?? false,
        freezeSubscriptionsPermission =
            json['freezeSubscriptionsPermission'] ?? false,
        cancelSubscriptionsPermission =
            json['cancelSubscriptionsPermission'] ?? false,
        grantFreeTrialsPermission = json['grantFreeTrialsPermission'] ?? false,
        manageDiscountsPermission = json['manageDiscountsPermission'] ?? false,
        viewSubscriptionAnalyticsPermission =
            json['viewSubscriptionAnalyticsPermission'] ?? false,
        bulkSubscriptionOperationsPermission =
            json['bulkSubscriptionOperationsPermission'] ?? false,
        systemSettingsPermission = json['systemSettingsPermission'] ?? false,
        databaseManagementPermission =
            json['databaseManagementPermission'] ?? false,
        backupRestorePermission = json['backupRestorePermission'] ?? false,
        serverManagementPermission =
            json['serverManagementPermission'] ?? false,
        systemMonitoringPermission =
            json['systemMonitoringPermission'] ?? false,
        performanceAnalyticsPermission =
            json['performanceAnalyticsPermission'] ?? false,
        systemAlertsPermission = json['systemAlertsPermission'] ?? false,
        maintenanceModePermission = json['maintenanceModePermission'] ?? false,
        securityManagementPermission =
            json['securityManagementPermission'] ?? false,
        passwordPolicyPermission = json['passwordPolicyPermission'] ?? false,
        auditLogsPermission = json['auditLogsPermission'] ?? false,
        sessionManagementPermission =
            json['sessionManagementPermission'] ?? false,
        ipControlPermission = json['ipControlPermission'] ?? false,
        twoFactorAuthPermission = json['twoFactorAuthPermission'] ?? false,
        encryptionManagementPermission =
            json['encryptionManagementPermission'] ?? false,
        securityAlertsPermission = json['securityAlertsPermission'] ?? false,
        advancedFinancialControlPermission =
            json['advancedFinancialControlPermission'] ?? false,
        paymentGatewayManagementPermission =
            json['paymentGatewayManagementPermission'] ?? false,
        invoiceManagementPermission =
            json['invoiceManagementPermission'] ?? false,
        spendingLimitsPermission = json['spendingLimitsPermission'] ?? false,
        commissionManagementPermission =
            json['commissionManagementPermission'] ?? false,
        currencyManagementPermission =
            json['currencyManagementPermission'] ?? false,
        taxManagementPermission = json['taxManagementPermission'] ?? false,
        financialReportsPermission =
            json['financialReportsPermission'] ?? false,
        contentManagementPermission =
            json['contentManagementPermission'] ?? false,
        advertisingManagementPermission =
            json['advertisingManagementPermission'] ?? false,
        emailMarketingPermission = json['emailMarketingPermission'] ?? false,
        smsMarketingPermission = json['smsMarketingPermission'] ?? false,
        notificationManagementPermission =
            json['notificationManagementPermission'] ?? false,
        socialMediaManagementPermission =
            json['socialMediaManagementPermission'] ?? false,
        advancedShopManagementPermission =
            json['advancedShopManagementPermission'] ?? false,
        shopVerificationPermission =
            json['shopVerificationPermission'] ?? false,
        shopSuspensionPermission = json['shopSuspensionPermission'] ?? false,
        shopAnalyticsPermission = json['shopAnalyticsPermission'] ?? false,
        shopSettingsPermission = json['shopSettingsPermission'] ?? false,
        shopCategoriesManagementPermission =
            json['shopCategoriesManagementPermission'] ?? false,
        advancedUserManagementPermission =
            json['advancedUserManagementPermission'] ?? false,
        userVerificationPermission =
            json['userVerificationPermission'] ?? false,
        userSuspensionPermission = json['userSuspensionPermission'] ?? false,
        userAnalyticsPermission = json['userAnalyticsPermission'] ?? false,
        massUserOperationsPermission =
            json['massUserOperationsPermission'] ?? false,
        userRoleManagementPermission =
            json['userRoleManagementPermission'] ?? false,
        advancedReportsPermission = json['advancedReportsPermission'] ?? false,
        businessIntelligencePermission =
            json['businessIntelligencePermission'] ?? false,
        dataExportPermission = json['dataExportPermission'] ?? false,
        customReportsPermission = json['customReportsPermission'] ?? false,
        realTimeAnalyticsPermission =
            json['realTimeAnalyticsPermission'] ?? false,
        predictiveAnalyticsPermission =
            json['predictiveAnalyticsPermission'] ?? false,
        apiManagementPermission = json['apiManagementPermission'] ?? false,
        thirdPartyIntegrationsPermission =
            json['thirdPartyIntegrationsPermission'] ?? false,
        webhookManagementPermission =
            json['webhookManagementPermission'] ?? false,
        apiKeysManagementPermission =
            json['apiKeysManagementPermission'] ?? false,
        qualityControlPermission = json['qualityControlPermission'] ?? false,
        customerSupportManagementPermission =
            json['customerSupportManagementPermission'] ?? false,
        ticketManagementPermission =
            json['ticketManagementPermission'] ?? false,
        feedbackManagementPermission =
            json['feedbackManagementPermission'] ?? false,
        emergencyAccessPermission = json['emergencyAccessPermission'] ?? false,
        systemShutdownPermission = json['systemShutdownPermission'] ?? false,
        dataRecoveryPermission = json['dataRecoveryPermission'] ?? false,
        emergencyNotificationsPermission =
            json['emergencyNotificationsPermission'] ?? false,
        developerModePermission = json['developerModePermission'] ?? false,
        debugModePermission = json['debugModePermission'] ?? false,
        logManagementPermission = json['logManagementPermission'] ?? false,
        codeDeploymentPermission = json['codeDeploymentPermission'] ?? false,
        advancedInventoryPermission =
            json['advancedInventoryPermission'] ?? false,
        inventoryForecastingPermission =
            json['inventoryForecastingPermission'] ?? false,
        supplierManagementPermission =
            json['supplierManagementPermission'] ?? false,
        warehouseManagementPermission =
            json['warehouseManagementPermission'] ?? false,
        advancedHRPermission = json['advancedHRPermission'] ?? false,
        payrollManagementPermission =
            json['payrollManagementPermission'] ?? false,
        attendanceManagementPermission =
            json['attendanceManagementPermission'] ?? false,
        performanceManagementPermission =
            json['performanceManagementPermission'] ?? false;

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() => {
        // صلاحيات إدارة الاشتراكات المتقدمة
        'subscriptionManagementPermission': subscriptionManagementPermission,
        'createCustomPlansPermission': createCustomPlansPermission,
        'modifySubscriptionPlansPermission': modifySubscriptionPlansPermission,
        'freezeSubscriptionsPermission': freezeSubscriptionsPermission,
        'cancelSubscriptionsPermission': cancelSubscriptionsPermission,
        'grantFreeTrialsPermission': grantFreeTrialsPermission,
        'manageDiscountsPermission': manageDiscountsPermission,
        'viewSubscriptionAnalyticsPermission':
            viewSubscriptionAnalyticsPermission,
        'bulkSubscriptionOperationsPermission':
            bulkSubscriptionOperationsPermission,

        // صلاحيات إدارة النظام
        'systemSettingsPermission': systemSettingsPermission,
        'databaseManagementPermission': databaseManagementPermission,
        'backupRestorePermission': backupRestorePermission,
        'serverManagementPermission': serverManagementPermission,
        'systemMonitoringPermission': systemMonitoringPermission,
        'performanceAnalyticsPermission': performanceAnalyticsPermission,
        'systemAlertsPermission': systemAlertsPermission,
        'maintenanceModePermission': maintenanceModePermission,

        // صلاحيات الأمان والحماية
        'securityManagementPermission': securityManagementPermission,
        'passwordPolicyPermission': passwordPolicyPermission,
        'auditLogsPermission': auditLogsPermission,
        'sessionManagementPermission': sessionManagementPermission,
        'ipControlPermission': ipControlPermission,
        'twoFactorAuthPermission': twoFactorAuthPermission,
        'encryptionManagementPermission': encryptionManagementPermission,
        'securityAlertsPermission': securityAlertsPermission,

        // صلاحيات التحكم المالي المتقدم
        'advancedFinancialControlPermission':
            advancedFinancialControlPermission,
        'paymentGatewayManagementPermission':
            paymentGatewayManagementPermission,
        'invoiceManagementPermission': invoiceManagementPermission,
        'spendingLimitsPermission': spendingLimitsPermission,
        'commissionManagementPermission': commissionManagementPermission,
        'currencyManagementPermission': currencyManagementPermission,
        'taxManagementPermission': taxManagementPermission,
        'financialReportsPermission': financialReportsPermission,

        // صلاحيات إدارة المحتوى والإعلانات
        'contentManagementPermission': contentManagementPermission,
        'advertisingManagementPermission': advertisingManagementPermission,
        'emailMarketingPermission': emailMarketingPermission,
        'smsMarketingPermission': smsMarketingPermission,
        'notificationManagementPermission': notificationManagementPermission,
        'socialMediaManagementPermission': socialMediaManagementPermission,

        // صلاحيات إدارة المتاجر المتقدمة
        'advancedShopManagementPermission': advancedShopManagementPermission,
        'shopVerificationPermission': shopVerificationPermission,
        'shopSuspensionPermission': shopSuspensionPermission,
        'shopAnalyticsPermission': shopAnalyticsPermission,
        'shopSettingsPermission': shopSettingsPermission,
        'shopCategoriesManagementPermission':
            shopCategoriesManagementPermission,

        // صلاحيات إدارة المستخدمين المتقدمة
        'advancedUserManagementPermission': advancedUserManagementPermission,
        'userVerificationPermission': userVerificationPermission,
        'userSuspensionPermission': userSuspensionPermission,
        'userAnalyticsPermission': userAnalyticsPermission,
        'massUserOperationsPermission': massUserOperationsPermission,
        'userRoleManagementPermission': userRoleManagementPermission,

        // صلاحيات التقارير والتحليلات المتقدمة
        'advancedReportsPermission': advancedReportsPermission,
        'businessIntelligencePermission': businessIntelligencePermission,
        'dataExportPermission': dataExportPermission,
        'customReportsPermission': customReportsPermission,
        'realTimeAnalyticsPermission': realTimeAnalyticsPermission,
        'predictiveAnalyticsPermission': predictiveAnalyticsPermission,

        // صلاحيات إدارة API والتكاملات
        'apiManagementPermission': apiManagementPermission,
        'thirdPartyIntegrationsPermission': thirdPartyIntegrationsPermission,
        'webhookManagementPermission': webhookManagementPermission,
        'apiKeysManagementPermission': apiKeysManagementPermission,

        // صلاحيات إدارة الجودة والدعم
        'qualityControlPermission': qualityControlPermission,
        'customerSupportManagementPermission':
            customerSupportManagementPermission,
        'ticketManagementPermission': ticketManagementPermission,
        'feedbackManagementPermission': feedbackManagementPermission,

        // صلاحيات الطوارئ
        'emergencyAccessPermission': emergencyAccessPermission,
        'systemShutdownPermission': systemShutdownPermission,
        'dataRecoveryPermission': dataRecoveryPermission,
        'emergencyNotificationsPermission': emergencyNotificationsPermission,

        // صلاحيات المطور
        'developerModePermission': developerModePermission,
        'debugModePermission': debugModePermission,
        'logManagementPermission': logManagementPermission,
        'codeDeploymentPermission': codeDeploymentPermission,

        // صلاحيات إدارة المخزون المتقدمة
        'advancedInventoryPermission': advancedInventoryPermission,
        'inventoryForecastingPermission': inventoryForecastingPermission,
        'supplierManagementPermission': supplierManagementPermission,
        'warehouseManagementPermission': warehouseManagementPermission,

        // صلاحيات إدارة الموارد البشرية المتقدمة
        'advancedHRPermission': advancedHRPermission,
        'payrollManagementPermission': payrollManagementPermission,
        'attendanceManagementPermission': attendanceManagementPermission,
        'performanceManagementPermission': performanceManagementPermission,
      };

  /// تعيين جميع الصلاحيات إلى قيمة محددة
  void setAllPermissions(bool value) {
    // صلاحيات إدارة الاشتراكات المتقدمة
    subscriptionManagementPermission = value;
    createCustomPlansPermission = value;
    modifySubscriptionPlansPermission = value;
    freezeSubscriptionsPermission = value;
    cancelSubscriptionsPermission = value;
    grantFreeTrialsPermission = value;
    manageDiscountsPermission = value;
    viewSubscriptionAnalyticsPermission = value;
    bulkSubscriptionOperationsPermission = value;

    // صلاحيات إدارة النظام
    systemSettingsPermission = value;
    databaseManagementPermission = value;
    backupRestorePermission = value;
    serverManagementPermission = value;
    systemMonitoringPermission = value;
    performanceAnalyticsPermission = value;
    systemAlertsPermission = value;
    maintenanceModePermission = value;

    // صلاحيات الأمان والحماية
    securityManagementPermission = value;
    passwordPolicyPermission = value;
    auditLogsPermission = value;
    sessionManagementPermission = value;
    ipControlPermission = value;
    twoFactorAuthPermission = value;
    encryptionManagementPermission = value;
    securityAlertsPermission = value;

    // صلاحيات التحكم المالي المتقدم
    advancedFinancialControlPermission = value;
    paymentGatewayManagementPermission = value;
    invoiceManagementPermission = value;
    spendingLimitsPermission = value;
    commissionManagementPermission = value;
    currencyManagementPermission = value;
    taxManagementPermission = value;
    financialReportsPermission = value;

    // صلاحيات إدارة المحتوى والإعلانات
    contentManagementPermission = value;
    advertisingManagementPermission = value;
    emailMarketingPermission = value;
    smsMarketingPermission = value;
    notificationManagementPermission = value;
    socialMediaManagementPermission = value;

    // صلاحيات إدارة المتاجر المتقدمة
    advancedShopManagementPermission = value;
    shopVerificationPermission = value;
    shopSuspensionPermission = value;
    shopAnalyticsPermission = value;
    shopSettingsPermission = value;
    shopCategoriesManagementPermission = value;

    // صلاحيات إدارة المستخدمين المتقدمة
    advancedUserManagementPermission = value;
    userVerificationPermission = value;
    userSuspensionPermission = value;
    userAnalyticsPermission = value;
    massUserOperationsPermission = value;
    userRoleManagementPermission = value;

    // صلاحيات التقارير والتحليلات المتقدمة
    advancedReportsPermission = value;
    businessIntelligencePermission = value;
    dataExportPermission = value;
    customReportsPermission = value;
    realTimeAnalyticsPermission = value;
    predictiveAnalyticsPermission = value;

    // صلاحيات إدارة API والتكاملات
    apiManagementPermission = value;
    thirdPartyIntegrationsPermission = value;
    webhookManagementPermission = value;
    apiKeysManagementPermission = value;

    // صلاحيات إدارة الجودة والدعم
    qualityControlPermission = value;
    customerSupportManagementPermission = value;
    ticketManagementPermission = value;
    feedbackManagementPermission = value;

    // صلاحيات الطوارئ
    emergencyAccessPermission = value;
    systemShutdownPermission = value;
    dataRecoveryPermission = value;
    emergencyNotificationsPermission = value;

    // صلاحيات المطور
    developerModePermission = value;
    debugModePermission = value;
    logManagementPermission = value;
    codeDeploymentPermission = value;

    // صلاحيات إدارة المخزون المتقدمة
    advancedInventoryPermission = value;
    inventoryForecastingPermission = value;
    supplierManagementPermission = value;
    warehouseManagementPermission = value;

    // صلاحيات إدارة الموارد البشرية المتقدمة
    advancedHRPermission = value;
    payrollManagementPermission = value;
    attendanceManagementPermission = value;
    performanceManagementPermission = value;
  }

  /// التحقق من وجود أي صلاحية
  bool hasAnyPermission() {
    return subscriptionManagementPermission ||
        createCustomPlansPermission ||
        modifySubscriptionPlansPermission ||
        freezeSubscriptionsPermission ||
        cancelSubscriptionsPermission ||
        grantFreeTrialsPermission ||
        manageDiscountsPermission ||
        viewSubscriptionAnalyticsPermission ||
        bulkSubscriptionOperationsPermission ||
        systemSettingsPermission ||
        databaseManagementPermission ||
        backupRestorePermission ||
        serverManagementPermission ||
        systemMonitoringPermission ||
        performanceAnalyticsPermission ||
        systemAlertsPermission ||
        maintenanceModePermission ||
        securityManagementPermission ||
        passwordPolicyPermission ||
        auditLogsPermission ||
        sessionManagementPermission ||
        ipControlPermission ||
        twoFactorAuthPermission ||
        encryptionManagementPermission ||
        securityAlertsPermission ||
        advancedFinancialControlPermission ||
        paymentGatewayManagementPermission ||
        invoiceManagementPermission ||
        spendingLimitsPermission ||
        commissionManagementPermission ||
        currencyManagementPermission ||
        taxManagementPermission ||
        financialReportsPermission ||
        contentManagementPermission ||
        advertisingManagementPermission ||
        emailMarketingPermission ||
        smsMarketingPermission ||
        notificationManagementPermission ||
        socialMediaManagementPermission ||
        advancedShopManagementPermission ||
        shopVerificationPermission ||
        shopSuspensionPermission ||
        shopAnalyticsPermission ||
        shopSettingsPermission ||
        shopCategoriesManagementPermission ||
        advancedUserManagementPermission ||
        userVerificationPermission ||
        userSuspensionPermission ||
        userAnalyticsPermission ||
        massUserOperationsPermission ||
        userRoleManagementPermission ||
        advancedReportsPermission ||
        businessIntelligencePermission ||
        dataExportPermission ||
        customReportsPermission ||
        realTimeAnalyticsPermission ||
        predictiveAnalyticsPermission ||
        apiManagementPermission ||
        thirdPartyIntegrationsPermission ||
        webhookManagementPermission ||
        apiKeysManagementPermission ||
        qualityControlPermission ||
        customerSupportManagementPermission ||
        ticketManagementPermission ||
        feedbackManagementPermission ||
        emergencyAccessPermission ||
        systemShutdownPermission ||
        dataRecoveryPermission ||
        emergencyNotificationsPermission ||
        developerModePermission ||
        debugModePermission ||
        logManagementPermission ||
        codeDeploymentPermission ||
        advancedInventoryPermission ||
        inventoryForecastingPermission ||
        supplierManagementPermission ||
        warehouseManagementPermission ||
        advancedHRPermission ||
        payrollManagementPermission ||
        attendanceManagementPermission ||
        performanceManagementPermission;
  }

  /// نسخ الكائن
  AdvancedAdminPermissionsModel copyWith({
    bool? subscriptionManagementPermission,
    bool? createCustomPlansPermission,
    bool? modifySubscriptionPlansPermission,
    bool? freezeSubscriptionsPermission,
    bool? cancelSubscriptionsPermission,
    bool? grantFreeTrialsPermission,
    bool? manageDiscountsPermission,
    bool? viewSubscriptionAnalyticsPermission,
    bool? bulkSubscriptionOperationsPermission,
    bool? systemSettingsPermission,
    bool? databaseManagementPermission,
    bool? backupRestorePermission,
    bool? serverManagementPermission,
    bool? systemMonitoringPermission,
    bool? performanceAnalyticsPermission,
    bool? systemAlertsPermission,
    bool? maintenanceModePermission,
    bool? securityManagementPermission,
    bool? passwordPolicyPermission,
    bool? auditLogsPermission,
    bool? sessionManagementPermission,
    bool? ipControlPermission,
    bool? twoFactorAuthPermission,
    bool? encryptionManagementPermission,
    bool? securityAlertsPermission,
    bool? advancedFinancialControlPermission,
    bool? paymentGatewayManagementPermission,
    bool? invoiceManagementPermission,
    bool? spendingLimitsPermission,
    bool? commissionManagementPermission,
    bool? currencyManagementPermission,
    bool? taxManagementPermission,
    bool? financialReportsPermission,
    bool? contentManagementPermission,
    bool? advertisingManagementPermission,
    bool? emailMarketingPermission,
    bool? smsMarketingPermission,
    bool? notificationManagementPermission,
    bool? socialMediaManagementPermission,
    bool? advancedShopManagementPermission,
    bool? shopVerificationPermission,
    bool? shopSuspensionPermission,
    bool? shopAnalyticsPermission,
    bool? shopSettingsPermission,
    bool? shopCategoriesManagementPermission,
    bool? advancedUserManagementPermission,
    bool? userVerificationPermission,
    bool? userSuspensionPermission,
    bool? userAnalyticsPermission,
    bool? massUserOperationsPermission,
    bool? userRoleManagementPermission,
    bool? advancedReportsPermission,
    bool? businessIntelligencePermission,
    bool? dataExportPermission,
    bool? customReportsPermission,
    bool? realTimeAnalyticsPermission,
    bool? predictiveAnalyticsPermission,
    bool? apiManagementPermission,
    bool? thirdPartyIntegrationsPermission,
    bool? webhookManagementPermission,
    bool? apiKeysManagementPermission,
    bool? qualityControlPermission,
    bool? customerSupportManagementPermission,
    bool? ticketManagementPermission,
    bool? feedbackManagementPermission,
    bool? emergencyAccessPermission,
    bool? systemShutdownPermission,
    bool? dataRecoveryPermission,
    bool? emergencyNotificationsPermission,
    bool? developerModePermission,
    bool? debugModePermission,
    bool? logManagementPermission,
    bool? codeDeploymentPermission,
    bool? advancedInventoryPermission,
    bool? inventoryForecastingPermission,
    bool? supplierManagementPermission,
    bool? warehouseManagementPermission,
    bool? advancedHRPermission,
    bool? payrollManagementPermission,
    bool? attendanceManagementPermission,
    bool? performanceManagementPermission,
  }) {
    return AdvancedAdminPermissionsModel(
      subscriptionManagementPermission: subscriptionManagementPermission ??
          this.subscriptionManagementPermission,
      createCustomPlansPermission:
          createCustomPlansPermission ?? this.createCustomPlansPermission,
      modifySubscriptionPlansPermission: modifySubscriptionPlansPermission ??
          this.modifySubscriptionPlansPermission,
      freezeSubscriptionsPermission:
          freezeSubscriptionsPermission ?? this.freezeSubscriptionsPermission,
      cancelSubscriptionsPermission:
          cancelSubscriptionsPermission ?? this.cancelSubscriptionsPermission,
      grantFreeTrialsPermission:
          grantFreeTrialsPermission ?? this.grantFreeTrialsPermission,
      manageDiscountsPermission:
          manageDiscountsPermission ?? this.manageDiscountsPermission,
      viewSubscriptionAnalyticsPermission:
          viewSubscriptionAnalyticsPermission ??
              this.viewSubscriptionAnalyticsPermission,
      bulkSubscriptionOperationsPermission:
          bulkSubscriptionOperationsPermission ??
              this.bulkSubscriptionOperationsPermission,
      systemSettingsPermission:
          systemSettingsPermission ?? this.systemSettingsPermission,
      databaseManagementPermission:
          databaseManagementPermission ?? this.databaseManagementPermission,
      backupRestorePermission:
          backupRestorePermission ?? this.backupRestorePermission,
      serverManagementPermission:
          serverManagementPermission ?? this.serverManagementPermission,
      systemMonitoringPermission:
          systemMonitoringPermission ?? this.systemMonitoringPermission,
      performanceAnalyticsPermission:
          performanceAnalyticsPermission ?? this.performanceAnalyticsPermission,
      systemAlertsPermission:
          systemAlertsPermission ?? this.systemAlertsPermission,
      maintenanceModePermission:
          maintenanceModePermission ?? this.maintenanceModePermission,
      securityManagementPermission:
          securityManagementPermission ?? this.securityManagementPermission,
      passwordPolicyPermission:
          passwordPolicyPermission ?? this.passwordPolicyPermission,
      auditLogsPermission: auditLogsPermission ?? this.auditLogsPermission,
      sessionManagementPermission:
          sessionManagementPermission ?? this.sessionManagementPermission,
      ipControlPermission: ipControlPermission ?? this.ipControlPermission,
      twoFactorAuthPermission:
          twoFactorAuthPermission ?? this.twoFactorAuthPermission,
      encryptionManagementPermission:
          encryptionManagementPermission ?? this.encryptionManagementPermission,
      securityAlertsPermission:
          securityAlertsPermission ?? this.securityAlertsPermission,
      advancedFinancialControlPermission: advancedFinancialControlPermission ??
          this.advancedFinancialControlPermission,
      paymentGatewayManagementPermission: paymentGatewayManagementPermission ??
          this.paymentGatewayManagementPermission,
      invoiceManagementPermission:
          invoiceManagementPermission ?? this.invoiceManagementPermission,
      spendingLimitsPermission:
          spendingLimitsPermission ?? this.spendingLimitsPermission,
      commissionManagementPermission:
          commissionManagementPermission ?? this.commissionManagementPermission,
      currencyManagementPermission:
          currencyManagementPermission ?? this.currencyManagementPermission,
      taxManagementPermission:
          taxManagementPermission ?? this.taxManagementPermission,
      financialReportsPermission:
          financialReportsPermission ?? this.financialReportsPermission,
      contentManagementPermission:
          contentManagementPermission ?? this.contentManagementPermission,
      advertisingManagementPermission: advertisingManagementPermission ??
          this.advertisingManagementPermission,
      emailMarketingPermission:
          emailMarketingPermission ?? this.emailMarketingPermission,
      smsMarketingPermission:
          smsMarketingPermission ?? this.smsMarketingPermission,
      notificationManagementPermission: notificationManagementPermission ??
          this.notificationManagementPermission,
      socialMediaManagementPermission: socialMediaManagementPermission ??
          this.socialMediaManagementPermission,
      advancedShopManagementPermission: advancedShopManagementPermission ??
          this.advancedShopManagementPermission,
      shopVerificationPermission:
          shopVerificationPermission ?? this.shopVerificationPermission,
      shopSuspensionPermission:
          shopSuspensionPermission ?? this.shopSuspensionPermission,
      shopAnalyticsPermission:
          shopAnalyticsPermission ?? this.shopAnalyticsPermission,
      shopSettingsPermission:
          shopSettingsPermission ?? this.shopSettingsPermission,
      shopCategoriesManagementPermission: shopCategoriesManagementPermission ??
          this.shopCategoriesManagementPermission,
      advancedUserManagementPermission: advancedUserManagementPermission ??
          this.advancedUserManagementPermission,
      userVerificationPermission:
          userVerificationPermission ?? this.userVerificationPermission,
      userSuspensionPermission:
          userSuspensionPermission ?? this.userSuspensionPermission,
      userAnalyticsPermission:
          userAnalyticsPermission ?? this.userAnalyticsPermission,
      massUserOperationsPermission:
          massUserOperationsPermission ?? this.massUserOperationsPermission,
      userRoleManagementPermission:
          userRoleManagementPermission ?? this.userRoleManagementPermission,
      advancedReportsPermission:
          advancedReportsPermission ?? this.advancedReportsPermission,
      businessIntelligencePermission:
          businessIntelligencePermission ?? this.businessIntelligencePermission,
      dataExportPermission: dataExportPermission ?? this.dataExportPermission,
      customReportsPermission:
          customReportsPermission ?? this.customReportsPermission,
      realTimeAnalyticsPermission:
          realTimeAnalyticsPermission ?? this.realTimeAnalyticsPermission,
      predictiveAnalyticsPermission:
          predictiveAnalyticsPermission ?? this.predictiveAnalyticsPermission,
      apiManagementPermission:
          apiManagementPermission ?? this.apiManagementPermission,
      thirdPartyIntegrationsPermission: thirdPartyIntegrationsPermission ??
          this.thirdPartyIntegrationsPermission,
      webhookManagementPermission:
          webhookManagementPermission ?? this.webhookManagementPermission,
      apiKeysManagementPermission:
          apiKeysManagementPermission ?? this.apiKeysManagementPermission,
      qualityControlPermission:
          qualityControlPermission ?? this.qualityControlPermission,
      customerSupportManagementPermission:
          customerSupportManagementPermission ??
              this.customerSupportManagementPermission,
      ticketManagementPermission:
          ticketManagementPermission ?? this.ticketManagementPermission,
      feedbackManagementPermission:
          feedbackManagementPermission ?? this.feedbackManagementPermission,
      emergencyAccessPermission:
          emergencyAccessPermission ?? this.emergencyAccessPermission,
      systemShutdownPermission:
          systemShutdownPermission ?? this.systemShutdownPermission,
      dataRecoveryPermission:
          dataRecoveryPermission ?? this.dataRecoveryPermission,
      emergencyNotificationsPermission: emergencyNotificationsPermission ??
          this.emergencyNotificationsPermission,
      developerModePermission:
          developerModePermission ?? this.developerModePermission,
      debugModePermission: debugModePermission ?? this.debugModePermission,
      logManagementPermission:
          logManagementPermission ?? this.logManagementPermission,
      codeDeploymentPermission:
          codeDeploymentPermission ?? this.codeDeploymentPermission,
      advancedInventoryPermission:
          advancedInventoryPermission ?? this.advancedInventoryPermission,
      inventoryForecastingPermission:
          inventoryForecastingPermission ?? this.inventoryForecastingPermission,
      supplierManagementPermission:
          supplierManagementPermission ?? this.supplierManagementPermission,
      warehouseManagementPermission:
          warehouseManagementPermission ?? this.warehouseManagementPermission,
      advancedHRPermission: advancedHRPermission ?? this.advancedHRPermission,
      payrollManagementPermission:
          payrollManagementPermission ?? this.payrollManagementPermission,
      attendanceManagementPermission:
          attendanceManagementPermission ?? this.attendanceManagementPermission,
      performanceManagementPermission: performanceManagementPermission ??
          this.performanceManagementPermission,
    );
  }
}
