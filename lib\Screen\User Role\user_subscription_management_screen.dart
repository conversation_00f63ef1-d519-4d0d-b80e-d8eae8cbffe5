import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hugeicons/hugeicons.dart';

import '../Widgets/Constant Data/constant.dart';
import '../../Provider/seller_info_provider.dart';
import '../../Provider/subacription_plan_provider.dart';
import '../../Provider/default_subscription_provider.dart';
import '../../model/seller_info_model.dart';
import '../../model/subscription_plan_model.dart';


import '../Widgets/Pop Up/User Role/change_subscription_dialog.dart';

class UserSubscriptionManagementScreen extends StatefulWidget {
  const UserSubscriptionManagementScreen({super.key});

  static const String route = '/user_subscription_management';

  @override
  State<UserSubscriptionManagementScreen> createState() => _UserSubscriptionManagementScreenState();
}

class _UserSubscriptionManagementScreenState extends State<UserSubscriptionManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, active, expired, trial
  SubscriptionPlanModel? _defaultPlan;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: kWhiteTextColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: kMainColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10.0),
                    topRight: Radius.circular(10.0),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedLoyaltyCard,
                      color: kMainColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إدارة باقات المستخدمين',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: kMainColor,
                      ),
                    ),
                    const Spacer(),
                    // Default Plan Selector
                    Consumer(
                      builder: (context, ref, child) {
                        final plansAsync = ref.watch(subscriptionPlanProvider);
                        final defaultPlanAsync = ref.watch(defaultSubscriptionProvider);

                        return plansAsync.when(
                          data: (plans) {
                            // Set current default plan from provider
                            defaultPlanAsync.whenData((defaultPlan) {
                              if (defaultPlan != null && _defaultPlan == null) {
                                _defaultPlan = plans.firstWhere(
                                  (plan) => plan.subscriptionName == defaultPlan.planName,
                                  orElse: () => plans.first,
                                );
                              }
                            });

                            return Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: DropdownButton<SubscriptionPlanModel>(
                                value: _defaultPlan,
                                hint: const Text('الباقة الافتراضية'),
                                underline: const SizedBox(),
                                onChanged: (value) {
                                  setState(() {
                                    _defaultPlan = value;
                                  });
                                  _setDefaultPlan(value, ref);
                                },
                                items: plans.map((plan) {
                                  return DropdownMenuItem(
                                    value: plan,
                                    child: Text(plan.subscriptionName),
                                  );
                                }).toList(),
                              ),
                            );
                          },
                          loading: () => const CircularProgressIndicator(),
                          error: (error, stack) => const Text('خطأ في تحميل الباقات'),
                        );
                      },
                    ),
                  ],
                ),
              ),

              // Search and Filter Bar
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    // Search Field
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: _searchController,
                        onChanged: (value) {
                          setState(() {
                            _searchQuery = value.toLowerCase();
                          });
                        },
                        decoration: InputDecoration(
                          hintText: 'البحث في المستخدمين...',
                          prefixIcon: const Icon(HugeIcons.strokeRoundedSearch01),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: kMainColor),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Filter Dropdown
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        underline: const SizedBox(),
                        onChanged: (value) {
                          setState(() {
                            _selectedFilter = value!;
                          });
                        },
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('جميع المستخدمين')),
                          DropdownMenuItem(value: 'active', child: Text('الباقات النشطة')),
                          DropdownMenuItem(value: 'expired', child: Text('الباقات المنتهية')),
                          DropdownMenuItem(value: 'trial', child: Text('الباقات التجريبية')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Users List
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final sellersAsync = ref.watch(sellerInfoProvider);

                    return sellersAsync.when(
                      data: (sellers) {
                        final filteredSellers = sellers.where((seller) {
                          final matchesSearch = (seller.companyName?.toLowerCase() ?? '').contains(_searchQuery) ||
                                               (seller.email?.toLowerCase() ?? '').contains(_searchQuery);

                          if (!matchesSearch) return false;

                          // Apply subscription filter
                          if (_selectedFilter == 'all') return true;

                          // Apply subscription filter based on subscription status
                          final subscriptionName = seller.subscriptionName?.toLowerCase() ?? '';
                          switch (_selectedFilter) {
                            case 'active':
                              return subscriptionName.isNotEmpty && subscriptionName != 'مجاني';
                            case 'expired':
                              return subscriptionName.isEmpty;
                            case 'trial':
                              return subscriptionName == 'مجاني';
                            default:
                              return true;
                          }
                        }).toList();

                        if (filteredSellers.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  HugeIcons.strokeRoundedSearchRemove,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _searchQuery.isEmpty
                                    ? 'لا توجد مستخدمين'
                                    : 'لا توجد نتائج للبحث',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: filteredSellers.length,
                          itemBuilder: (context, index) {
                            final seller = filteredSellers[index];
                            return _buildUserSubscriptionCard(context, seller, ref);
                          },
                        );
                      },
                      loading: () => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      error: (error, stack) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              HugeIcons.strokeRoundedAlert02,
                              size: 64,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'حدث خطأ في تحميل البيانات',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.red.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              error.toString(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserSubscriptionCard(BuildContext context, SellerInfoModel seller, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: kMainColor.withValues(alpha: 0.1),
                  child: Text(
                    (seller.companyName?.isNotEmpty == true) ? seller.companyName![0].toUpperCase() : 'U',
                    style: TextStyle(
                      color: kMainColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        seller.companyName ?? 'غير محدد',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        seller.email ?? 'غير محدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Subscription Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getSubscriptionStatusColor(seller).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getSubscriptionStatusText(seller),
                    style: TextStyle(
                      color: _getSubscriptionStatusColor(seller),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(context, value, seller, ref),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'change_plan',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedLoyaltyCard),
                          SizedBox(width: 8),
                          Text('تغيير الباقة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'extend_subscription',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedCalendarAdd01),
                          SizedBox(width: 8),
                          Text('تمديد الاشتراك'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'view_history',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedCalendar03),
                          SizedBox(width: 8),
                          Text('تاريخ الاشتراكات'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Subscription Info
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('الباقة الحالية', seller.subscriptionName ?? 'غير محدد'),
                ),
                Expanded(
                  child: _buildInfoItem('تاريخ الانتهاء', _getExpiryDate(seller)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Color _getSubscriptionStatusColor(SellerInfoModel seller) {
    final subscriptionName = seller.subscriptionName?.toLowerCase() ?? '';
    if (subscriptionName.isEmpty) return Colors.red;
    if (subscriptionName == 'مجاني') return Colors.orange;
    return Colors.green;
  }

  String _getSubscriptionStatusText(SellerInfoModel seller) {
    final subscriptionName = seller.subscriptionName?.toLowerCase() ?? '';
    if (subscriptionName.isEmpty) return 'منتهي';
    if (subscriptionName == 'مجاني') return 'تجريبي';
    return 'نشط';
  }

  String _getExpiryDate(SellerInfoModel seller) {
    if (seller.subscriptionDate != null) {
      try {
        final subscriptionDate = DateTime.parse(seller.subscriptionDate!);
        final expiryDate = subscriptionDate.add(const Duration(days: 30)); // Default 30 days
        return '${expiryDate.day}/${expiryDate.month}/${expiryDate.year}';
      } catch (e) {
        return 'غير محدد';
      }
    }
    return 'غير محدد';
  }

  void _handleMenuAction(BuildContext context, String action, SellerInfoModel seller, WidgetRef ref) {
    switch (action) {
      case 'change_plan':
        _showChangePlanDialog(context, seller, ref);
        break;
      case 'extend_subscription':
        _showExtendSubscriptionDialog(context, seller, ref);
        break;
      case 'view_history':
        _showSubscriptionHistory(context, seller);
        break;
    }
  }

  void _showChangePlanDialog(BuildContext context, SellerInfoModel seller, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => ChangeSubscriptionDialog(seller: seller),
    );
  }

  void _showExtendSubscriptionDialog(BuildContext context, SellerInfoModel seller, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تمديد الاشتراك'),
        content: Text('تمديد اشتراك: ${seller.companyName ?? 'غير محدد'}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('ميزة تمديد الاشتراك ستكون متاحة قريباً')),
              );
            },
            child: const Text('تمديد'),
          ),
        ],
      ),
    );
  }

  void _showSubscriptionHistory(BuildContext context, SellerInfoModel seller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تاريخ الاشتراكات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المستخدم: ${seller.companyName ?? 'غير محدد'}'),
            const SizedBox(height: 8),
            Text('الباقة الحالية: ${seller.subscriptionName ?? 'غير محدد'}'),
            const SizedBox(height: 8),
            Text('تاريخ التسجيل: ${seller.subscriptionDate ?? 'غير محدد'}'),
            const SizedBox(height: 16),
            const Text('تاريخ الاشتراكات السابقة سيكون متاحاً قريباً'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _setDefaultPlan(SubscriptionPlanModel? plan, WidgetRef ref) async {
    if (plan != null) {
      try {
        // Save default plan to Firebase
        await ref.read(defaultSubscriptionRepositoryProvider).setDefaultPlan(plan);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تعيين "${plan.subscriptionName}" كباقة افتراضية')),
          );
        }

        // Refresh the default subscription provider
        ref.invalidate(defaultSubscriptionProvider);
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تعيين الباقة الافتراضية: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
