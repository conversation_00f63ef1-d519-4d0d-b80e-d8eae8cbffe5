class DefaultSubscriptionModel {
  String planId;
  String planName;
  int duration;
  bool isActive;
  DateTime createdAt;
  DateTime updatedAt;

  DefaultSubscriptionModel({
    required this.planId,
    required this.planName,
    required this.duration,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  DefaultSubscriptionModel.fromJson(Map<dynamic, dynamic> json)
      : planId = json['planId'] ?? '',
        planName = json['planName'] ?? '',
        duration = json['duration'] ?? 30,
        isActive = json['isActive'] ?? true,
        createdAt = json['createdAt'] != null 
            ? DateTime.parse(json['createdAt']) 
            : DateTime.now(),
        updatedAt = json['updatedAt'] != null 
            ? DateTime.parse(json['updatedAt']) 
            : DateTime.now();

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'planId': planId,
        'planName': planName,
        'duration': duration,
        'isActive': isActive,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };
}
