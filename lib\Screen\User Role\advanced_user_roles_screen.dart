import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../Widgets/Constant Data/constant.dart';
import '../../Provider/user_role_provider.dart';
import '../../model/user_role_model.dart';
import '../Widgets/Pop Up/User Role/create_role_dialog.dart';

class AdvancedUserRolesScreen extends StatefulWidget {
  const AdvancedUserRolesScreen({super.key});

  static const String route = '/advanced_user_roles';

  @override
  State<AdvancedUserRolesScreen> createState() => _AdvancedUserRolesScreenState();
}

class _AdvancedUserRolesScreenState extends State<AdvancedUserRolesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: kWhiteTextColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: kMainColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10.0),
                    topRight: Radius.circular(10.0),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedUserSettings01,
                      color: kMainColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إدارة أدوار المستخدمين المتقدمة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: kMainColor,
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton.icon(
                      onPressed: () => _showCreateRoleDialog(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: const Icon(HugeIcons.strokeRoundedAdd01, size: 18),
                      label: const Text('إنشاء دور جديد'),
                    ),
                  ],
                ),
              ),

              // Search Bar
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'البحث في الأدوار...',
                    prefixIcon: const Icon(HugeIcons.strokeRoundedSearch01),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: kMainColor),
                    ),
                  ),
                ),
              ),

              // Roles List
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final userRolesAsync = ref.watch(allAdminUserRoleProvider);

                    return userRolesAsync.when(
                      data: (userRoles) {
                        final filteredRoles = userRoles.where((role) {
                          return role.userTitle.toLowerCase().contains(_searchQuery) ||
                                 role.email.toLowerCase().contains(_searchQuery);
                        }).toList();

                        if (filteredRoles.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  HugeIcons.strokeRoundedSearchRemove,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _searchQuery.isEmpty
                                    ? 'لا توجد أدوار مستخدمين'
                                    : 'لا توجد نتائج للبحث',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: filteredRoles.length,
                          itemBuilder: (context, index) {
                            final role = filteredRoles[index];
                            return _buildRoleCard(context, role, ref);
                          },
                        );
                      },
                      loading: () => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      error: (error, stack) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              HugeIcons.strokeRoundedAlert02,
                              size: 64,
                              color: Colors.red.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'حدث خطأ في تحميل البيانات',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.red.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              error.toString(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard(BuildContext context, UserRoleModel role, WidgetRef ref) {
    final permissions = _getActivePermissions(role);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: kMainColor.withValues(alpha: 0.1),
                  child: Text(
                    role.userTitle.isNotEmpty ? role.userTitle[0].toUpperCase() : 'U',
                    style: TextStyle(
                      color: kMainColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        role.userTitle,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        role.email,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(context, value, role, ref),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedEdit02),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'permissions',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedSettings02),
                          SizedBox(width: 8),
                          Text('إدارة الصلاحيات'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(HugeIcons.strokeRoundedDelete02, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Permissions Summary
            Text(
              'الصلاحيات النشطة:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: permissions.map((permission) {
                return Chip(
                  label: Text(
                    permission,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: kMainColor.withValues(alpha: 0.1),
                  labelStyle: TextStyle(color: kMainColor),
                  side: BorderSide.none,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  List<String> _getActivePermissions(UserRoleModel role) {
    List<String> permissions = [];

    if (role.salePermission) permissions.add('المبيعات');
    if (role.partiesPermission) permissions.add('العملاء');
    if (role.purchasePermission) permissions.add('المشتريات');
    if (role.productPermission) permissions.add('المنتجات');
    if (role.profileEditPermission) permissions.add('تعديل الملف الشخصي');
    if (role.addExpensePermission) permissions.add('إضافة المصروفات');
    if (role.lossProfitPermission) permissions.add('الأرباح والخسائر');
    if (role.dueListPermission) permissions.add('قائمة المستحقات');
    if (role.stockPermission) permissions.add('المخزون');
    if (role.reportsPermission) permissions.add('التقارير');
    if (role.salesListPermission) permissions.add('قائمة المبيعات');
    if (role.purchaseListPermission) permissions.add('قائمة المشتريات');

    return permissions;
  }

  void _handleMenuAction(BuildContext context, String action, UserRoleModel role, WidgetRef ref) {
    switch (action) {
      case 'edit':
        _showEditRoleDialog(context, role);
        break;
      case 'permissions':
        _showPermissionsDialog(context, role);
        break;
      case 'delete':
        _showDeleteConfirmation(context, role, ref);
        break;
    }
  }

  void _showCreateRoleDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateRoleDialog(),
    );
  }

  void _showEditRoleDialog(BuildContext context, UserRoleModel role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل دور: ${role.userTitle}'),
        content: const Text('ميزة تعديل الأدوار ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showPermissionsDialog(BuildContext context, UserRoleModel role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('صلاحيات: ${role.userTitle}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPermissionItem('المبيعات', role.salePermission),
              _buildPermissionItem('العملاء والموردين', role.partiesPermission),
              _buildPermissionItem('المشتريات', role.purchasePermission),
              _buildPermissionItem('المنتجات', role.productPermission),
              _buildPermissionItem('تعديل الملف الشخصي', role.profileEditPermission),
              _buildPermissionItem('المصروفات', role.addExpensePermission),
              _buildPermissionItem('الأرباح والخسائر', role.lossProfitPermission),
              _buildPermissionItem('المستحقات', role.dueListPermission),
              _buildPermissionItem('المخزون', role.stockPermission),
              _buildPermissionItem('التقارير', role.reportsPermission),
              _buildPermissionItem('قائمة المبيعات', role.salesListPermission),
              _buildPermissionItem('قائمة المشتريات', role.purchaseListPermission),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionItem(String title, bool hasPermission) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            hasPermission ? Icons.check_circle : Icons.cancel,
            color: hasPermission ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, UserRoleModel role, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف دور "${role.userTitle}"؟\nهذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteRole(role, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteRole(UserRoleModel role, WidgetRef ref) async {
    try {
      EasyLoading.show(status: 'جاري الحذف...');

      if (role.userKey != null) {
        await FirebaseDatabase.instance
            .ref()
            .child('Admin Panel')
            .child('User Role')
            .child(role.userKey!)
            .remove();

        EasyLoading.showSuccess('تم حذف الدور بنجاح!');
        ref.invalidate(allAdminUserRoleProvider);
      } else {
        EasyLoading.showError('خطأ: لا يمكن حذف هذا الدور');
      }
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showError('فشل في حذف الدور: ${e.toString()}');
    }
  }
}
