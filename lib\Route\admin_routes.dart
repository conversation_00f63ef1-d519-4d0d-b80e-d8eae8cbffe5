import 'package:flutter/material.dart';
import '../Screen/Super Admin/super_admin_dashboard.dart';
import '../Screen/Advanced Admin/advanced_admin_permissions_screen.dart';

/// ملف التوجيه للشاشات الإدارية المتقدمة
class AdminRoutes {
  /// خريطة جميع المسارات الإدارية
  static Map<String, WidgetBuilder> get routes => {
    SuperAdminDashboard.route: (context) => const SuperAdminDashboard(),
    AdvancedAdminPermissionsScreen.route: (context) => const AdvancedAdminPermissionsScreen(),
  };

  /// التنقل إلى لوحة التحكم الشاملة
  static Future<void> navigateToSuperAdminDashboard(BuildContext context) async {
    await Navigator.pushNamed(context, SuperAdminDashboard.route);
  }

  /// التنقل إلى شاشة الصلاحيات المتقدمة
  static Future<void> navigateToAdvancedPermissions(BuildContext context) async {
    await Navigator.pushNamed(context, AdvancedAdminPermissionsScreen.route);
  }

  /// التنقل مع استبدال الشاشة الحالية
  static Future<void> navigateAndReplace(BuildContext context, String routeName) async {
    await Navigator.pushReplacementNamed(context, routeName);
  }

  /// التنقل وإزالة جميع الشاشات السابقة
  static Future<void> navigateAndClearStack(BuildContext context, String routeName) async {
    await Navigator.pushNamedAndRemoveUntil(
      context, 
      routeName, 
      (route) => false,
    );
  }

  /// التحقق من صحة المسار
  static bool isValidRoute(String routeName) {
    return routes.containsKey(routeName);
  }

  /// الحصول على جميع أسماء المسارات
  static List<String> get allRouteNames => routes.keys.toList();
}
