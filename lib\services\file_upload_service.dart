import 'package:flutter/foundation.dart';
import 'imgur_service.dart';
import 'gofile_upload_service.dart';
import 'image_upload_service.dart';

/// خدمة الرفع المركزية الذكية
class FileUploadService {
  /// رفع صورة (يستخدم Imgur أولاً، ثم GoFile كبديل)
  static Future<Map<String, dynamic>> uploadImage(Uint8List imageBytes,
      {String fileName = 'image.jpg'}) async {
    try {
      // التحقق من صحة الصورة
      if (!ImgurService.isValidImage(imageBytes)) {
        return {
          'success': false,
          'error': 'الصورة غير صالحة أو كبيرة جداً (أكثر من 10MB)',
        };
      }

      // ضغط الصورة إذا كانت كبيرة
      Uint8List? compressedImage = ImgurService.compressImage(imageBytes);
      compressedImage ??= imageBytes;

      // محاولة رفع الصورة على Imgur أولاً (إذا كان مكون)
      if (ImgurService.isConfigured) {
        debugPrint('🔄 محاولة رفع الصورة على Imgur...');
        final imgurResult = await ImgurService.uploadImage(compressedImage);

        if (imgurResult['success'] == true) {
          debugPrint('✅ تم رفع الصورة على Imgur بنجاح');

          // تسجيل الإحصائيات
          final sizeMB = imageBytes.length / (1024 * 1024);
          ImgurStats.recordUpload(true, sizeMB);

          return {
            'success': true,
            'service': 'imgur',
            'directLink': imgurResult['directLink'],
            'deleteHash': imgurResult['deleteHash'],
            'id': imgurResult['id'],
            'size': imgurResult['size'],
          };
        }

        debugPrint('⚠️ فشل رفع الصورة على Imgur، محاولة GoFile...');
      } else {
        debugPrint('⚠️ Imgur غير مكون، استخدام GoFile مباشرة...');
      }

      // استخدام الخدمة المحسنة للصور (GoFile)
      return await ImageUploadService.uploadImage(
        compressedImage,
        fileName: fileName,
      );
    } catch (e) {
      debugPrint('❌ خطأ عام في رفع الصورة: $e');
      return {
        'success': false,
        'error': 'خطأ عام في رفع الصورة: $e',
      };
    }
  }

  /// رفع ملف صوتي (يستخدم GoFile)
  static Future<Map<String, dynamic>> uploadVoiceNote(
    Uint8List audioBytes,
    String fileName,
  ) async {
    try {
      debugPrint('🔄 رفع ملف صوتي على GoFile...');
      final result = await GoFileService.uploadFile(
        audioBytes,
        fileName,
        mimeType: 'audio/mpeg',
      );

      if (result['success'] == true) {
        debugPrint('✅ تم رفع الملف الصوتي بنجاح');

        // تسجيل الإحصائيات
        final sizeGB = audioBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(true, sizeGB);
      } else {
        final sizeGB = audioBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(false, sizeGB);
      }

      return result;
    } catch (e) {
      debugPrint('❌ خطأ في رفع الملف الصوتي: $e');
      return {
        'success': false,
        'error': 'خطأ في رفع الملف الصوتي: $e',
      };
    }
  }

  /// رفع مستند (يستخدم GoFile)
  static Future<Map<String, dynamic>> uploadDocument(
    Uint8List documentBytes,
    String fileName,
  ) async {
    try {
      debugPrint('🔄 رفع مستند على GoFile...');
      final result = await GoFileService.uploadFile(
        documentBytes,
        fileName,
        mimeType: 'application/octet-stream',
      );

      if (result['success'] == true) {
        debugPrint('✅ تم رفع المستند بنجاح');

        // تسجيل الإحصائيات
        final sizeGB = documentBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(true, sizeGB);
      } else {
        final sizeGB = documentBytes.length / (1024 * 1024 * 1024);
        GoFileStats.recordUpload(false, sizeGB);
      }

      return result;
    } catch (e) {
      debugPrint('❌ خطأ في رفع المستند: $e');
      return {
        'success': false,
        'error': 'خطأ في رفع المستند: $e',
      };
    }
  }

  /// حذف ملف حسب الخدمة
  static Future<bool> deleteFile(String service, String identifier) async {
    try {
      switch (service.toLowerCase()) {
        case 'imgur':
          return await ImgurService.deleteImage(identifier);
        case 'gofile':
          return await GoFileService.deleteFile(identifier);
        default:
          debugPrint('❌ خدمة غير معروفة: $service');
          return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الملف: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات شاملة
  static Map<String, dynamic> getAllStats() {
    return {
      'images': ImageUploadService.getStats(),
      'gofile': GoFileStats.getStats(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// إعادة تعيين جميع الإحصائيات
  static void resetAllStats() {
    ImageUploadService.resetStats();
    GoFileStats.reset();
  }

  /// التحقق من حالة الخدمات
  static Future<Map<String, bool>> checkServicesStatus() async {
    final results = <String, bool>{};

    // فحص Imgur
    try {
      final testBytes = Uint8List.fromList([1, 2, 3]); // بيانات وهمية صغيرة
      final imgurResult = await ImgurService.uploadImage(testBytes);
      results['imgur'] = imgurResult['success'] == true;

      // حذف الصورة التجريبية إذا تم رفعها
      if (results['imgur'] == true && imgurResult['deleteHash'] != null) {
        await ImgurService.deleteImage(imgurResult['deleteHash']);
      }
    } catch (e) {
      results['imgur'] = false;
    }

    // فحص GoFile
    try {
      final server = await GoFileService.getBestServer();
      results['gofile'] = server.isNotEmpty;
    } catch (e) {
      results['gofile'] = false;
    }

    return results;
  }
}
