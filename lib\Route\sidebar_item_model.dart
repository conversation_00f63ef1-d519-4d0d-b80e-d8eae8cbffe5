import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import '../../Homepage Advertising/homepage_advertising.dart';
import '../Screen/Add user/add_user.dart';
import '../Screen/Dashboard/dashboard.dart';
import '../Screen/Payment Settings/payment_settings_screen.dart';
import '../Screen/Reports/reports.dart';
import '../Screen/Shop Category/shop_category.dart';
import '../Screen/Shop Management/shop_management.dart';
import '../Screen/Subscription Plans/subscription_plans.dart';
import '../Screen/Terms and Policy/terms_and_policy_screen.dart';

import '../Screen/Admin Notifications/admin_notifications_screen.dart';
import '../Screen/Admin Notifications/quick_admin_test.dart';
import '../Screen/Advanced Messaging/advanced_messaging_center.dart';

// class SidebarItemModel {
//   final String name;
//   final IconData iconPath;
//   final SidebarItemType sidebarItemType;
//   final List<SidebarSubmenuModel>? submenus;
//   final String? navigationPath;
//   final bool isPage;
//
//   SidebarItemModel({
//     required this.name,
//     required this.iconPath,
//     this.sidebarItemType = SidebarItemType.tile,
//     this.submenus,
//     this.navigationPath,
//     this.isPage = false,
//   }) : assert(
//   sidebarItemType != SidebarItemType.submenu ||
//       (submenus != null && submenus.isNotEmpty),
//   'Sub menus cannot be null or empty if the item type is submenu',
//   );
// }
//
// class SidebarSubmenuModel {
//   final String name;
//   final String? navigationPath;
//   final bool isPage;
//
//   SidebarSubmenuModel({
//     required this.name,
//     this.navigationPath,
//     this.isPage = false,
//   });
// }
//
//
// enum SidebarItemType { tile, submenu }

class SidebarItemModel {
  final String name;
  final IconData icon; // Renamed for clarity
  final SidebarItemType sidebarItemType;
  final List<SidebarSubmenuModel>? submenus;
  final String? navigationPath;
  final bool isPage;

  SidebarItemModel({
    required this.name,
    required this.icon,
    this.sidebarItemType = SidebarItemType.tile,
    this.submenus,
    this.navigationPath,
    this.isPage = false,
  }) : assert(
          sidebarItemType != SidebarItemType.submenu ||
              (submenus != null && submenus.isNotEmpty),
          'Sub menus cannot be null or empty if the item type is submenu',
        );
}

class SidebarSubmenuModel {
  final String name;
  final String? navigationPath;
  final bool isPage;

  SidebarSubmenuModel({
    required this.name,
    this.navigationPath,
    this.isPage = false,
  });
}

enum SidebarItemType { tile, submenu }

final topMenus = <SidebarItemModel>[
  SidebarItemModel(
    name: 'لوحة التحكم',
    icon: HugeIcons.strokeRoundedHome01,
    navigationPath: MtDashboard.route,
  ),
  SidebarItemModel(
    name: 'قائمة المتاجر',
    icon: HugeIcons.strokeRoundedStore03,
    navigationPath: ShopManagement.route,
  ),
  SidebarItemModel(
    name: 'فئات المتاجر',
    icon: HugeIcons.strokeRoundedLeftToRightListTriangle,
    navigationPath: ShopCategory.route,
  ),
  SidebarItemModel(
    name: 'التقارير',
    icon: HugeIcons.strokeRoundedDocumentValidation,
    navigationPath: Reports.route,
  ),
  SidebarItemModel(
    name: 'خطط الاشتراك',
    icon: HugeIcons.strokeRoundedLoyaltyCard,
    navigationPath: SubscriptionPlans.route,
  ),
  SidebarItemModel(
    name: 'إعدادات الدفع',
    icon: HugeIcons.strokeRoundedCreditCardValidation,
    navigationPath: PaymentSettings.route,
  ),
  SidebarItemModel(
    name: 'إعلانات الصفحة الرئيسية',
    icon: HugeIcons.strokeRoundedComputer,
    navigationPath: HomepageAdvertising.route,
  ),
  SidebarItemModel(
    name: 'أدوار المستخدمين',
    icon: HugeIcons.strokeRoundedUserSettings01,
    sidebarItemType: SidebarItemType.submenu,
    navigationPath: '/user_roles',
    submenus: [
      SidebarSubmenuModel(
        name: 'عرض الأدوار',
        navigationPath: '/overview',
      ),
      SidebarSubmenuModel(
        name: 'إدارة الأدوار المتقدمة',
        navigationPath: '/advanced',
      ),
      SidebarSubmenuModel(
        name: 'تفاصيل المستخدمين',
        navigationPath: '/details',
      ),
      SidebarSubmenuModel(
        name: 'إعدادات الصلاحيات',
        navigationPath: '/permissions',
      ),
      SidebarSubmenuModel(
        name: 'إدارة باقات المستخدمين',
        navigationPath: '/subscriptions',
      ),
    ],
  ),
  SidebarItemModel(
    name: 'إضافة مستخدم',
    icon: HugeIcons.strokeRoundedUserSettings01,
    navigationPath: AddUser.route,
  ),
  SidebarItemModel(
    name: 'الشروط والخصوصية',
    icon: HugeIcons.strokeRoundedAlert02,
    navigationPath: TermsAndPolicyScreen.route,
  ),
  SidebarItemModel(
    name: 'إدارة الإشعارات',
    icon: HugeIcons.strokeRoundedNotification03,
    navigationPath: AdminNotificationsScreen.route,
  ),
  SidebarItemModel(
    name: 'اختبار الإشعارات',
    icon: HugeIcons.strokeRoundedTestTube,
    navigationPath: QuickAdminTest.route,
  ),
  SidebarItemModel(
    name: 'مركز الإرسال المتقدم',
    icon: HugeIcons.strokeRoundedMessage02,
    navigationPath: AdvancedMessagingCenter.route,
  ),
];
