import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import '../model/advanced_notification_model.dart';

/// خدمة إدارة الإشعارات الإدارية الثابتة
/// تسمح للإدمن بإرسال إشعارات ثابتة لجميع المستخدمين في تطبيق Mobile POS
class AdminNotificationsService {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static const String _adminNotificationsPath = 'Admin_Notifications';
  static const String _globalNotificationsPath = 'Global_Notifications';

  /// إرسال إشعار إداري جديد
  static Future<bool> sendAdminNotification({
    required String title,
    required String message,
    required String type,
    String? actionUrl,
    String? imageUrl,
    Map<String, dynamic>? customData,
  }) async {
    try {
      final notification = AdminNotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        message: message,
        type: type,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        actionUrl: actionUrl,
        imageUrl: imageUrl,
        customData: customData ?? {},
      );

      // حفظ الإشعار في قاعدة البيانات الإدارية
      await _database
          .ref(_adminNotificationsPath)
          .child(notification.id)
          .set(notification.toJson());

      // إضافة الإشعار للإشعارات العامة المرئية لتطبيق Mobile POS
      await _database.ref(_globalNotificationsPath).child(notification.id).set({
        'id': notification.id,
        'title': notification.title,
        'message': notification.message,
        'type': notification.type,
        'isActive': notification.isActive,
        'createdAt': notification.createdAt.toIso8601String(),
        'actionUrl': notification.actionUrl,
        'imageUrl': notification.imageUrl,
        'customData': notification.customData,
      });

      debugPrint('✅ تم إرسال الإشعار الإداري بنجاح: ${notification.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار الإداري: $e');
      return false;
    }
  }

  /// تحديث حالة الإشعار (تفعيل/إلغاء تفعيل)
  static Future<bool> updateNotificationStatus(
    String notificationId,
    bool isActive,
  ) async {
    try {
      // تحديث في قاعدة بيانات الإدمن
      await _database.ref(_adminNotificationsPath).child(notificationId).update(
        {'isActive': isActive, 'updatedAt': DateTime.now().toIso8601String()},
      );

      if (isActive) {
        // إذا تم تفعيل الإشعار، أضفه للإشعارات العامة
        final notificationData = await _database
            .ref(_adminNotificationsPath)
            .child(notificationId)
            .get();

        if (notificationData.exists) {
          final data = Map<String, dynamic>.from(notificationData.value as Map);
          await _database
              .ref(_globalNotificationsPath)
              .child(notificationId)
              .set({
            'id': data['id'],
            'title': data['title'],
            'message': data['message'],
            'type': data['type'],
            'isActive': true,
            'createdAt': data['createdAt'],
            'actionUrl': data['actionUrl'],
            'imageUrl': data['imageUrl'],
            'customData': data['customData'] ?? {},
          });
        }
      } else {
        // إذا تم إلغاء تفعيل الإشعار، احذفه من الإشعارات العامة
        await _database
            .ref(_globalNotificationsPath)
            .child(notificationId)
            .remove();
      }

      debugPrint('✅ تم تحديث حالة الإشعار: $notificationId -> $isActive');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث حالة الإشعار: $e');
      return false;
    }
  }

  /// حذف إشعار إداري نهائياً
  static Future<bool> deleteAdminNotification(String notificationId) async {
    try {
      // حذف من قاعدة بيانات الإدمن
      await _database
          .ref(_adminNotificationsPath)
          .child(notificationId)
          .remove();

      // حذف من الإشعارات العامة
      await _database
          .ref(_globalNotificationsPath)
          .child(notificationId)
          .remove();

      debugPrint('✅ تم حذف الإشعار الإداري: $notificationId');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الإشعار الإداري: $e');
      return false;
    }
  }

  /// الحصول على جميع الإشعارات الإدارية
  static Future<List<AdminNotificationModel>> getAllAdminNotifications() async {
    try {
      final snapshot = await _database.ref(_adminNotificationsPath).get();

      if (!snapshot.exists) {
        return [];
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final notifications = <AdminNotificationModel>[];

      data.forEach((key, value) {
        try {
          final notification = AdminNotificationModel.fromJson(
            Map<String, dynamic>.from(value),
          );
          notifications.add(notification);
        } catch (e) {
          debugPrint('خطأ في تحليل الإشعار $key: $e');
        }
      });

      // ترتيب الإشعارات حسب تاريخ الإنشاء (الأحدث أولاً)
      notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return notifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإشعارات الإدارية: $e');
      return [];
    }
  }

  /// الحصول على الإشعارات النشطة للمستخدمين
  static Future<List<AdminNotificationModel>> getActiveNotifications() async {
    try {
      final snapshot = await _database.ref(_globalNotificationsPath).get();

      if (!snapshot.exists) {
        return [];
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final notifications = <AdminNotificationModel>[];

      data.forEach((key, value) {
        try {
          final notificationData = Map<String, dynamic>.from(value);
          final notification = AdminNotificationModel(
            id: notificationData['id'] ?? '',
            title: notificationData['title'] ?? '',
            message: notificationData['message'] ?? '',
            type: notificationData['type'] ?? 'info',
            isActive: notificationData['isActive'] ?? false,
            createdAt: DateTime.parse(
              notificationData['createdAt'] ?? DateTime.now().toIso8601String(),
            ),
            updatedAt: DateTime.now(),
            actionUrl: notificationData['actionUrl'],
            imageUrl: notificationData['imageUrl'],
            customData: Map<String, dynamic>.from(
              notificationData['customData'] ?? {},
            ),
          );
          notifications.add(notification);
        } catch (e) {
          debugPrint('خطأ في تحليل الإشعار النشط $key: $e');
        }
      });

      // ترتيب الإشعارات حسب تاريخ الإنشاء (الأحدث أولاً)
      notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return notifications;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإشعارات النشطة: $e');
      return [];
    }
  }

  /// مراقبة الإشعارات النشطة في الوقت الفعلي
  static Stream<List<AdminNotificationModel>> watchActiveNotifications() {
    return _database.ref(_globalNotificationsPath).onValue.map((event) {
      if (!event.snapshot.exists) {
        return <AdminNotificationModel>[];
      }

      final data = Map<String, dynamic>.from(event.snapshot.value as Map);
      final notifications = <AdminNotificationModel>[];

      data.forEach((key, value) {
        try {
          final notificationData = Map<String, dynamic>.from(value);
          final notification = AdminNotificationModel(
            id: notificationData['id'] ?? '',
            title: notificationData['title'] ?? '',
            message: notificationData['message'] ?? '',
            type: notificationData['type'] ?? 'info',
            isActive: notificationData['isActive'] ?? false,
            createdAt: DateTime.parse(
              notificationData['createdAt'] ?? DateTime.now().toIso8601String(),
            ),
            updatedAt: DateTime.now(),
            actionUrl: notificationData['actionUrl'],
            imageUrl: notificationData['imageUrl'],
            customData: Map<String, dynamic>.from(
              notificationData['customData'] ?? {},
            ),
          );
          notifications.add(notification);
        } catch (e) {
          debugPrint('خطأ في تحليل الإشعار النشط $key: $e');
        }
      });

      // ترتيب الإشعارات حسب تاريخ الإنشاء (الأحدث أولاً)
      notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return notifications;
    });
  }

  /// إرسال إشعار طوارئ عاجل
  static Future<bool> sendEmergencyNotification({
    required String title,
    required String message,
    String? actionUrl,
  }) async {
    return await sendAdminNotification(
      title: title,
      message: message,
      type: 'emergency',
      actionUrl: actionUrl,
      customData: {
        'priority': 'high',
        'emergency': true,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// إرسال إشعار صيانة
  static Future<bool> sendMaintenanceNotification({
    required String title,
    required String message,
    DateTime? scheduledTime,
    String? estimatedDuration,
  }) async {
    return await sendAdminNotification(
      title: title,
      message: message,
      type: 'maintenance',
      customData: {
        'scheduledTime': scheduledTime?.toIso8601String(),
        'estimatedDuration': estimatedDuration,
        'maintenanceType': 'scheduled',
      },
    );
  }

  /// إرسال إشعار ترويجي
  static Future<bool> sendPromotionalNotification({
    required String title,
    required String message,
    String? imageUrl,
    String? actionUrl,
    DateTime? expiryDate,
  }) async {
    return await sendAdminNotification(
      title: title,
      message: message,
      type: 'promotional',
      imageUrl: imageUrl,
      actionUrl: actionUrl,
      customData: {
        'expiryDate': expiryDate?.toIso8601String(),
        'promotional': true,
      },
    );
  }

  /// إحصائيات الإشعارات
  static Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final allNotifications = await getAllAdminNotifications();
      final activeNotifications = await getActiveNotifications();

      final stats = {
        'total': allNotifications.length,
        'active': activeNotifications.length,
        'inactive': allNotifications.length - activeNotifications.length,
        'byType': <String, int>{},
        'recentActivity': <String, dynamic>{},
      };

      // إحصائيات حسب النوع
      final byType = stats['byType'] as Map<String, int>;
      for (final notification in allNotifications) {
        final type = notification.type;
        byType[type] = (byType[type] ?? 0) + 1;
      }

      // النشاط الأخير
      if (allNotifications.isNotEmpty) {
        final latest = allNotifications.first;
        stats['recentActivity'] = {
          'latestNotificationId': latest.id,
          'latestNotificationTitle': latest.title,
          'latestNotificationDate': latest.createdAt.toIso8601String(),
        };
      }

      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في جلب إحصائيات الإشعارات: $e');
      return {};
    }
  }
}
