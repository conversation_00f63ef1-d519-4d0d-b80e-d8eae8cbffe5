/// تكوين خدمة Imgur
class ImgurConfig {
  // يجب الحصول على Client ID من https://api.imgur.com/oauth2/addclient
  static const String clientId = '2d2c52237921871';

  // رابط للحصول على Client ID
  static const String registrationUrl =
      'https://api.imgur.com/oauth2/addclient';

  // معلومات التطبيق المطلوبة للتسجيل
  static const Map<String, String> appInfo = {
    'Application name': 'AmrDevPOS Admin',
    'Authorization type': 'Anonymous usage without user authorization',
    'Authorization callback URL': 'https://amrdevpos.com/callback',
    'Application website': 'https://amrdevpos.com',
    'Email': '<EMAIL>',
    'Description': 'POS Admin Panel for uploading images and advertisements',
  };

  // تحقق من صحة التكوين
  static bool get isConfigured =>
      clientId != '2d2c52237921871' && clientId.isNotEmpty;

  // رسالة التكوين
  static String get configurationMessage => '''
🔧 تكوين خدمة Imgur مطلوب:

1. اذهب إلى: $registrationUrl
2. أنشئ تطبيق جديد بالمعلومات التالية:
   - Application name: ${appInfo['Application name']}
   - Authorization type: ${appInfo['Authorization type']}
   - Authorization callback URL: ${appInfo['Authorization callback URL']}
   - Application website: ${appInfo['Application website']}
   - Email: ${appInfo['Email']}
   - Description: ${appInfo['Description']}

3. انسخ Client ID واستبدله في ملف imgur_config.dart

بدون Client ID، ستستخدم الخدمة GoFile كبديل.
''';
}
