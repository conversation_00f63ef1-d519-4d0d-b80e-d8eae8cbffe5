import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../model/super_admin_model.dart';
import '../Widgets/Constant Data/constant.dart';
import '../Advanced Admin/advanced_admin_permissions_screen.dart';

class SuperAdminDashboard extends StatefulWidget {
  const SuperAdminDashboard({super.key});

  static const String route = '/super_admin_dashboard';

  @override
  State<SuperAdminDashboard> createState() => _SuperAdminDashboardState();
}

class _SuperAdminDashboardState extends State<SuperAdminDashboard> {
  SuperAdminModel? superAdmin;
  bool isLoading = true;

  // إحصائيات النظام
  int totalShops = 0;
  int activeShops = 0;
  int totalUsers = 0;
  int activeUsers = 0;
  int totalSubscriptions = 0;
  int activeSubscriptions = 0;
  double totalRevenue = 0.0;
  double monthlyRevenue = 0.0;

  @override
  void initState() {
    super.initState();
    _loadSuperAdminData();
    _loadSystemStats();
  }

  Future<void> _loadSuperAdminData() async {
    try {
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId != null) {
        final ref =
            FirebaseDatabase.instance.ref('Admin Panel/Super Admin/$userId');
        final snapshot = await ref.get();

        if (snapshot.exists && snapshot.value != null) {
          final data = snapshot.value as Map<dynamic, dynamic>;
          superAdmin = SuperAdminModel.fromJson(data);
        } else {
          // إنشاء أدمن شامل جديد
          superAdmin = SuperAdminModel.createSuperAdmin(
            email: FirebaseAuth.instance.currentUser?.email ?? '',
            userTitle: 'Super Admin',
            databaseId: userId,
          );

          // حفظ البيانات
          await ref.set(superAdmin!.toJson());
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأدمن الشامل: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _loadSystemStats() async {
    try {
      // تحميل إحصائيات المتاجر
      final shopsRef = FirebaseDatabase.instance.ref('Admin Panel/Seller List');
      final shopsSnapshot = await shopsRef.get();
      if (shopsSnapshot.exists) {
        totalShops = shopsSnapshot.children.length;
        activeShops = shopsSnapshot.children.where((shop) {
          final data = shop.value as Map<dynamic, dynamic>;
          return data['isActive'] == true;
        }).length;
      }

      // تحميل إحصائيات المستخدمين
      final usersRef = FirebaseDatabase.instance.ref('Admin Panel/User Role');
      final usersSnapshot = await usersRef.get();
      if (usersSnapshot.exists) {
        totalUsers = usersSnapshot.children.length;
        activeUsers = usersSnapshot.children.where((user) {
          final data = user.value as Map<dynamic, dynamic>;
          return data['isActive'] == true;
        }).length;
      }

      // تحميل إحصائيات الاشتراكات
      final subscriptionsRef = FirebaseDatabase.instance
          .ref('Admin Panel/Subscription Update Request');
      final subscriptionsSnapshot = await subscriptionsRef.get();
      if (subscriptionsSnapshot.exists) {
        totalSubscriptions = subscriptionsSnapshot.children.length;
        activeSubscriptions = subscriptionsSnapshot.children.where((sub) {
          final data = sub.value as Map<dynamic, dynamic>;
          return data['status'] == 'active';
        }).length;
      }

      setState(() {});
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: kDarkWhite,
      appBar: AppBar(
        title: Text(
          'لوحة التحكم الشاملة',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: kMainColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AdvancedAdminPermissionsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.admin_panel_settings, color: Colors.white),
            tooltip: 'الصلاحيات المتقدمة',
          ),
          IconButton(
            onPressed: _loadSystemStats,
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب بالأدمن
            _buildWelcomeCard(),
            const SizedBox(height: 20),

            // إحصائيات سريعة
            _buildQuickStats(),
            const SizedBox(height: 20),

            // أدوات التحكم السريع
            _buildQuickActions(),
            const SizedBox(height: 20),

            // إحصائيات الصلاحيات
            _buildPermissionsStats(),
            const SizedBox(height: 20),

            // التنبيهات والإشعارات
            _buildAlertsSection(),
            const SizedBox(height: 20),

            // الأنشطة الأخيرة
            _buildRecentActivities(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [kMainColor, kMainColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: kMainColor.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          const CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white,
            child: Icon(
              Icons.admin_panel_settings,
              size: 35,
              color: kMainColor,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً، ${superAdmin?.userTitle ?? 'Super Admin'}',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  'لديك تحكم كامل في جميع أجزاء النظام',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  'آخر تسجيل دخول: ${DateTime.now().toString().substring(0, 16)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            superAdmin?.isSuperAdmin() == true ? Icons.verified : Icons.warning,
            color: Colors.white,
            size: 30,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'المتاجر',
            value: '$activeShops / $totalShops',
            icon: Icons.store,
            color: Colors.blue,
            subtitle: 'نشط / إجمالي',
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: _buildStatCard(
            title: 'المستخدمين',
            value: '$activeUsers / $totalUsers',
            icon: Icons.people,
            color: Colors.green,
            subtitle: 'نشط / إجمالي',
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: _buildStatCard(
            title: 'الاشتراكات',
            value: '$activeSubscriptions / $totalSubscriptions',
            icon: Icons.subscriptions,
            color: Colors.orange,
            subtitle: 'نشط / إجمالي',
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: _buildStatCard(
            title: 'الإيرادات',
            value: '\$${totalRevenue.toStringAsFixed(0)}',
            icon: Icons.attach_money,
            color: Colors.purple,
            subtitle: 'إجمالي',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 30),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              fontSize: 10,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أدوات التحكم السريع',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: [
            _buildActionCard(
              title: 'إدارة المتاجر',
              icon: Icons.store_mall_directory,
              color: Colors.blue,
              onTap: () {
                // التنقل إلى إدارة المتاجر
              },
            ),
            _buildActionCard(
              title: 'إدارة الاشتراكات',
              icon: Icons.subscriptions,
              color: Colors.orange,
              onTap: () {
                // التنقل إلى إدارة الاشتراكات
              },
            ),
            _buildActionCard(
              title: 'إدارة المستخدمين',
              icon: Icons.people_alt,
              color: Colors.green,
              onTap: () {
                // التنقل إلى إدارة المستخدمين
              },
            ),
            _buildActionCard(
              title: 'التقارير المالية',
              icon: Icons.analytics,
              color: Colors.purple,
              onTap: () {
                // التنقل إلى التقارير المالية
              },
            ),
            _buildActionCard(
              title: 'إعدادات النظام',
              icon: Icons.settings,
              color: Colors.grey,
              onTap: () {
                // التنقل إلى إعدادات النظام
              },
            ),
            _buildActionCard(
              title: 'الأمان والحماية',
              icon: Icons.security,
              color: Colors.red,
              onTap: () {
                // التنقل إلى الأمان والحماية
              },
            ),
            _buildActionCard(
              title: 'النسخ الاحتياطي',
              icon: Icons.backup,
              color: Colors.teal,
              onTap: () {
                // التنقل إلى النسخ الاحتياطي
              },
            ),
            _buildActionCard(
              title: 'الصلاحيات المتقدمة',
              icon: Icons.admin_panel_settings,
              color: kMainColor,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const AdvancedAdminPermissionsScreen(),
                  ),
                );
              },
            ),
            _buildActionCard(
              title: 'إدارة الإشعارات',
              icon: Icons.notifications_active,
              color: Colors.indigo,
              onTap: () {
                Navigator.pushNamed(context, '/AdminNotifications');
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 30),
            const SizedBox(height: 8),
            Text(
              title,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsStats() {
    if (superAdmin == null) return const SizedBox();

    final basicPermissions = superAdmin!.hasAllBasicPermissions();
    final advancedPermissions = superAdmin!.hasAllAdvancedPermissions();
    final isSuperAdmin = superAdmin!.isSuperAdmin();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.admin_panel_settings,
                color: kMainColor,
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                'حالة الصلاحيات',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: kMainColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: _buildPermissionStatus(
                  title: 'الصلاحيات الأساسية',
                  isEnabled: basicPermissions,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildPermissionStatus(
                  title: 'الصلاحيات المتقدمة',
                  isEnabled: advancedPermissions,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: _buildPermissionStatus(
                  title: 'أدمن شامل',
                  isEnabled: isSuperAdmin,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionStatus({
    required String title,
    required bool isEnabled,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isEnabled
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEnabled ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.red,
            size: 30,
          ),
          const SizedBox(height: 5),
          Text(
            title,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isEnabled ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.notifications_active,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                'التنبيهات والإشعارات',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: kMainColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          _buildAlertItem(
            title: 'طلبات اشتراك جديدة',
            count: 5,
            color: Colors.blue,
            icon: Icons.new_releases,
          ),
          _buildAlertItem(
            title: 'متاجر تحتاج موافقة',
            count: 3,
            color: Colors.orange,
            icon: Icons.pending_actions,
          ),
          _buildAlertItem(
            title: 'مشاكل أمنية',
            count: 1,
            color: Colors.red,
            icon: Icons.security,
          ),
          _buildAlertItem(
            title: 'تحديثات النظام',
            count: 2,
            color: Colors.green,
            icon: Icons.system_update,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertItem({
    required String title,
    required int count,
    required Color color,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.history,
                color: kMainColor,
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                'الأنشطة الأخيرة',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: kMainColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          _buildActivityItem(
            title: 'تم إنشاء متجر جديد',
            subtitle: 'متجر الإلكترونيات الحديثة',
            time: 'منذ 5 دقائق',
            icon: Icons.store,
            color: Colors.green,
          ),
          _buildActivityItem(
            title: 'تم تحديث اشتراك',
            subtitle: 'ترقية إلى الباقة الذهبية',
            time: 'منذ 15 دقيقة',
            icon: Icons.upgrade,
            color: Colors.blue,
          ),
          _buildActivityItem(
            title: 'تم حذف مستخدم',
            subtitle: 'مستخدم غير نشط',
            time: 'منذ ساعة',
            icon: Icons.person_remove,
            color: Colors.red,
          ),
          _buildActivityItem(
            title: 'تم إنشاء نسخة احتياطية',
            subtitle: 'نسخة احتياطية يومية',
            time: 'منذ 3 ساعات',
            icon: Icons.backup,
            color: Colors.teal,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required String title,
    required String subtitle,
    required String time,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: color.withValues(alpha: 0.1),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: GoogleFonts.poppins(
              fontSize: 11,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
