import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import '../Widgets/Constant Data/constant.dart';

class PermissionsSettingsScreen extends StatefulWidget {
  const PermissionsSettingsScreen({super.key});

  static const String route = '/permissions_settings';

  @override
  State<PermissionsSettingsScreen> createState() => _PermissionsSettingsScreenState();
}

class _PermissionsSettingsScreenState extends State<PermissionsSettingsScreen> {
  final Map<String, bool> _permissions = {
    'salePermission': false,
    'partiesPermission': false,
    'purchasePermission': false,
    'productPermission': false,
    'profileEditPermission': false,
    'addExpensePermission': false,
    'lossProfitPermission': false,
    'dueListPermission': false,
    'stockPermission': false,
    'reportsPermission': false,
    'salesListPermission': false,
    'purchaseListPermission': false,
    'userManagementPermission': false,
    'systemSettingsPermission': false,
    'backupPermission': false,
    'auditLogPermission': false,
    'notificationPermission': false,
    'discountPermission': false,
    'refundPermission': false,
    'categoryManagementPermission': false,
  };

  final Map<String, String> _permissionLabels = {
    'salePermission': 'إدارة المبيعات',
    'partiesPermission': 'إدارة العملاء والموردين',
    'purchasePermission': 'إدارة المشتريات',
    'productPermission': 'إدارة المنتجات',
    'profileEditPermission': 'تعديل الملف الشخصي',
    'addExpensePermission': 'إضافة المصروفات',
    'lossProfitPermission': 'عرض الأرباح والخسائر',
    'dueListPermission': 'عرض قائمة المستحقات',
    'stockPermission': 'إدارة المخزون',
    'reportsPermission': 'عرض التقارير',
    'salesListPermission': 'عرض قائمة المبيعات',
    'purchaseListPermission': 'عرض قائمة المشتريات',
    'userManagementPermission': 'إدارة المستخدمين',
    'systemSettingsPermission': 'إعدادات النظام',
    'backupPermission': 'النسخ الاحتياطي',
    'auditLogPermission': 'سجل العمليات',
    'notificationPermission': 'إدارة الإشعارات',
    'discountPermission': 'إدارة الخصومات',
    'refundPermission': 'إدارة المرتجعات',
    'categoryManagementPermission': 'إدارة الفئات',
  };

  final Map<String, String> _permissionDescriptions = {
    'salePermission': 'السماح بإنشاء وتعديل وحذف المبيعات',
    'partiesPermission': 'إدارة بيانات العملاء والموردين',
    'purchasePermission': 'السماح بإنشاء وتعديل المشتريات',
    'productPermission': 'إضافة وتعديل وحذف المنتجات',
    'profileEditPermission': 'تعديل بيانات الملف الشخصي للمتجر',
    'addExpensePermission': 'إضافة وتسجيل المصروفات',
    'lossProfitPermission': 'عرض تقارير الأرباح والخسائر',
    'dueListPermission': 'عرض قائمة المبالغ المستحقة',
    'stockPermission': 'إدارة المخزون وحركة البضائع',
    'reportsPermission': 'الوصول إلى جميع التقارير',
    'salesListPermission': 'عرض قائمة جميع المبيعات',
    'purchaseListPermission': 'عرض قائمة جميع المشتريات',
    'userManagementPermission': 'إضافة وتعديل وحذف المستخدمين',
    'systemSettingsPermission': 'تعديل إعدادات النظام العامة',
    'backupPermission': 'إنشاء واستعادة النسخ الاحتياطية',
    'auditLogPermission': 'عرض سجل جميع العمليات والأنشطة',
    'notificationPermission': 'إرسال وإدارة الإشعارات',
    'discountPermission': 'إنشاء وتطبيق الخصومات والعروض',
    'refundPermission': 'معالجة المرتجعات واسترداد الأموال',
    'categoryManagementPermission': 'إدارة فئات المنتجات والخدمات',
  };

  final Map<String, IconData> _permissionIcons = {
    'salePermission': HugeIcons.strokeRoundedShoppingCart01,
    'partiesPermission': HugeIcons.strokeRoundedUserMultiple,
    'purchasePermission': HugeIcons.strokeRoundedShoppingBag01,
    'productPermission': HugeIcons.strokeRoundedPackage,
    'profileEditPermission': HugeIcons.strokeRoundedUserEdit01,
    'addExpensePermission': HugeIcons.strokeRoundedMoneyReceive01,
    'lossProfitPermission': HugeIcons.strokeRoundedAnalytics01,
    'dueListPermission': HugeIcons.strokeRoundedInvoice01,
    'stockPermission': HugeIcons.strokeRoundedWarehouse,
    'reportsPermission': HugeIcons.strokeRoundedDocumentValidation,
    'salesListPermission': HugeIcons.strokeRoundedSaleTag01,
    'purchaseListPermission': HugeIcons.strokeRoundedShoppingBasket01,
    'userManagementPermission': HugeIcons.strokeRoundedUserSettings01,
    'systemSettingsPermission': HugeIcons.strokeRoundedSettings02,
    'backupPermission': HugeIcons.strokeRoundedCloudUpload,
    'auditLogPermission': HugeIcons.strokeRoundedFileSearch,
    'notificationPermission': HugeIcons.strokeRoundedNotification03,
    'discountPermission': HugeIcons.strokeRoundedSaleTag01,
    'refundPermission': HugeIcons.strokeRoundedMoneyReceive01,
    'categoryManagementPermission': HugeIcons.strokeRoundedLeftToRightListTriangle,
  };

  String _selectedTemplate = 'custom';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: kWhiteTextColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: kMainColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10.0),
                    topRight: Radius.circular(10.0),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      HugeIcons.strokeRoundedSettings02,
                      color: kMainColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إعدادات الصلاحيات',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: kMainColor,
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton.icon(
                      onPressed: _savePermissions,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: const Icon(HugeIcons.strokeRoundedCheckmarkCircle02, size: 18),
                      label: const Text('حفظ الإعدادات'),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Templates Section
                      _buildTemplatesSection(),

                      const SizedBox(height: 24),

                      // Permissions Section
                      _buildPermissionsSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplatesSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'قوالب الصلاحيات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر قالب جاهز أو قم بتخصيص الصلاحيات يدوياً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildTemplateCard(
                  'admin',
                  'مدير كامل',
                  'جميع الصلاحيات',
                  HugeIcons.strokeRoundedCrown,
                  Colors.purple,
                ),
                _buildTemplateCard(
                  'manager',
                  'مدير',
                  'صلاحيات إدارية أساسية',
                  HugeIcons.strokeRoundedUserSettings01,
                  Colors.blue,
                ),
                _buildTemplateCard(
                  'employee',
                  'موظف',
                  'صلاحيات محدودة',
                  HugeIcons.strokeRoundedUser,
                  Colors.green,
                ),
                _buildTemplateCard(
                  'custom',
                  'مخصص',
                  'تخصيص يدوي',
                  HugeIcons.strokeRoundedSettings02,
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateCard(String value, String title, String description, IconData icon, Color color) {
    final isSelected = _selectedTemplate == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTemplate = value;
          _applyTemplate(value);
        });
      },
      child: Container(
        width: 160,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey.shade50,
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey.shade600,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: isSelected ? color : Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'الصلاحيات التفصيلية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _selectAll,
                  icon: const Icon(HugeIcons.strokeRoundedCheckmarkSquare02, size: 16),
                  label: const Text('تحديد الكل'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: _deselectAll,
                  icon: const Icon(HugeIcons.strokeRoundedSquare, size: 16),
                  label: const Text('إلغاء الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ...(_permissions.keys.map((key) {
              return _buildPermissionTile(key);
            }).toList()),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionTile(String permissionKey) {
    final isEnabled = _permissions[permissionKey] ?? false;
    final label = _permissionLabels[permissionKey] ?? permissionKey;
    final description = _permissionDescriptions[permissionKey] ?? '';
    final icon = _permissionIcons[permissionKey] ?? HugeIcons.strokeRoundedSettings02;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SwitchListTile(
        value: isEnabled,
        onChanged: (value) {
          setState(() {
            _permissions[permissionKey] = value;
            if (value == false || _permissions.values.any((v) => !v)) {
              _selectedTemplate = 'custom';
            }
          });
        },
        title: Row(
          children: [
            Icon(
              icon,
              color: isEnabled ? kMainColor : Colors.grey.shade400,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: isEnabled ? Colors.black : Colors.grey.shade600,
              ),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(right: 32),
          child: Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ),
        activeColor: kMainColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  void _applyTemplate(String template) {
    switch (template) {
      case 'admin':
        _permissions.updateAll((key, value) => true);
        break;
      case 'manager':
        _permissions.updateAll((key, value) => false);
        _permissions['salePermission'] = true;
        _permissions['partiesPermission'] = true;
        _permissions['productPermission'] = true;
        _permissions['reportsPermission'] = true;
        _permissions['salesListPermission'] = true;
        _permissions['stockPermission'] = true;
        break;
      case 'employee':
        _permissions.updateAll((key, value) => false);
        _permissions['salePermission'] = true;
        _permissions['productPermission'] = true;
        _permissions['salesListPermission'] = true;
        break;
      case 'custom':
        // Keep current permissions
        break;
    }
  }

  void _selectAll() {
    setState(() {
      _permissions.updateAll((key, value) => true);
      _selectedTemplate = 'admin';
    });
  }

  void _deselectAll() {
    setState(() {
      _permissions.updateAll((key, value) => false);
      _selectedTemplate = 'custom';
    });
  }

  void _savePermissions() {
    // Save permissions to local storage or send to server
    final selectedPermissions = _permissions.entries
        .where((entry) => entry.value)
        .map((entry) => _permissionLabels[entry.key] ?? entry.key)
        .toList();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حفظ ${selectedPermissions.length} صلاحية بنجاح'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () {
            _undoLastSave();
          },
        ),
      ),
    );
  }

  void _undoLastSave() {
    // Reset permissions to previous state
    setState(() {
      _permissions.updateAll((key, value) => false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم التراجع عن آخر حفظ'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
