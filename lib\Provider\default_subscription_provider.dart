import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_database/firebase_database.dart';
import '../model/default_subscription_model.dart';
import '../model/subscription_plan_model.dart';

class DefaultSubscriptionRepository {
  final DatabaseReference _ref = FirebaseDatabase.instance.ref();

  Future<DefaultSubscriptionModel?> getDefaultPlan() async {
    try {
      final snapshot = await _ref.child('Admin Panel').child('Default Subscription').get();
      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return DefaultSubscriptionModel.fromJson(data);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get default plan: $e');
    }
  }

  Future<void> setDefaultPlan(SubscriptionPlanModel plan) async {
    try {
      final defaultPlan = DefaultSubscriptionModel(
        planId: plan.subscriptionName, // Using name as ID for now
        planName: plan.subscriptionName,
        duration: plan.duration,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _ref.child('Admin Panel').child('Default Subscription').set(defaultPlan.toJson());
    } catch (e) {
      throw Exception('Failed to set default plan: $e');
    }
  }

  Future<void> removeDefaultPlan() async {
    try {
      await _ref.child('Admin Panel').child('Default Subscription').remove();
    } catch (e) {
      throw Exception('Failed to remove default plan: $e');
    }
  }

  Future<void> assignDefaultPlanToUser(String userId) async {
    try {
      final defaultPlan = await getDefaultPlan();
      if (defaultPlan != null) {
        // Get the full subscription plan details
        final plansSnapshot = await _ref.child('Admin Panel').child('Subscription Plan').get();
        if (plansSnapshot.exists) {
          SubscriptionPlanModel? matchingPlan;
          
          for (var child in plansSnapshot.children) {
            final planData = child.value as Map<dynamic, dynamic>;
            final plan = SubscriptionPlanModel.fromJson(planData);
            if (plan.subscriptionName == defaultPlan.planName) {
              matchingPlan = plan;
              break;
            }
          }

          if (matchingPlan != null) {
            // Create subscription for user
            final userSubscription = {
              'subscriptionName': matchingPlan.subscriptionName,
              'subscriptionDate': DateTime.now().toString(),
              'saleNumber': matchingPlan.saleNumber,
              'purchaseNumber': matchingPlan.purchaseNumber,
              'partiesNumber': matchingPlan.partiesNumber,
              'dueNumber': matchingPlan.dueNumber,
              'duration': matchingPlan.duration,
              'products': matchingPlan.products,
            };

            await _ref.child(userId).child('Subscription').set(userSubscription);

            // Update seller info with subscription details
            final sellerSnapshot = await _ref.child('Admin Panel').child('Seller List')
                .orderByChild('userId').equalTo(userId).get();
            
            if (sellerSnapshot.exists) {
              for (var child in sellerSnapshot.children) {
                await child.ref.update({
                  'subscriptionName': matchingPlan.subscriptionName,
                  'subscriptionDate': DateTime.now().toString(),
                });
              }
            }
          }
        }
      }
    } catch (e) {
      throw Exception('Failed to assign default plan to user: $e');
    }
  }
}

final defaultSubscriptionRepositoryProvider = Provider<DefaultSubscriptionRepository>((ref) {
  return DefaultSubscriptionRepository();
});

final defaultSubscriptionProvider = FutureProvider<DefaultSubscriptionModel?>((ref) {
  final repository = ref.watch(defaultSubscriptionRepositoryProvider);
  return repository.getDefaultPlan();
});

final setDefaultPlanProvider = FutureProvider.family<void, SubscriptionPlanModel>((ref, plan) {
  final repository = ref.watch(defaultSubscriptionRepositoryProvider);
  return repository.setDefaultPlan(plan);
});

final assignDefaultPlanProvider = FutureProvider.family<void, String>((ref, userId) {
  final repository = ref.watch(defaultSubscriptionRepositoryProvider);
  return repository.assignDefaultPlanToUser(userId);
});
