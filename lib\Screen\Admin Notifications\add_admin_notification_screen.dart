import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker_web/image_picker_web.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../Provider/admin_notifications_provider.dart';
import '../../model/advanced_notification_model.dart';
import '../../Screen/Widgets/Constant Data/constant.dart';
import '../../services/file_upload_service.dart';
import '../../services/image_compression_service.dart';

/// شاشة إضافة أو تعديل إشعار إداري
class AddAdminNotificationScreen extends ConsumerStatefulWidget {
  final AdminNotificationModel? editNotification;
  final AdminNotificationModel? duplicateNotification;

  const AddAdminNotificationScreen({
    super.key,
    this.editNotification,
    this.duplicateNotification,
  });

  @override
  ConsumerState<AddAdminNotificationScreen> createState() =>
      _AddAdminNotificationScreenState();
}

class _AddAdminNotificationScreenState
    extends ConsumerState<AddAdminNotificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  final _actionUrlController = TextEditingController();
  final _imageUrlController = TextEditingController();

  String _selectedType = 'info';
  bool _isActive = true;
  DateTime? _expiryDate;
  String? _estimatedDuration;
  DateTime? _scheduledTime;

  // متغيرات رفع الصور
  Uint8List? _selectedImageBytes;
  String? _uploadedImageUrl;
  bool _isUploadingImage = false;

  bool get _isEditing => widget.editNotification != null;
  bool get _isDuplicating => widget.duplicateNotification != null;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    final notification =
        widget.editNotification ?? widget.duplicateNotification;

    if (notification != null) {
      _titleController.text = notification.title;
      _messageController.text = notification.message;
      _selectedType = notification.type;
      _isActive = _isDuplicating ? true : notification.isActive;
      _actionUrlController.text = notification.actionUrl ?? '';
      _imageUrlController.text = notification.imageUrl ?? '';
      _uploadedImageUrl = notification.imageUrl;

      // استخراج البيانات المخصصة
      if (notification.customData['expiryDate'] != null) {
        _expiryDate = DateTime.parse(notification.customData['expiryDate']);
      }
      _estimatedDuration = notification.customData['estimatedDuration'];
      if (notification.customData['scheduledTime'] != null) {
        _scheduledTime =
            DateTime.parse(notification.customData['scheduledTime']);
      }
    }
  }

  // دالة رفع الصورة
  Future<void> _uploadNotificationImage() async {
    if (kIsWeb) {
      try {
        setState(() {
          _isUploadingImage = true;
        });

        EasyLoading.show(
          status: 'جاري رفع صورة الإشعار...',
          dismissOnTap: false,
        );

        Uint8List? bytesFromPicker = await ImagePickerWeb.getImageAsBytes();

        if (bytesFromPicker != null && bytesFromPicker.isNotEmpty) {
          // ضغط الصورة قبل الرفع
          final compressedImage =
              ImageCompressionService.smartCompress(bytesFromPicker);
          final finalImage = compressedImage ?? bytesFromPicker;

          // رفع الصورة باستخدام الخدمة الذكية (Imgur أولاً)
          final result = await FileUploadService.uploadImage(
            finalImage,
            fileName:
                'notification_${DateTime.now().millisecondsSinceEpoch}.jpg',
          );

          if (result['success'] == true) {
            setState(() {
              _selectedImageBytes = bytesFromPicker;
              _uploadedImageUrl = result['directLink'];
              _imageUrlController.text = result['directLink'];
            });

            EasyLoading.showSuccess(
              result['service'] == 'imgur'
                  ? 'تم رفع الصورة على Imgur بنجاح!'
                  : 'تم رفع الصورة على GoFile بنجاح!',
            );
          } else {
            EasyLoading.showError('فشل في رفع الصورة: ${result['error']}');
          }
        } else {
          EasyLoading.showError('لم يتم اختيار صورة');
        }
      } catch (e) {
        EasyLoading.dismiss();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في رفع الصورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isUploadingImage = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    _actionUrlController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = ref.watch(adminNotificationsProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          _isEditing
              ? 'تعديل الإشعار'
              : _isDuplicating
                  ? 'نسخ الإشعار'
                  : 'إضافة إشعار جديد',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          if (_isEditing || _isDuplicating)
            IconButton(
              icon: const Icon(Icons.preview, color: Colors.white),
              onPressed: _previewNotification,
              tooltip: 'معاينة',
            ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: provider.isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildTypeSection(),
                      const SizedBox(height: 24),
                      _buildAdvancedOptionsSection(),
                      const SizedBox(height: 32),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _titleController,
          decoration: kInputDecoration.copyWith(
            labelText: 'عنوان الإشعار *',
            hintText: 'أدخل عنوان الإشعار',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'عنوان الإشعار مطلوب';
            }
            if (value.trim().length < 3) {
              return 'عنوان الإشعار يجب أن يكون 3 أحرف على الأقل';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _messageController,
          maxLines: 4,
          decoration: kInputDecoration.copyWith(
            labelText: 'محتوى الإشعار *',
            hintText: 'أدخل محتوى الإشعار',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'محتوى الإشعار مطلوب';
            }
            if (value.trim().length < 10) {
              return 'محتوى الإشعار يجب أن يكون 10 أحرف على الأقل';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Text(
              'حالة الإشعار:',
              style: GoogleFonts.cairo(fontSize: 16),
            ),
            const SizedBox(width: 12),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              activeColor: kMainColor,
            ),
            Text(
              _isActive ? 'نشط' : 'معطل',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: _isActive ? Colors.green : Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الإشعار',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _selectedType,
          decoration: kInputDecoration.copyWith(
            labelText: 'اختر نوع الإشعار',
          ),
          items: AdvancedNotificationTypes.all.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Row(
                children: [
                  Text(
                    _getTypeIcon(type),
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        AdvancedNotificationTypes.getArabicName(type),
                        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        AdvancedNotificationTypes.getDescription(type),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Color(int.parse(
                    _getTypeColor(_selectedType).replaceFirst('#', '0xFF')))
                .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Color(int.parse(
                      _getTypeColor(_selectedType).replaceFirst('#', '0xFF')))
                  .withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Text(
                _getTypeIcon(_selectedType),
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'معاينة: ${AdvancedNotificationTypes.getDescription(_selectedType)}',
                  style: GoogleFonts.cairo(
                    color: Color(int.parse(_getTypeColor(_selectedType)
                        .replaceFirst('#', '0xFF'))),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خيارات متقدمة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _actionUrlController,
          decoration: kInputDecoration.copyWith(
            labelText: 'رابط الإجراء (اختياري)',
            hintText: 'https://example.com',
            prefixIcon: const Icon(Icons.link),
          ),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return 'رابط غير صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // قسم رفع الصورة المحسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.image, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'صورة الإشعار (اختيارية)',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // معاينة الصورة المرفوعة
              if (_selectedImageBytes != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      _selectedImageBytes!,
                      height: 150,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                )
              else if (_uploadedImageUrl != null &&
                  _uploadedImageUrl!.isNotEmpty)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      _uploadedImageUrl!,
                      height: 150,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 150,
                          color: Colors.grey[200],
                          child: const Center(
                            child: Icon(Icons.broken_image, color: Colors.grey),
                          ),
                        );
                      },
                    ),
                  ),
                ),

              // أزرار التحكم
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed:
                          _isUploadingImage ? null : _uploadNotificationImage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      icon: _isUploadingImage
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.upload),
                      label: Text(
                        _isUploadingImage ? 'جاري الرفع...' : 'رفع صورة',
                        style: GoogleFonts.cairo(),
                      ),
                    ),
                  ),
                  if (_uploadedImageUrl != null) ...[
                    const SizedBox(width: 12),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _selectedImageBytes = null;
                          _uploadedImageUrl = null;
                          _imageUrlController.clear();
                        });
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف الصورة',
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // حقل رابط الصورة (للإدخال اليدوي)
              TextFormField(
                controller: _imageUrlController,
                decoration: kInputDecoration.copyWith(
                  labelText: 'أو أدخل رابط الصورة يدوياً',
                  hintText: 'https://example.com/image.jpg',
                  prefixIcon: const Icon(Icons.link),
                ),
                onChanged: (value) {
                  if (value.isNotEmpty && _selectedImageBytes == null) {
                    setState(() {
                      _uploadedImageUrl = value;
                    });
                  }
                },
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final uri = Uri.tryParse(value);
                    if (uri == null || !uri.hasAbsolutePath) {
                      return 'رابط الصورة غير صحيح';
                    }
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // خيارات خاصة بنوع الإشعار
        if (_selectedType == 'promotional') ...[
          ListTile(
            title: Text(
              'تاريخ انتهاء الصلاحية',
              style: GoogleFonts.cairo(),
            ),
            subtitle: Text(
              _expiryDate != null
                  ? 'ينتهي في: ${_formatDate(_expiryDate!)}'
                  : 'لا يوجد تاريخ انتهاء',
              style: GoogleFonts.cairo(fontSize: 12),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.calendar_today),
              onPressed: _selectExpiryDate,
            ),
          ),
        ],

        if (_selectedType == 'maintenance') ...[
          ListTile(
            title: Text(
              'موعد الصيانة المجدولة',
              style: GoogleFonts.cairo(),
            ),
            subtitle: Text(
              _scheduledTime != null
                  ? 'مجدولة في: ${_formatDate(_scheduledTime!)}'
                  : 'لم يتم تحديد موعد',
              style: GoogleFonts.cairo(fontSize: 12),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.schedule),
              onPressed: _selectScheduledTime,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            initialValue: _estimatedDuration,
            decoration: kInputDecoration.copyWith(
              labelText: 'المدة المتوقعة للصيانة',
              hintText: 'مثال: ساعتان',
            ),
            onChanged: (value) {
              _estimatedDuration = value;
            },
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: kMainColor),
            ),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: kMainColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveNotification,
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              _isEditing ? 'تحديث' : 'إرسال الإشعار',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _expiryDate ?? DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _expiryDate = date;
      });
    }
  }

  void _selectScheduledTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _scheduledTime ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_scheduledTime ?? DateTime.now()),
      );

      if (time != null) {
        setState(() {
          _scheduledTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  void _previewNotification() {
    if (!_formKey.currentState!.validate()) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(
              _getTypeIcon(_selectedType),
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'معاينة الإشعار',
                style: GoogleFonts.cairo(fontSize: 16),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Color(int.parse(
                        _getTypeColor(_selectedType).replaceFirst('#', '0xFF')))
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(int.parse(_getTypeColor(_selectedType)
                          .replaceFirst('#', '0xFF')))
                      .withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _titleController.text,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(int.parse(_getTypeColor(_selectedType)
                          .replaceFirst('#', '0xFF'))),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _messageController.text,
                    style: GoogleFonts.cairo(fontSize: 14),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'النوع: ${AdvancedNotificationTypes.getArabicName(_selectedType)}',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    'الحالة: ${_isActive ? "نشط" : "معطل"}',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: _isActive ? Colors.green : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );
  }

  void _saveNotification() async {
    if (!_formKey.currentState!.validate()) return;

    final provider = ref.read(adminNotificationsProvider);

    // إعداد البيانات المخصصة
    final customData = <String, dynamic>{};

    if (_selectedType == 'promotional' && _expiryDate != null) {
      customData['expiryDate'] = _expiryDate!.toIso8601String();
      customData['promotional'] = true;
    }

    if (_selectedType == 'maintenance') {
      if (_scheduledTime != null) {
        customData['scheduledTime'] = _scheduledTime!.toIso8601String();
      }
      if (_estimatedDuration != null && _estimatedDuration!.isNotEmpty) {
        customData['estimatedDuration'] = _estimatedDuration;
      }
      customData['maintenanceType'] = 'scheduled';
    }

    if (_selectedType == 'emergency') {
      customData['priority'] = 'high';
      customData['emergency'] = true;
      customData['timestamp'] = DateTime.now().toIso8601String();
    }

    bool success;

    if (_isEditing) {
      // تحديث الإشعار الموجود
      success = await provider.sendNotification(
        title: _titleController.text.trim(),
        message: _messageController.text.trim(),
        type: _selectedType,
        actionUrl: _actionUrlController.text.trim().isEmpty
            ? null
            : _actionUrlController.text.trim(),
        imageUrl: _imageUrlController.text.trim().isEmpty
            ? null
            : _imageUrlController.text.trim(),
        customData: customData,
      );

      if (success) {
        // حذف الإشعار القديم
        await provider.deleteNotification(widget.editNotification!.id);
      }
    } else {
      // إنشاء إشعار جديد
      success = await provider.sendNotification(
        title: _titleController.text.trim(),
        message: _messageController.text.trim(),
        type: _selectedType,
        actionUrl: _actionUrlController.text.trim().isEmpty
            ? null
            : _actionUrlController.text.trim(),
        imageUrl: _imageUrlController.text.trim().isEmpty
            ? null
            : _imageUrlController.text.trim(),
        customData: customData,
      );
    }

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث الإشعار بنجاح' : 'تم إرسال الإشعار بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'فشل في تحديث الإشعار' : 'فشل في إرسال الإشعار',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getTypeIcon(String type) {
    switch (type) {
      case 'emergency':
        return '🚨';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'maintenance':
        return '🔧';
      case 'update':
        return '🔄';
      case 'promotional':
        return '🎉';
      default:
        return '📢';
    }
  }

  String _getTypeColor(String type) {
    switch (type) {
      case 'emergency':
        return '#FF0000';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'success':
        return '#4CAF50';
      case 'info':
        return '#2196F3';
      case 'maintenance':
        return '#9C27B0';
      case 'update':
        return '#00BCD4';
      case 'promotional':
        return '#FF5722';
      default:
        return '#757575';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
