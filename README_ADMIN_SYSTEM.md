# نظام الصلاحيات الشاملة للأدمن - تطبيق POS

## 🚀 نظرة عامة

تم تطوير نظام صلاحيات شامل ومتقدم لتطبيق نقاط البيع (POS) يمنحك التحكم الكامل في جميع جوانب النظام. هذا النظام يوفر مستويات متعددة من الصلاحيات لضمان الأمان والمرونة في الإدارة.

## ✨ الميزات الرئيسية

### 🔐 نظام صلاحيات متعدد المستويات
- **الصلاحيات الأساسية**: للعمليات اليومية الأساسية
- **الصلاحيات المتقدمة**: للتحكم الشامل في النظام
- **صلاحيات الطوارئ**: للوصول في الحالات الحرجة
- **صلاحيات المطور**: للصيانة والتطوير

### 💼 إدارة الاشتراكات المتقدمة
- إنشاء خطط اشتراك مخصصة
- تجميد وإلغاء الاشتراكات
- منح فترات تجريبية مجانية
- إدارة الخصومات والعروض
- تحليلات الاشتراكات المفصلة

### 🛡️ الأمان والحماية
- المصادقة الثنائية
- التحكم في عناوين IP
- سجلات التدقيق الشاملة
- مراقبة الجلسات
- تنبيهات الأمان

### 📊 التحليلات والتقارير
- تحليلات ذكية للأعمال
- تقارير مخصصة
- تحليلات تنبؤية
- تصدير البيانات
- مراقبة الأداء

### ⚙️ إدارة النظام
- مراقبة الأداء في الوقت الفعلي
- إدارة قاعدة البيانات
- النسخ الاحتياطي التلقائي
- وضع الصيانة
- إدارة الخوادم

## 🏗️ البنية التقنية

### الملفات الرئيسية

```
lib/
├── model/
│   ├── advanced_admin_permissions_model.dart    # نموذج الصلاحيات المتقدمة
│   ├── super_admin_model.dart                   # نموذج الأدمن الشامل
│   └── user_role_model.dart                     # نموذج أدوار المستخدمين
├── Screen/
│   ├── Advanced Admin/
│   │   └── advanced_admin_permissions_screen.dart  # شاشة الصلاحيات المتقدمة
│   └── Super Admin/
│       └── super_admin_dashboard.dart              # لوحة التحكم الشاملة
└── docs/
    └── admin_permissions_guide.md               # دليل الاستخدام
```

### النماذج (Models)

#### 1. AdvancedAdminPermissionsModel
```dart
class AdvancedAdminPermissionsModel {
  // صلاحيات إدارة الاشتراكات المتقدمة (9 صلاحيات)
  late bool subscriptionManagementPermission;
  late bool createCustomPlansPermission;
  // ... المزيد
  
  // صلاحيات إدارة النظام (8 صلاحيات)
  late bool systemSettingsPermission;
  late bool databaseManagementPermission;
  // ... المزيد
  
  // صلاحيات الأمان والحماية (8 صلاحيات)
  late bool securityManagementPermission;
  late bool passwordPolicyPermission;
  // ... المزيد
  
  // إجمالي 72+ صلاحية متقدمة
}
```

#### 2. SuperAdminModel
```dart
class SuperAdminModel {
  // الصلاحيات الأساسية
  late bool salePermission;
  late bool partiesPermission;
  // ... المزيد
  
  // الصلاحيات المتقدمة
  late AdvancedAdminPermissionsModel advancedPermissions;
  
  // دوال مساعدة
  bool isSuperAdmin();
  bool hasAllBasicPermissions();
  void setAllPermissions(bool value);
}
```

## 🚀 كيفية الاستخدام

### 1. إنشاء أدمن شامل

```dart
// إنشاء أدمن شامل بجميع الصلاحيات
final superAdmin = SuperAdminModel.createSuperAdmin(
  email: '<EMAIL>',
  userTitle: 'Super Administrator',
  databaseId: 'unique_id',
);

// حفظ في Firebase
await FirebaseDatabase.instance
    .ref('Admin Panel/Super Admin/${superAdmin.databaseId}')
    .set(superAdmin.toJson());
```

### 2. التحقق من الصلاحيات

```dart
// التحقق من صلاحية محددة
if (superAdmin.advancedPermissions.subscriptionManagementPermission) {
  // السماح بإدارة الاشتراكات
}

// التحقق من كونه أدمن شامل
if (superAdmin.isSuperAdmin()) {
  // السماح بجميع العمليات
}
```

### 3. الوصول إلى الشاشات

```dart
// لوحة التحكم الشاملة
Navigator.pushNamed(context, SuperAdminDashboard.route);

// شاشة الصلاحيات المتقدمة
Navigator.pushNamed(context, AdvancedAdminPermissionsScreen.route);
```

## 📱 الشاشات

### 1. لوحة التحكم الشاملة (SuperAdminDashboard)
- إحصائيات النظام الشاملة
- أدوات التحكم السريع
- حالة الصلاحيات
- التنبيهات والإشعارات
- الأنشطة الأخيرة

### 2. شاشة الصلاحيات المتقدمة (AdvancedAdminPermissionsScreen)
- إدارة جميع الصلاحيات المتقدمة
- إحصائيات الصلاحيات
- تفعيل/إلغاء الصلاحيات
- حفظ التغييرات

## 🔧 التكوين

### 1. إعداد Firebase

```dart
// في main.dart
await Firebase.initializeApp();

// في pubspec.yaml
dependencies:
  firebase_core: ^latest
  firebase_database: ^latest
  firebase_auth: ^latest
```

### 2. إعداد الصلاحيات

```dart
// تحميل الصلاحيات من Firebase
final ref = FirebaseDatabase.instance.ref('Admin Panel/Advanced Permissions/$userId');
final snapshot = await ref.get();

if (snapshot.exists) {
  final permissions = AdvancedAdminPermissionsModel.fromJson(snapshot.value);
}
```

## 🛡️ الأمان

### مستويات الحماية
1. **التشفير**: جميع البيانات مشفرة
2. **المصادقة**: مصادقة ثنائية إجبارية
3. **التدقيق**: سجلات شاملة لجميع العمليات
4. **المراقبة**: مراقبة في الوقت الفعلي

### أفضل الممارسات
- تغيير كلمات المرور بانتظام
- مراجعة سجلات التدقيق
- تحديث الصلاحيات حسب الحاجة
- إنشاء نسخ احتياطية منتظمة

## 📊 الإحصائيات والتحليلات

### المتاح حالياً
- إحصائيات المتاجر والمستخدمين
- تحليلات الاشتراكات
- مراقبة الأداء
- تقارير الأنشطة

### قريباً
- تحليلات تنبؤية متقدمة
- تقارير مخصصة
- ذكاء اصطناعي للتحليلات
- تكاملات خارجية

## 🚀 التطوير المستقبلي

### الميزات المخططة
- [ ] نظام إشعارات متقدم
- [ ] تكاملات API خارجية
- [ ] تحليلات ذكية بالذكاء الاصطناعي
- [ ] نظام تقارير مرئية
- [ ] دعم العملات المتعددة
- [ ] نظام إدارة المحتوى

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] تحسين واجهة المستخدم
- [ ] دعم الوضع المظلم
- [ ] تحسين الاستجابة
- [ ] دعم اللغات المتعددة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## 📞 الدعم

للحصول على الدعم:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 الدردشة المباشرة في التطبيق
- 📱 الهاتف: +**********

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا النظام المتقدم.

---

**تم التطوير بـ ❤️ لتطبيق POS Admin**
