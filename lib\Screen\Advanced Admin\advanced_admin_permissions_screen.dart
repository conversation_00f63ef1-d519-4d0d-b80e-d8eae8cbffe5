import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../model/advanced_admin_permissions_model.dart';
import '../Widgets/Constant Data/constant.dart';

class AdvancedAdminPermissionsScreen extends StatefulWidget {
  const AdvancedAdminPermissionsScreen({super.key});

  static const String route = '/advanced_admin_permissions';

  @override
  State<AdvancedAdminPermissionsScreen> createState() =>
      _AdvancedAdminPermissionsScreenState();
}

class _AdvancedAdminPermissionsScreenState
    extends State<AdvancedAdminPermissionsScreen> {
  late AdvancedAdminPermissionsModel permissions;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPermissions();
  }

  Future<void> _loadPermissions() async {
    try {
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId != null) {
        final ref = FirebaseDatabase.instance
            .ref('Admin Panel/Advanced Permissions/$userId');
        final snapshot = await ref.get();

        if (snapshot.exists && snapshot.value != null) {
          final data = snapshot.value as Map<dynamic, dynamic>;
          permissions = AdvancedAdminPermissionsModel.fromJson(data);
        } else {
          permissions = AdvancedAdminPermissionsModel();
        }
      } else {
        permissions = AdvancedAdminPermissionsModel();
      }
    } catch (e) {
      permissions = AdvancedAdminPermissionsModel();
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _savePermissions() async {
    try {
      EasyLoading.show(status: 'جاري الحفظ...');

      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId != null) {
        final ref = FirebaseDatabase.instance
            .ref('Admin Panel/Advanced Permissions/$userId');
        await ref.set(permissions.toJson());

        EasyLoading.showSuccess('تم حفظ الصلاحيات بنجاح');
      } else {
        EasyLoading.showError('خطأ: لم يتم تسجيل الدخول');
      }
    } catch (e) {
      EasyLoading.showError('خطأ في حفظ الصلاحيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: kDarkWhite,
      appBar: AppBar(
        title: Text(
          'الصلاحيات المتقدمة للأدمن',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: kMainColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => permissions.setAllPermissions(true),
            icon: const Icon(Icons.select_all, color: Colors.white),
            tooltip: 'تحديد الكل',
          ),
          IconButton(
            onPressed: () => permissions.setAllPermissions(false),
            icon: const Icon(Icons.deselect, color: Colors.white),
            tooltip: 'إلغاء تحديد الكل',
          ),
          IconButton(
            onPressed: _savePermissions,
            icon: const Icon(Icons.save, color: Colors.white),
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // إحصائيات سريعة
            _buildStatsCard(),
            const SizedBox(height: 20),

            // صلاحيات إدارة الاشتراكات المتقدمة
            _buildPermissionSection(
              title: 'إدارة الاشتراكات المتقدمة',
              icon: Icons.subscriptions,
              color: Colors.blue,
              permissions: [
                _buildPermissionTile('إدارة الاشتراكات العامة',
                    permissions.subscriptionManagementPermission, (value) {
                  setState(() =>
                      permissions.subscriptionManagementPermission = value);
                }),
                _buildPermissionTile(
                    'إنشاء خطط مخصصة', permissions.createCustomPlansPermission,
                    (value) {
                  setState(
                      () => permissions.createCustomPlansPermission = value);
                }),
                _buildPermissionTile('تعديل خطط الاشتراك',
                    permissions.modifySubscriptionPlansPermission, (value) {
                  setState(() =>
                      permissions.modifySubscriptionPlansPermission = value);
                }),
                _buildPermissionTile('تجميد الاشتراكات',
                    permissions.freezeSubscriptionsPermission, (value) {
                  setState(
                      () => permissions.freezeSubscriptionsPermission = value);
                }),
                _buildPermissionTile('إلغاء الاشتراكات',
                    permissions.cancelSubscriptionsPermission, (value) {
                  setState(
                      () => permissions.cancelSubscriptionsPermission = value);
                }),
                _buildPermissionTile('منح فترات تجريبية مجانية',
                    permissions.grantFreeTrialsPermission, (value) {
                  setState(() => permissions.grantFreeTrialsPermission = value);
                }),
                _buildPermissionTile('إدارة الخصومات والعروض',
                    permissions.manageDiscountsPermission, (value) {
                  setState(() => permissions.manageDiscountsPermission = value);
                }),
                _buildPermissionTile('عرض تحليلات الاشتراكات',
                    permissions.viewSubscriptionAnalyticsPermission, (value) {
                  setState(() =>
                      permissions.viewSubscriptionAnalyticsPermission = value);
                }),
                _buildPermissionTile('العمليات المجمعة للاشتراكات',
                    permissions.bulkSubscriptionOperationsPermission, (value) {
                  setState(() =>
                      permissions.bulkSubscriptionOperationsPermission = value);
                }),
              ],
            ),

            const SizedBox(height: 20),

            // صلاحيات إدارة النظام
            _buildPermissionSection(
              title: 'إدارة النظام',
              icon: Icons.settings_system_daydream,
              color: Colors.orange,
              permissions: [
                _buildPermissionTile('إعدادات النظام العامة',
                    permissions.systemSettingsPermission, (value) {
                  setState(() => permissions.systemSettingsPermission = value);
                }),
                _buildPermissionTile('إدارة قاعدة البيانات',
                    permissions.databaseManagementPermission, (value) {
                  setState(
                      () => permissions.databaseManagementPermission = value);
                }),
                _buildPermissionTile('النسخ الاحتياطي والاستعادة',
                    permissions.backupRestorePermission, (value) {
                  setState(() => permissions.backupRestorePermission = value);
                }),
                _buildPermissionTile(
                    'إدارة الخوادم', permissions.serverManagementPermission,
                    (value) {
                  setState(
                      () => permissions.serverManagementPermission = value);
                }),
                _buildPermissionTile(
                    'مراقبة النظام', permissions.systemMonitoringPermission,
                    (value) {
                  setState(
                      () => permissions.systemMonitoringPermission = value);
                }),
                _buildPermissionTile('تحليلات الأداء',
                    permissions.performanceAnalyticsPermission, (value) {
                  setState(
                      () => permissions.performanceAnalyticsPermission = value);
                }),
                _buildPermissionTile(
                    'تنبيهات النظام', permissions.systemAlertsPermission,
                    (value) {
                  setState(() => permissions.systemAlertsPermission = value);
                }),
                _buildPermissionTile(
                    'وضع الصيانة', permissions.maintenanceModePermission,
                    (value) {
                  setState(() => permissions.maintenanceModePermission = value);
                }),
              ],
            ),

            const SizedBox(height: 20),

            // صلاحيات الأمان والحماية
            _buildPermissionSection(
              title: 'الأمان والحماية',
              icon: Icons.security,
              color: Colors.red,
              permissions: [
                _buildPermissionTile('إدارة الأمان العامة',
                    permissions.securityManagementPermission, (value) {
                  setState(
                      () => permissions.securityManagementPermission = value);
                }),
                _buildPermissionTile(
                    'سياسات كلمات المرور', permissions.passwordPolicyPermission,
                    (value) {
                  setState(() => permissions.passwordPolicyPermission = value);
                }),
                _buildPermissionTile(
                    'سجلات التدقيق', permissions.auditLogsPermission, (value) {
                  setState(() => permissions.auditLogsPermission = value);
                }),
                _buildPermissionTile(
                    'إدارة الجلسات', permissions.sessionManagementPermission,
                    (value) {
                  setState(
                      () => permissions.sessionManagementPermission = value);
                }),
                _buildPermissionTile(
                    'التحكم في IP المسموح', permissions.ipControlPermission,
                    (value) {
                  setState(() => permissions.ipControlPermission = value);
                }),
                _buildPermissionTile(
                    'المصادقة الثنائية', permissions.twoFactorAuthPermission,
                    (value) {
                  setState(() => permissions.twoFactorAuthPermission = value);
                }),
                _buildPermissionTile(
                    'إدارة التشفير', permissions.encryptionManagementPermission,
                    (value) {
                  setState(
                      () => permissions.encryptionManagementPermission = value);
                }),
                _buildPermissionTile(
                    'تنبيهات الأمان', permissions.securityAlertsPermission,
                    (value) {
                  setState(() => permissions.securityAlertsPermission = value);
                }),
              ],
            ),

            const SizedBox(height: 20),

            // صلاحيات التحكم المالي المتقدم
            _buildPermissionSection(
              title: 'التحكم المالي المتقدم',
              icon: Icons.account_balance,
              color: Colors.green,
              permissions: [
                _buildPermissionTile('التحكم المالي المتقدم',
                    permissions.advancedFinancialControlPermission, (value) {
                  setState(() =>
                      permissions.advancedFinancialControlPermission = value);
                }),
                _buildPermissionTile('إدارة بوابات الدفع',
                    permissions.paymentGatewayManagementPermission, (value) {
                  setState(() =>
                      permissions.paymentGatewayManagementPermission = value);
                }),
                _buildPermissionTile(
                    'إدارة الفواتير', permissions.invoiceManagementPermission,
                    (value) {
                  setState(
                      () => permissions.invoiceManagementPermission = value);
                }),
                _buildPermissionTile(
                    'حدود الإنفاق', permissions.spendingLimitsPermission,
                    (value) {
                  setState(() => permissions.spendingLimitsPermission = value);
                }),
                _buildPermissionTile('إدارة العمولات',
                    permissions.commissionManagementPermission, (value) {
                  setState(
                      () => permissions.commissionManagementPermission = value);
                }),
                _buildPermissionTile(
                    'إدارة العملات', permissions.currencyManagementPermission,
                    (value) {
                  setState(
                      () => permissions.currencyManagementPermission = value);
                }),
                _buildPermissionTile(
                    'إدارة الضرائب', permissions.taxManagementPermission,
                    (value) {
                  setState(() => permissions.taxManagementPermission = value);
                }),
                _buildPermissionTile('التقارير المالية المتقدمة',
                    permissions.financialReportsPermission, (value) {
                  setState(
                      () => permissions.financialReportsPermission = value);
                }),
              ],
            ),

            const SizedBox(height: 20),

            // صلاحيات إدارة المحتوى والإعلانات
            _buildPermissionSection(
              title: 'إدارة المحتوى والإعلانات',
              icon: Icons.campaign,
              color: Colors.purple,
              permissions: [
                _buildPermissionTile(
                    'إدارة المحتوى', permissions.contentManagementPermission,
                    (value) {
                  setState(
                      () => permissions.contentManagementPermission = value);
                }),
                _buildPermissionTile('إدارة الإعلانات',
                    permissions.advertisingManagementPermission, (value) {
                  setState(() =>
                      permissions.advertisingManagementPermission = value);
                }),
                _buildPermissionTile('التسويق عبر البريد الإلكتروني',
                    permissions.emailMarketingPermission, (value) {
                  setState(() => permissions.emailMarketingPermission = value);
                }),
                _buildPermissionTile('التسويق عبر الرسائل النصية',
                    permissions.smsMarketingPermission, (value) {
                  setState(() => permissions.smsMarketingPermission = value);
                }),
                _buildPermissionTile('إدارة الإشعارات',
                    permissions.notificationManagementPermission, (value) {
                  setState(() =>
                      permissions.notificationManagementPermission = value);
                }),
                _buildPermissionTile('إدارة وسائل التواصل الاجتماعي',
                    permissions.socialMediaManagementPermission, (value) {
                  setState(() =>
                      permissions.socialMediaManagementPermission = value);
                }),
              ],
            ),

            const SizedBox(height: 100), // مساحة إضافية في الأسفل
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _savePermissions,
        backgroundColor: kMainColor,
        icon: const Icon(Icons.save, color: Colors.white),
        label: Text(
          'حفظ الصلاحيات',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    const totalPermissions = 72; // إجمالي عدد الصلاحيات المتقدمة
    final enabledPermissions = _countEnabledPermissions();
    final percentage = (enabledPermissions / totalPermissions * 100).round();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائيات الصلاحيات',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: kMainColor,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'الصلاحيات المفعلة: $enabledPermissions من $totalPermissions',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  'النسبة المئوية: $percentage%',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          CircularProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              percentage > 75
                  ? Colors.green
                  : percentage > 50
                      ? Colors.orange
                      : Colors.red,
            ),
            strokeWidth: 8,
          ),
          const SizedBox(width: 10),
          Text(
            '$percentage%',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: percentage > 75
                  ? Colors.green
                  : percentage > 50
                      ? Colors.orange
                      : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  int _countEnabledPermissions() {
    int count = 0;

    // صلاحيات إدارة الاشتراكات المتقدمة
    if (permissions.subscriptionManagementPermission) count++;
    if (permissions.createCustomPlansPermission) count++;
    if (permissions.modifySubscriptionPlansPermission) count++;
    if (permissions.freezeSubscriptionsPermission) count++;
    if (permissions.cancelSubscriptionsPermission) count++;
    if (permissions.grantFreeTrialsPermission) count++;
    if (permissions.manageDiscountsPermission) count++;
    if (permissions.viewSubscriptionAnalyticsPermission) count++;
    if (permissions.bulkSubscriptionOperationsPermission) count++;

    // صلاحيات إدارة النظام
    if (permissions.systemSettingsPermission) count++;
    if (permissions.databaseManagementPermission) count++;
    if (permissions.backupRestorePermission) count++;
    if (permissions.serverManagementPermission) count++;
    if (permissions.systemMonitoringPermission) count++;
    if (permissions.performanceAnalyticsPermission) count++;
    if (permissions.systemAlertsPermission) count++;
    if (permissions.maintenanceModePermission) count++;

    // صلاحيات الأمان والحماية
    if (permissions.securityManagementPermission) count++;
    if (permissions.passwordPolicyPermission) count++;
    if (permissions.auditLogsPermission) count++;
    if (permissions.sessionManagementPermission) count++;
    if (permissions.ipControlPermission) count++;
    if (permissions.twoFactorAuthPermission) count++;
    if (permissions.encryptionManagementPermission) count++;
    if (permissions.securityAlertsPermission) count++;

    // صلاحيات التحكم المالي المتقدم
    if (permissions.advancedFinancialControlPermission) count++;
    if (permissions.paymentGatewayManagementPermission) count++;
    if (permissions.invoiceManagementPermission) count++;
    if (permissions.spendingLimitsPermission) count++;
    if (permissions.commissionManagementPermission) count++;
    if (permissions.currencyManagementPermission) count++;
    if (permissions.taxManagementPermission) count++;
    if (permissions.financialReportsPermission) count++;

    // صلاحيات إدارة المحتوى والإعلانات
    if (permissions.contentManagementPermission) count++;
    if (permissions.advertisingManagementPermission) count++;
    if (permissions.emailMarketingPermission) count++;
    if (permissions.smsMarketingPermission) count++;
    if (permissions.notificationManagementPermission) count++;
    if (permissions.socialMediaManagementPermission) count++;

    // إضافة باقي الصلاحيات...
    if (permissions.advancedShopManagementPermission) count++;
    if (permissions.shopVerificationPermission) count++;
    if (permissions.shopSuspensionPermission) count++;
    if (permissions.shopAnalyticsPermission) count++;
    if (permissions.shopSettingsPermission) count++;
    if (permissions.shopCategoriesManagementPermission) count++;

    if (permissions.advancedUserManagementPermission) count++;
    if (permissions.userVerificationPermission) count++;
    if (permissions.userSuspensionPermission) count++;
    if (permissions.userAnalyticsPermission) count++;
    if (permissions.massUserOperationsPermission) count++;
    if (permissions.userRoleManagementPermission) count++;

    if (permissions.advancedReportsPermission) count++;
    if (permissions.businessIntelligencePermission) count++;
    if (permissions.dataExportPermission) count++;
    if (permissions.customReportsPermission) count++;
    if (permissions.realTimeAnalyticsPermission) count++;
    if (permissions.predictiveAnalyticsPermission) count++;

    if (permissions.apiManagementPermission) count++;
    if (permissions.thirdPartyIntegrationsPermission) count++;
    if (permissions.webhookManagementPermission) count++;
    if (permissions.apiKeysManagementPermission) count++;

    if (permissions.qualityControlPermission) count++;
    if (permissions.customerSupportManagementPermission) count++;
    if (permissions.ticketManagementPermission) count++;
    if (permissions.feedbackManagementPermission) count++;

    if (permissions.emergencyAccessPermission) count++;
    if (permissions.systemShutdownPermission) count++;
    if (permissions.dataRecoveryPermission) count++;
    if (permissions.emergencyNotificationsPermission) count++;

    if (permissions.developerModePermission) count++;
    if (permissions.debugModePermission) count++;
    if (permissions.logManagementPermission) count++;
    if (permissions.codeDeploymentPermission) count++;

    if (permissions.advancedInventoryPermission) count++;
    if (permissions.inventoryForecastingPermission) count++;
    if (permissions.supplierManagementPermission) count++;
    if (permissions.warehouseManagementPermission) count++;

    if (permissions.advancedHRPermission) count++;
    if (permissions.payrollManagementPermission) count++;
    if (permissions.attendanceManagementPermission) count++;
    if (permissions.performanceManagementPermission) count++;

    return count;
  }

  Widget _buildPermissionSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> permissions,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(icon, color: color, size: 28),
        title: Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        children: permissions,
      ),
    );
  }

  Widget _buildPermissionTile(
      String title, bool value, Function(bool) onChanged) {
    return ListTile(
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: kMainColor,
      ),
      onTap: () => onChanged(!value),
    );
  }
}
