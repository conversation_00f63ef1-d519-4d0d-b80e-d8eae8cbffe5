import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'l10n_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/l10n.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S? of(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @mrp.
  ///
  /// In en, this message translates to:
  /// **'MRP'**
  String get mrp;

  /// No description provided for @userTitle.
  ///
  /// In en, this message translates to:
  /// **'User Title'**
  String get userTitle;

  /// No description provided for @yourPackageWillExpireTodayPleasePurchaseagain.
  ///
  /// In en, this message translates to:
  /// **'Your Package Will Expire Today\n\nPlease Purchase again'**
  String get yourPackageWillExpireTodayPleasePurchaseagain;

  /// No description provided for @theSystemIsProvided.
  ///
  /// In en, this message translates to:
  /// **'(a) The System is provided solely for the purpose of facilitating point of sale transactions and related activities in your business.'**
  String get theSystemIsProvided;

  /// No description provided for @toUseTheSystem.
  ///
  /// In en, this message translates to:
  /// **'(a) To use the System, you may be required to create an account. You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate and complete.'**
  String get toUseTheSystem;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get accept;

  /// No description provided for @acceptanceOfTerms.
  ///
  /// In en, this message translates to:
  /// **'Acceptance of Terms'**
  String get acceptanceOfTerms;

  /// No description provided for @accepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get accepted;

  /// No description provided for @account.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// No description provided for @accountName.
  ///
  /// In en, this message translates to:
  /// **'Account Name'**
  String get accountName;

  /// No description provided for @accountNumber.
  ///
  /// In en, this message translates to:
  /// **'Account Number'**
  String get accountNumber;

  /// No description provided for @accountType.
  ///
  /// In en, this message translates to:
  /// **'Account Type'**
  String get accountType;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @addBrand.
  ///
  /// In en, this message translates to:
  /// **'Add Brand'**
  String get addBrand;

  /// No description provided for @addCategory.
  ///
  /// In en, this message translates to:
  /// **'Add Category'**
  String get addCategory;

  /// No description provided for @addCustomer.
  ///
  /// In en, this message translates to:
  /// **'Add Customer'**
  String get addCustomer;

  /// No description provided for @addDelivery.
  ///
  /// In en, this message translates to:
  /// **'Add Delivery'**
  String get addDelivery;

  /// No description provided for @addDocumentId.
  ///
  /// In en, this message translates to:
  /// **'Add Document ID'**
  String get addDocumentId;

  /// No description provided for @addDucument.
  ///
  /// In en, this message translates to:
  /// **'Add Document'**
  String get addDucument;

  /// No description provided for @addExpense.
  ///
  /// In en, this message translates to:
  /// **'Add Expense'**
  String get addExpense;

  /// No description provided for @addExpenseCategory.
  ///
  /// In en, this message translates to:
  /// **'Add Expense Category'**
  String get addExpenseCategory;

  /// No description provided for @addItems.
  ///
  /// In en, this message translates to:
  /// **'Add Items'**
  String get addItems;

  /// No description provided for @addNewAddress.
  ///
  /// In en, this message translates to:
  /// **'Add New Address'**
  String get addNewAddress;

  /// No description provided for @addNewProduct.
  ///
  /// In en, this message translates to:
  /// **'Add New Product'**
  String get addNewProduct;

  /// No description provided for @addNote.
  ///
  /// In en, this message translates to:
  /// **'Add Note'**
  String get addNote;

  /// No description provided for @addPurchase.
  ///
  /// In en, this message translates to:
  /// **'Add Purchase'**
  String get addPurchase;

  /// No description provided for @addSales.
  ///
  /// In en, this message translates to:
  /// **'Add Sales'**
  String get addSales;

  /// No description provided for @addSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Added Successfully'**
  String get addSuccessful;

  /// No description provided for @addUnit.
  ///
  /// In en, this message translates to:
  /// **'Add Unit'**
  String get addUnit;

  /// No description provided for @addUserRole.
  ///
  /// In en, this message translates to:
  /// **'Add User Role'**
  String get addUserRole;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @allBusinessSolution.
  ///
  /// In en, this message translates to:
  /// **'All Business Solutions'**
  String get allBusinessSolution;

  /// No description provided for @alreadyHaveAnAccounts.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAnAccounts;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @androidIOSAppSupport.
  ///
  /// In en, this message translates to:
  /// **'Android & iOS App Support'**
  String get androidIOSAppSupport;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @areYourSureDeleteThisUser.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this user?'**
  String get areYourSureDeleteThisUser;

  /// No description provided for @backToHome.
  ///
  /// In en, this message translates to:
  /// **'Back To Home'**
  String get backToHome;

  /// No description provided for @balance.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// No description provided for @bankAccountNumber.
  ///
  /// In en, this message translates to:
  /// **'Bank Account Number'**
  String get bankAccountNumber;

  /// No description provided for @bankAccounts.
  ///
  /// In en, this message translates to:
  /// **'Bank Accounts'**
  String get bankAccounts;

  /// No description provided for @bankDetails.
  ///
  /// In en, this message translates to:
  /// **'Bank Details'**
  String get bankDetails;

  /// No description provided for @bankName.
  ///
  /// In en, this message translates to:
  /// **'Bank Name'**
  String get bankName;

  /// No description provided for @barcodeORqrCodeScanner.
  ///
  /// In en, this message translates to:
  /// **'Barcode/QR Code Scanner'**
  String get barcodeORqrCodeScanner;

  /// No description provided for @basicInformation.
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get basicInformation;

  /// No description provided for @youMustBeAtLeastYearsOld.
  ///
  /// In en, this message translates to:
  /// **'(b) You must be at least 18 years old or the legal age of majority in your jurisdiction to use the System.'**
  String get youMustBeAtLeastYearsOld;

  /// No description provided for @brand.
  ///
  /// In en, this message translates to:
  /// **'Brand'**
  String get brand;

  /// No description provided for @brandName.
  ///
  /// In en, this message translates to:
  /// **'Brand Name'**
  String get brandName;

  /// No description provided for @businessCategory.
  ///
  /// In en, this message translates to:
  /// **'Business Category'**
  String get businessCategory;

  /// No description provided for @businessCategoryPlaceHolder.
  ///
  /// In en, this message translates to:
  /// **'Enter your business category'**
  String get businessCategoryPlaceHolder;

  /// No description provided for @businessIdentificationNumber.
  ///
  /// In en, this message translates to:
  /// **'Business Identification Number'**
  String get businessIdentificationNumber;

  /// No description provided for @businessName.
  ///
  /// In en, this message translates to:
  /// **'Business Name'**
  String get businessName;

  /// No description provided for @businessNamePlaceHolder.
  ///
  /// In en, this message translates to:
  /// **'Enter your business name'**
  String get businessNamePlaceHolder;

  /// No description provided for @businessType.
  ///
  /// In en, this message translates to:
  /// **'Business Type'**
  String get businessType;

  /// No description provided for @buyPremiumPlan.
  ///
  /// In en, this message translates to:
  /// **'Buy Premium Plan'**
  String get buyPremiumPlan;

  /// No description provided for @buySms.
  ///
  /// In en, this message translates to:
  /// **'Buy SMS'**
  String get buySms;

  /// No description provided for @buysmsPlan.
  ///
  /// In en, this message translates to:
  /// **'Buy SMS Plan'**
  String get buysmsPlan;

  /// No description provided for @theUserCanCreateSalesInvoice.
  ///
  /// In en, this message translates to:
  /// **'(c) The user can create sales invoices, purchase invoices, add stock, add expense, and view sales/purchase/expense/loss profit reports.'**
  String get theUserCanCreateSalesInvoice;

  /// No description provided for @call.
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @capacity.
  ///
  /// In en, this message translates to:
  /// **'Capacity'**
  String get capacity;

  /// No description provided for @cash.
  ///
  /// In en, this message translates to:
  /// **'Cash'**
  String get cash;

  /// No description provided for @cashAndBank.
  ///
  /// In en, this message translates to:
  /// **'Cash & Bank'**
  String get cashAndBank;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @categoryName.
  ///
  /// In en, this message translates to:
  /// **'Category Name'**
  String get categoryName;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @checkEmail.
  ///
  /// In en, this message translates to:
  /// **'Check Email'**
  String get checkEmail;

  /// No description provided for @choseYourFeature.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Features'**
  String get choseYourFeature;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @cityPinCode.
  ///
  /// In en, this message translates to:
  /// **'City Pin Code'**
  String get cityPinCode;

  /// No description provided for @collectdue.
  ///
  /// In en, this message translates to:
  /// **'Collect Due'**
  String get collectdue;

  /// No description provided for @color.
  ///
  /// In en, this message translates to:
  /// **'Color'**
  String get color;

  /// No description provided for @companyNameAlt.
  ///
  /// In en, this message translates to:
  /// **'Company Name'**
  String get companyNameAlt;

  /// No description provided for @companyName.
  ///
  /// In en, this message translates to:
  /// **'Company Name'**
  String get companyName;

  /// No description provided for @companyNamePlaceHolder.
  ///
  /// In en, this message translates to:
  /// **'Enter your company name'**
  String get companyNamePlaceHolder;

  /// No description provided for @completeTransaction.
  ///
  /// In en, this message translates to:
  /// **'Complete Transaction'**
  String get completeTransaction;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @continueButton.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @countryPinCode.
  ///
  /// In en, this message translates to:
  /// **'Country Pin Code'**
  String get countryPinCode;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// No description provided for @createAFreeAccounts.
  ///
  /// In en, this message translates to:
  /// **'Create a free account'**
  String get createAFreeAccounts;

  /// No description provided for @cteateAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Create an Account'**
  String get cteateAnAccount;

  /// No description provided for @currentStock.
  ///
  /// In en, this message translates to:
  /// **'Current Stock'**
  String get currentStock;

  /// No description provided for @customer.
  ///
  /// In en, this message translates to:
  /// **'Customer'**
  String get customer;

  /// No description provided for @customerDue.
  ///
  /// In en, this message translates to:
  /// **'Customer Due'**
  String get customerDue;

  /// No description provided for @customerList.
  ///
  /// In en, this message translates to:
  /// **'Customer List'**
  String get customerList;

  /// No description provided for @customerName.
  ///
  /// In en, this message translates to:
  /// **'Customer Name'**
  String get customerName;

  /// No description provided for @customerOfTheMonth.
  ///
  /// In en, this message translates to:
  /// **'Customer of the Month'**
  String get customerOfTheMonth;

  /// No description provided for @customerType.
  ///
  /// In en, this message translates to:
  /// **'Customer Type'**
  String get customerType;

  /// No description provided for @dailyTransaction.
  ///
  /// In en, this message translates to:
  /// **'Daily Transaction'**
  String get dailyTransaction;

  /// No description provided for @dashBoardOverView.
  ///
  /// In en, this message translates to:
  /// **'Dashboard Overview'**
  String get dashBoardOverView;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @dealer.
  ///
  /// In en, this message translates to:
  /// **'Dealer'**
  String get dealer;

  /// No description provided for @dealerPrice.
  ///
  /// In en, this message translates to:
  /// **'Dealer Price'**
  String get dealerPrice;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @deleteSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Delete Successful'**
  String get deleteSuccessful;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @discount.
  ///
  /// In en, this message translates to:
  /// **'Discount'**
  String get discount;

  /// No description provided for @doNotDistrub.
  ///
  /// In en, this message translates to:
  /// **'Do not disturb'**
  String get doNotDistrub;

  /// No description provided for @dueAmount.
  ///
  /// In en, this message translates to:
  /// **'Due Amount'**
  String get dueAmount;

  /// No description provided for @dueCollection.
  ///
  /// In en, this message translates to:
  /// **'Due Collection'**
  String get dueCollection;

  /// No description provided for @dueCollectionReports.
  ///
  /// In en, this message translates to:
  /// **'Due Collection Reports'**
  String get dueCollectionReports;

  /// No description provided for @dueDate.
  ///
  /// In en, this message translates to:
  /// **'Due Date'**
  String get dueDate;

  /// No description provided for @dueList.
  ///
  /// In en, this message translates to:
  /// **'Due List'**
  String get dueList;

  /// No description provided for @dueTransaction.
  ///
  /// In en, this message translates to:
  /// **'Due Transaction'**
  String get dueTransaction;

  /// No description provided for @easyToUseMobilePos.
  ///
  /// In en, this message translates to:
  /// **'Easy to use mobile POS'**
  String get easyToUseMobilePos;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @editPurchaseInvoice.
  ///
  /// In en, this message translates to:
  /// **'Edit Purchase Invoice'**
  String get editPurchaseInvoice;

  /// No description provided for @editSalesInvoice.
  ///
  /// In en, this message translates to:
  /// **'Edit Sales Invoice'**
  String get editSalesInvoice;

  /// No description provided for @editSocailMedia.
  ///
  /// In en, this message translates to:
  /// **'Edit Social Media'**
  String get editSocailMedia;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// No description provided for @endDate.
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// No description provided for @enterAmount.
  ///
  /// In en, this message translates to:
  /// **'Enter Amount'**
  String get enterAmount;

  /// No description provided for @enterCategoryName.
  ///
  /// In en, this message translates to:
  /// **'Enter Category Name'**
  String get enterCategoryName;

  /// No description provided for @enterCustomerName.
  ///
  /// In en, this message translates to:
  /// **'Enter Customer Name'**
  String get enterCustomerName;

  /// No description provided for @enterDealerPrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Dealer Price'**
  String get enterDealerPrice;

  /// No description provided for @enterDiscountAmount.
  ///
  /// In en, this message translates to:
  /// **'Enter Discount Amount'**
  String get enterDiscountAmount;

  /// No description provided for @enterEmailAddresss.
  ///
  /// In en, this message translates to:
  /// **'Enter Email Address'**
  String get enterEmailAddresss;

  /// No description provided for @enterExpenseCategory.
  ///
  /// In en, this message translates to:
  /// **'Enter Expense Category'**
  String get enterExpenseCategory;

  /// No description provided for @enterExpenseDate.
  ///
  /// In en, this message translates to:
  /// **'Enter Expense Date'**
  String get enterExpenseDate;

  /// No description provided for @enterFullAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter Full Address'**
  String get enterFullAddress;

  /// No description provided for @enterIncomeCategory.
  ///
  /// In en, this message translates to:
  /// **'Enter Income Category'**
  String get enterIncomeCategory;

  /// No description provided for @enterIncomeDate.
  ///
  /// In en, this message translates to:
  /// **'Enter Income Date'**
  String get enterIncomeDate;

  /// No description provided for @enterLandMark.
  ///
  /// In en, this message translates to:
  /// **'Enter Landmark'**
  String get enterLandMark;

  /// No description provided for @enterMessageContent.
  ///
  /// In en, this message translates to:
  /// **'Enter Message Content'**
  String get enterMessageContent;

  /// No description provided for @enterMrpOrRetailerPirce.
  ///
  /// In en, this message translates to:
  /// **'Enter MRP/Retailer Price'**
  String get enterMrpOrRetailerPirce;

  /// No description provided for @enterNote.
  ///
  /// In en, this message translates to:
  /// **'Enter Note'**
  String get enterNote;

  /// No description provided for @enterOpeningBalance.
  ///
  /// In en, this message translates to:
  /// **'Enter Opening Balance'**
  String get enterOpeningBalance;

  /// No description provided for @enterPaidAmonts.
  ///
  /// In en, this message translates to:
  /// **'Enter Paid Amount'**
  String get enterPaidAmonts;

  /// No description provided for @enterPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter Password'**
  String get enterPassword;

  /// No description provided for @enterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Phone Number'**
  String get enterPhoneNumber;

  /// No description provided for @enterPinCode.
  ///
  /// In en, this message translates to:
  /// **'Enter Pin Code'**
  String get enterPinCode;

  /// No description provided for @enterPurchasePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Purchase Price'**
  String get enterPurchasePrice;

  /// No description provided for @enterQuantity.
  ///
  /// In en, this message translates to:
  /// **'Enter Quantity'**
  String get enterQuantity;

  /// No description provided for @enterReferenceNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Reference Number'**
  String get enterReferenceNumber;

  /// No description provided for @enterSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Sale Price'**
  String get enterSalePrice;

  /// No description provided for @enterShopName.
  ///
  /// In en, this message translates to:
  /// **'Enter Shop Name'**
  String get enterShopName;

  /// No description provided for @enterStockAmount.
  ///
  /// In en, this message translates to:
  /// **'Enter Stock Amount'**
  String get enterStockAmount;

  /// No description provided for @enterUnitName.
  ///
  /// In en, this message translates to:
  /// **'Enter Unit Name'**
  String get enterUnitName;

  /// No description provided for @enterUserTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter User Title'**
  String get enterUserTitle;

  /// No description provided for @enterWholeSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Wholesale Price'**
  String get enterWholeSalePrice;

  /// No description provided for @enterYourBankAccountNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Bank Account Number'**
  String get enterYourBankAccountNumber;

  /// No description provided for @enterYourBankName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Bank Name'**
  String get enterYourBankName;

  /// No description provided for @enterYourBanktc.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Bank Terms & Conditions'**
  String get enterYourBanktc;

  /// No description provided for @enterYourCityPinCode.
  ///
  /// In en, this message translates to:
  /// **'Enter Your City Pin Code'**
  String get enterYourCityPinCode;

  /// No description provided for @enterYourCompanyName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Company Name'**
  String get enterYourCompanyName;

  /// No description provided for @enterYourConfirmPassord.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Confirm Password'**
  String get enterYourConfirmPassord;

  /// No description provided for @enterYourDealerPrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Dealer Price'**
  String get enterYourDealerPrice;

  /// No description provided for @enterYourDetails.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Details'**
  String get enterYourDetails;

  /// No description provided for @enterYourEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Email Address'**
  String get enterYourEmailAddress;

  /// No description provided for @enterYourFullName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Full Name'**
  String get enterYourFullName;

  /// No description provided for @enterYourGstNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Your GST Number'**
  String get enterYourGstNumber;

  /// No description provided for @enterYourLandmark.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Landmark'**
  String get enterYourLandmark;

  /// No description provided for @enterYourMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Mobile Number'**
  String get enterYourMobileNumber;

  /// No description provided for @enterYourName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Name'**
  String get enterYourName;

  /// No description provided for @enterYourOpeningBalance.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Opening Balance'**
  String get enterYourOpeningBalance;

  /// No description provided for @enterYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Password'**
  String get enterYourPassword;

  /// No description provided for @enterYourPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Phone Number'**
  String get enterYourPhoneNumber;

  /// No description provided for @enterYourPincode.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Pincode'**
  String get enterYourPincode;

  /// No description provided for @enterYourPurchasePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Purchase Price'**
  String get enterYourPurchasePrice;

  /// No description provided for @enterYourSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Sale Price'**
  String get enterYourSalePrice;

  /// No description provided for @enterYourShopName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Shop Name'**
  String get enterYourShopName;

  /// No description provided for @enterYourState.
  ///
  /// In en, this message translates to:
  /// **'Enter Your State'**
  String get enterYourState;

  /// No description provided for @enterYourTotalSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Total Sale Price'**
  String get enterYourTotalSalePrice;

  /// No description provided for @enterYourUserRoleName.
  ///
  /// In en, this message translates to:
  /// **'Enter Your User Role Name'**
  String get enterYourUserRoleName;

  /// No description provided for @enterYourWholeSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Wholesale Price'**
  String get enterYourWholeSalePrice;

  /// No description provided for @enterYourZip.
  ///
  /// In en, this message translates to:
  /// **'Enter Your ZIP'**
  String get enterYourZip;

  /// No description provided for @expense.
  ///
  /// In en, this message translates to:
  /// **'Expense'**
  String get expense;

  /// No description provided for @expenseCategory.
  ///
  /// In en, this message translates to:
  /// **'Expense Category'**
  String get expenseCategory;

  /// No description provided for @expenseDate.
  ///
  /// In en, this message translates to:
  /// **'Expense Date'**
  String get expenseDate;

  /// No description provided for @expenseDetails.
  ///
  /// In en, this message translates to:
  /// **'Expense Details'**
  String get expenseDetails;

  /// No description provided for @expenseFor.
  ///
  /// In en, this message translates to:
  /// **'Expense For'**
  String get expenseFor;

  /// No description provided for @expenseList.
  ///
  /// In en, this message translates to:
  /// **'Expense List'**
  String get expenseList;

  /// No description provided for @expenseReport.
  ///
  /// In en, this message translates to:
  /// **'Expense Report'**
  String get expenseReport;

  /// No description provided for @facebook.
  ///
  /// In en, this message translates to:
  /// **'Facebook'**
  String get facebook;

  /// No description provided for @failedToGetPlatformVersion.
  ///
  /// In en, this message translates to:
  /// **'Failed to get platform version.'**
  String get failedToGetPlatformVersion;

  /// No description provided for @featureAreTheImportant.
  ///
  /// In en, this message translates to:
  /// **'Features are the important part which makes AmrDev POS different from traditional solutions.'**
  String get featureAreTheImportant;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// No description provided for @freeDataBackup.
  ///
  /// In en, this message translates to:
  /// **'Free Data Backup'**
  String get freeDataBackup;

  /// No description provided for @freeLifeTimeUpdate.
  ///
  /// In en, this message translates to:
  /// **'Free Lifetime Update'**
  String get freeLifeTimeUpdate;

  /// No description provided for @freePacakge.
  ///
  /// In en, this message translates to:
  /// **'Free Package'**
  String get freePacakge;

  /// No description provided for @freePlan.
  ///
  /// In en, this message translates to:
  /// **'Free Plan'**
  String get freePlan;

  /// No description provided for @gallary.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallary;

  /// No description provided for @gstNumber.
  ///
  /// In en, this message translates to:
  /// **'GST Number'**
  String get gstNumber;

  /// No description provided for @history.
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get history;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @howWeProtectYourInformation.
  ///
  /// In en, this message translates to:
  /// **'How we protect your information'**
  String get howWeProtectYourInformation;

  /// No description provided for @identityVerify.
  ///
  /// In en, this message translates to:
  /// **'Identity Verify'**
  String get identityVerify;

  /// No description provided for @image.
  ///
  /// In en, this message translates to:
  /// **'Image'**
  String get image;

  /// No description provided for @income.
  ///
  /// In en, this message translates to:
  /// **'Income'**
  String get income;

  /// No description provided for @incomeCategory.
  ///
  /// In en, this message translates to:
  /// **'Income Category'**
  String get incomeCategory;

  /// No description provided for @incomeDate.
  ///
  /// In en, this message translates to:
  /// **'Income Date'**
  String get incomeDate;

  /// No description provided for @incomeDetails.
  ///
  /// In en, this message translates to:
  /// **'Income Details'**
  String get incomeDetails;

  /// No description provided for @incomeList.
  ///
  /// In en, this message translates to:
  /// **'Income List'**
  String get incomeList;

  /// No description provided for @incomeReport.
  ///
  /// In en, this message translates to:
  /// **'Income Report'**
  String get incomeReport;

  /// No description provided for @instagram.
  ///
  /// In en, this message translates to:
  /// **'Instagram'**
  String get instagram;

  /// No description provided for @inv.
  ///
  /// In en, this message translates to:
  /// **'Inv'**
  String get inv;

  /// No description provided for @invoice.
  ///
  /// In en, this message translates to:
  /// **'Invoice'**
  String get invoice;

  /// No description provided for @invoiceNumber.
  ///
  /// In en, this message translates to:
  /// **'Invoice Number'**
  String get invoiceNumber;

  /// No description provided for @invoiceSettings.
  ///
  /// In en, this message translates to:
  /// **'Invoice Settings'**
  String get invoiceSettings;

  /// No description provided for @itemAdded.
  ///
  /// In en, this message translates to:
  /// **'Item Added'**
  String get itemAdded;

  /// No description provided for @kycVerification.
  ///
  /// In en, this message translates to:
  /// **'KYC Verification'**
  String get kycVerification;

  /// No description provided for @landmark.
  ///
  /// In en, this message translates to:
  /// **'Landmark'**
  String get landmark;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @lastName.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// No description provided for @lifeTimePurchase.
  ///
  /// In en, this message translates to:
  /// **'Lifetime\nPurchase'**
  String get lifeTimePurchase;

  /// No description provided for @limitedUsage.
  ///
  /// In en, this message translates to:
  /// **'Limited Usage'**
  String get limitedUsage;

  /// No description provided for @link.
  ///
  /// In en, this message translates to:
  /// **'Link'**
  String get link;

  /// No description provided for @linkedIN.
  ///
  /// In en, this message translates to:
  /// **'LinkedIn'**
  String get linkedIN;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @logIn.
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get logIn;

  /// No description provided for @logOut.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logOut;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @maan.
  ///
  /// In en, this message translates to:
  /// **'Maan'**
  String get maan;

  /// No description provided for @makeALastingImpression.
  ///
  /// In en, this message translates to:
  /// **'Make a lasting impression on your customers with branded invoices. Our Unlimited Upgrade offers the unique advantage of customizing your invoices, adding a professional touch that reinforces your brand identity and fosters customer loyalty.'**
  String get makeALastingImpression;

  /// No description provided for @manageYourBussinessWith.
  ///
  /// In en, this message translates to:
  /// **'Manage your business with'**
  String get manageYourBussinessWith;

  /// No description provided for @masterCard.
  ///
  /// In en, this message translates to:
  /// **'Master Card'**
  String get masterCard;

  /// No description provided for @manufacturer.
  ///
  /// In en, this message translates to:
  /// **'Manufacturer'**
  String get manufacturer;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @messageHistory.
  ///
  /// In en, this message translates to:
  /// **'Message History'**
  String get messageHistory;

  /// No description provided for @mobiPosAppIsFree.
  ///
  /// In en, this message translates to:
  /// **'AmrDev POS app is free, easy to use. In fact, it\'s one of the best POS systems around the world.'**
  String get mobiPosAppIsFree;

  /// No description provided for @mobiPosIsaCompleteBusinesSolution.
  ///
  /// In en, this message translates to:
  /// **'AmrDev POS is a complete business solution with stock, account, sales, expense & loss/profit.'**
  String get mobiPosIsaCompleteBusinesSolution;

  /// No description provided for @monthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// No description provided for @moreInfo.
  ///
  /// In en, this message translates to:
  /// **'More Info'**
  String get moreInfo;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Enter your name'**
  String get name;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @noConnection.
  ///
  /// In en, this message translates to:
  /// **'No Connection'**
  String get noConnection;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No Data'**
  String get noData;

  /// No description provided for @noDataAvailable.
  ///
  /// In en, this message translates to:
  /// **'No Data Available'**
  String get noDataAvailable;

  /// No description provided for @noHistoryFound.
  ///
  /// In en, this message translates to:
  /// **'No History Found!'**
  String get noHistoryFound;

  /// No description provided for @noTransactionFound.
  ///
  /// In en, this message translates to:
  /// **'No Transaction Found!'**
  String get noTransactionFound;

  /// No description provided for @noUserFoundForThatEmail.
  ///
  /// In en, this message translates to:
  /// **'No user found for that email.'**
  String get noUserFoundForThatEmail;

  /// No description provided for @noUserRoleFound.
  ///
  /// In en, this message translates to:
  /// **'No User Role Found'**
  String get noUserRoleFound;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @onboardOne.
  ///
  /// In en, this message translates to:
  /// **'POS system includes a lot of features, including sales tracking, inventory management.'**
  String get onboardOne;

  /// No description provided for @onboardThree.
  ///
  /// In en, this message translates to:
  /// **'This system helps you improve your operations for your customers.'**
  String get onboardThree;

  /// No description provided for @onboardTwo.
  ///
  /// In en, this message translates to:
  /// **'Our POS system should make daily operations easy and faster.'**
  String get onboardTwo;

  /// No description provided for @openingBalance.
  ///
  /// In en, this message translates to:
  /// **'Opening Balance'**
  String get openingBalance;

  /// No description provided for @order.
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get order;

  /// No description provided for @otpClose.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get otpClose;

  /// No description provided for @paid.
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// No description provided for @paidAmount.
  ///
  /// In en, this message translates to:
  /// **'Paid Amount'**
  String get paidAmount;

  /// No description provided for @parties.
  ///
  /// In en, this message translates to:
  /// **'Parties'**
  String get parties;

  /// No description provided for @partyList.
  ///
  /// In en, this message translates to:
  /// **'Party List'**
  String get partyList;

  /// No description provided for @partyName.
  ///
  /// In en, this message translates to:
  /// **'Party Name'**
  String get partyName;

  /// No description provided for @partyType.
  ///
  /// In en, this message translates to:
  /// **'Party Type'**
  String get partyType;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @payeeName.
  ///
  /// In en, this message translates to:
  /// **'Payee Name'**
  String get payeeName;

  /// No description provided for @payeeNumber.
  ///
  /// In en, this message translates to:
  /// **'Payee Number'**
  String get payeeNumber;

  /// No description provided for @paymentInstructions.
  ///
  /// In en, this message translates to:
  /// **'Payment Instructions:'**
  String get paymentInstructions;

  /// No description provided for @paymentType.
  ///
  /// In en, this message translates to:
  /// **'Payment Type'**
  String get paymentType;

  /// No description provided for @paymentTypes.
  ///
  /// In en, this message translates to:
  /// **'Payment Types'**
  String get paymentTypes;

  /// No description provided for @paypalClientId.
  ///
  /// In en, this message translates to:
  /// **'Paypal Client ID'**
  String get paypalClientId;

  /// No description provided for @paypalClientSecret.
  ///
  /// In en, this message translates to:
  /// **'Paypal Client Secret'**
  String get paypalClientSecret;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @pinCode.
  ///
  /// In en, this message translates to:
  /// **'Pin Code'**
  String get pinCode;

  /// No description provided for @pleaseAddSomeProductFirst.
  ///
  /// In en, this message translates to:
  /// **'Please add some product first'**
  String get pleaseAddSomeProductFirst;

  /// No description provided for @pleaseConnectYourBlutohPrinter.
  ///
  /// In en, this message translates to:
  /// **'Please connect your bluetooth printer'**
  String get pleaseConnectYourBlutohPrinter;

  /// No description provided for @pleaseConnectYourPrinter.
  ///
  /// In en, this message translates to:
  /// **'Please connect your printer'**
  String get pleaseConnectYourPrinter;

  /// No description provided for @pleaseEnterAConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter a confirm password'**
  String get pleaseEnterAConfirmPassword;

  /// No description provided for @pleaseEnterAPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter a password'**
  String get pleaseEnterAPassword;

  /// No description provided for @pleaseEnterAValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterAValidEmail;

  /// No description provided for @pleaseEnterAvalidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get pleaseEnterAvalidPhoneNumber;

  /// No description provided for @pleaseEnterAnEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter an email'**
  String get pleaseEnterAnEmail;

  /// No description provided for @pleaseEnterCustomerName.
  ///
  /// In en, this message translates to:
  /// **'Please enter customer name'**
  String get pleaseEnterCustomerName;

  /// No description provided for @pleaseEnterValidData.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid data'**
  String get pleaseEnterValidData;

  /// No description provided for @pleaseEnterYourDetails.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details'**
  String get pleaseEnterYourDetails;

  /// No description provided for @pleaseEnterYourEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email address'**
  String get pleaseEnterYourEmailAddress;

  /// No description provided for @pleaseEnterYourOpeningBalance.
  ///
  /// In en, this message translates to:
  /// **'Please enter your opening balance'**
  String get pleaseEnterYourOpeningBalance;

  /// No description provided for @pleaseEnterYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterYourPassword;

  /// No description provided for @pleaseEnterYourPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get pleaseEnterYourPhoneNumber;

  /// No description provided for @pleaseEnterYourPincode.
  ///
  /// In en, this message translates to:
  /// **'Please enter your pincode'**
  String get pleaseEnterYourPincode;

  /// No description provided for @pleaseEnterYourUserRoleName.
  ///
  /// In en, this message translates to:
  /// **'Please enter your user role name'**
  String get pleaseEnterYourUserRoleName;

  /// No description provided for @pleaseMakeASaleFirst.
  ///
  /// In en, this message translates to:
  /// **'Please make a sale first'**
  String get pleaseMakeASaleFirst;

  /// No description provided for @pleaseSelectACustomer.
  ///
  /// In en, this message translates to:
  /// **'Please select a customer'**
  String get pleaseSelectACustomer;

  /// No description provided for @pleaseSelectAExpenseCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select an expense category'**
  String get pleaseSelectAExpenseCategory;

  /// No description provided for @pleaseSelectAIncomeCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select an income category'**
  String get pleaseSelectAIncomeCategory;

  /// No description provided for @pleaseSelectAInvoicePrintSize.
  ///
  /// In en, this message translates to:
  /// **'Please select an invoice print size'**
  String get pleaseSelectAInvoicePrintSize;

  /// No description provided for @pleaseSelectAPaymentType.
  ///
  /// In en, this message translates to:
  /// **'Please select a payment type'**
  String get pleaseSelectAPaymentType;

  /// No description provided for @pleaseSelectAProduct.
  ///
  /// In en, this message translates to:
  /// **'Please select a product'**
  String get pleaseSelectAProduct;

  /// No description provided for @pleaseSelectAProductCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select a product category'**
  String get pleaseSelectAProductCategory;

  /// No description provided for @pleaseSelectAProductFirst.
  ///
  /// In en, this message translates to:
  /// **'Please select a product first'**
  String get pleaseSelectAProductFirst;

  /// No description provided for @pleaseSelectAValidDate.
  ///
  /// In en, this message translates to:
  /// **'Please select a valid date'**
  String get pleaseSelectAValidDate;

  /// No description provided for @pleaseSelectAnInvoicePrintSize.
  ///
  /// In en, this message translates to:
  /// **'Please select an invoice print size'**
  String get pleaseSelectAnInvoicePrintSize;

  /// No description provided for @pleaseSelectCustomer.
  ///
  /// In en, this message translates to:
  /// **'Please select customer'**
  String get pleaseSelectCustomer;

  /// No description provided for @pleaseSelectProductCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select product category'**
  String get pleaseSelectProductCategory;

  /// No description provided for @pleaseSelectProductFirst.
  ///
  /// In en, this message translates to:
  /// **'Please select product first'**
  String get pleaseSelectProductFirst;

  /// No description provided for @pleaseSelectValidDate.
  ///
  /// In en, this message translates to:
  /// **'Please select valid date'**
  String get pleaseSelectValidDate;

  /// No description provided for @pleaseSelectYourLanguage.
  ///
  /// In en, this message translates to:
  /// **'Please select your language'**
  String get pleaseSelectYourLanguage;

  /// No description provided for @pleaseSignInWithYourEmailAndPassword.
  ///
  /// In en, this message translates to:
  /// **'Please sign in with your email and password'**
  String get pleaseSignInWithYourEmailAndPassword;

  /// No description provided for @pleaseUpdateYourPlanToAccessThisFeature.
  ///
  /// In en, this message translates to:
  /// **'Please update your plan to access this feature'**
  String get pleaseUpdateYourPlanToAccessThisFeature;

  /// No description provided for @powerdedByMobiPos.
  ///
  /// In en, this message translates to:
  /// **'Powered By AmrDev POS'**
  String get powerdedByMobiPos;

  /// No description provided for @premiumCustomerSupport.
  ///
  /// In en, this message translates to:
  /// **'Premium Customer Support'**
  String get premiumCustomerSupport;

  /// No description provided for @premiumPlan.
  ///
  /// In en, this message translates to:
  /// **'Premium Plan'**
  String get premiumPlan;

  /// No description provided for @previousDue.
  ///
  /// In en, this message translates to:
  /// **'Previous Due'**
  String get previousDue;

  /// No description provided for @print.
  ///
  /// In en, this message translates to:
  /// **'Print'**
  String get print;

  /// No description provided for @printingOption.
  ///
  /// In en, this message translates to:
  /// **'Printing Option'**
  String get printingOption;

  /// No description provided for @product.
  ///
  /// In en, this message translates to:
  /// **'Product'**
  String get product;

  /// No description provided for @productBrand.
  ///
  /// In en, this message translates to:
  /// **'Product Brand'**
  String get productBrand;

  /// No description provided for @productCategory.
  ///
  /// In en, this message translates to:
  /// **'Product Category'**
  String get productCategory;

  /// No description provided for @productCode.
  ///
  /// In en, this message translates to:
  /// **'Product Code'**
  String get productCode;

  /// No description provided for @productCodeIsRequired.
  ///
  /// In en, this message translates to:
  /// **'Product code is required'**
  String get productCodeIsRequired;

  /// No description provided for @productDetails.
  ///
  /// In en, this message translates to:
  /// **'Product Details'**
  String get productDetails;

  /// No description provided for @productList.
  ///
  /// In en, this message translates to:
  /// **'Product List'**
  String get productList;

  /// No description provided for @productName.
  ///
  /// In en, this message translates to:
  /// **'Product Name'**
  String get productName;

  /// No description provided for @productNameIsRequired.
  ///
  /// In en, this message translates to:
  /// **'Product name is required'**
  String get productNameIsRequired;

  /// No description provided for @productNature.
  ///
  /// In en, this message translates to:
  /// **'Product Nature'**
  String get productNature;

  /// No description provided for @productSize.
  ///
  /// In en, this message translates to:
  /// **'Product Size'**
  String get productSize;

  /// No description provided for @productType.
  ///
  /// In en, this message translates to:
  /// **'Product Type'**
  String get productType;

  /// No description provided for @productUnit.
  ///
  /// In en, this message translates to:
  /// **'Product Unit'**
  String get productUnit;

  /// No description provided for @productWarranty.
  ///
  /// In en, this message translates to:
  /// **'Product Warranty'**
  String get productWarranty;

  /// No description provided for @productWeight.
  ///
  /// In en, this message translates to:
  /// **'Product Weight'**
  String get productWeight;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @profileEdit.
  ///
  /// In en, this message translates to:
  /// **'Profile Edit'**
  String get profileEdit;

  /// No description provided for @profit.
  ///
  /// In en, this message translates to:
  /// **'Profit'**
  String get profit;

  /// No description provided for @profitplus.
  ///
  /// In en, this message translates to:
  /// **'Profit+'**
  String get profitplus;

  /// No description provided for @purchase.
  ///
  /// In en, this message translates to:
  /// **'Purchase'**
  String get purchase;

  /// No description provided for @purchaseAlarm.
  ///
  /// In en, this message translates to:
  /// **'Purchase Alarm'**
  String get purchaseAlarm;

  /// No description provided for @purchaseConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Purchase Confirmed'**
  String get purchaseConfirmed;

  /// No description provided for @purchaseDetails.
  ///
  /// In en, this message translates to:
  /// **'Purchase Details'**
  String get purchaseDetails;

  /// No description provided for @purchaseList.
  ///
  /// In en, this message translates to:
  /// **'Purchase List'**
  String get purchaseList;

  /// No description provided for @purchaseNow.
  ///
  /// In en, this message translates to:
  /// **'Purchase Now'**
  String get purchaseNow;

  /// No description provided for @purchasePrice.
  ///
  /// In en, this message translates to:
  /// **'Purchase Price'**
  String get purchasePrice;

  /// No description provided for @purchasePremiumPlan.
  ///
  /// In en, this message translates to:
  /// **'Purchase Premium Plan'**
  String get purchasePremiumPlan;

  /// No description provided for @purchaseReportss.
  ///
  /// In en, this message translates to:
  /// **'Purchase Reports'**
  String get purchaseReportss;

  /// No description provided for @qty.
  ///
  /// In en, this message translates to:
  /// **'Qty'**
  String get qty;

  /// No description provided for @quantity.
  ///
  /// In en, this message translates to:
  /// **'Quantity'**
  String get quantity;

  /// No description provided for @recentTransactions.
  ///
  /// In en, this message translates to:
  /// **'Recent Transactions'**
  String get recentTransactions;

  /// No description provided for @referenceNumber.
  ///
  /// In en, this message translates to:
  /// **'Reference Number'**
  String get referenceNumber;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @remainingDue.
  ///
  /// In en, this message translates to:
  /// **'Remaining Due'**
  String get remainingDue;

  /// No description provided for @reports.
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// No description provided for @retailer.
  ///
  /// In en, this message translates to:
  /// **'Retailer'**
  String get retailer;

  /// No description provided for @returnItem.
  ///
  /// In en, this message translates to:
  /// **'Return'**
  String get returnItem;

  /// No description provided for @returnAmount.
  ///
  /// In en, this message translates to:
  /// **'Return Amount'**
  String get returnAmount;

  /// No description provided for @revenue.
  ///
  /// In en, this message translates to:
  /// **'Revenue'**
  String get revenue;

  /// No description provided for @sale.
  ///
  /// In en, this message translates to:
  /// **'Sale'**
  String get sale;

  /// No description provided for @saleConfirmed.
  ///
  /// In en, this message translates to:
  /// **'Sale Confirmed'**
  String get saleConfirmed;

  /// No description provided for @saleDetails.
  ///
  /// In en, this message translates to:
  /// **'Sale Details'**
  String get saleDetails;

  /// No description provided for @saleList.
  ///
  /// In en, this message translates to:
  /// **'Sale List'**
  String get saleList;

  /// No description provided for @salePrice.
  ///
  /// In en, this message translates to:
  /// **'Sale Price'**
  String get salePrice;

  /// No description provided for @saleReportss.
  ///
  /// In en, this message translates to:
  /// **'Sale Reports'**
  String get saleReportss;

  /// No description provided for @saleTransactionquationSumary.
  ///
  /// In en, this message translates to:
  /// **'Sale Transaction Quotation Summary'**
  String get saleTransactionquationSumary;

  /// No description provided for @sales.
  ///
  /// In en, this message translates to:
  /// **'Sales'**
  String get sales;

  /// No description provided for @salesAndPurchaseReports.
  ///
  /// In en, this message translates to:
  /// **'Sale & Purchase Reports'**
  String get salesAndPurchaseReports;

  /// No description provided for @salesList.
  ///
  /// In en, this message translates to:
  /// **'Sales List'**
  String get salesList;

  /// No description provided for @salesPurchaseOverview.
  ///
  /// In en, this message translates to:
  /// **'Sales Purchase Overview'**
  String get salesPurchaseOverview;

  /// No description provided for @salesReport.
  ///
  /// In en, this message translates to:
  /// **'Sales Report'**
  String get salesReport;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @saveNPublish.
  ///
  /// In en, this message translates to:
  /// **'Save & Publish'**
  String get saveNPublish;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @searchHere.
  ///
  /// In en, this message translates to:
  /// **'Search here...'**
  String get searchHere;

  /// No description provided for @seeAllPromoCode.
  ///
  /// In en, this message translates to:
  /// **'See all promo code'**
  String get seeAllPromoCode;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @selectABrand.
  ///
  /// In en, this message translates to:
  /// **'Select a brand'**
  String get selectABrand;

  /// No description provided for @selectAInvoicePrintSize.
  ///
  /// In en, this message translates to:
  /// **'Select a invoice print size'**
  String get selectAInvoicePrintSize;

  /// No description provided for @selectAPaymentType.
  ///
  /// In en, this message translates to:
  /// **'Select a payment type'**
  String get selectAPaymentType;

  /// No description provided for @selectAProduct.
  ///
  /// In en, this message translates to:
  /// **'Select a product'**
  String get selectAProduct;

  /// No description provided for @selectAProductCategory.
  ///
  /// In en, this message translates to:
  /// **'Select a product category'**
  String get selectAProductCategory;

  /// No description provided for @selectContacts.
  ///
  /// In en, this message translates to:
  /// **'Select Contacts'**
  String get selectContacts;

  /// No description provided for @selectCustomer.
  ///
  /// In en, this message translates to:
  /// **'Select Customer'**
  String get selectCustomer;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// No description provided for @selectExpenseCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Expense Category'**
  String get selectExpenseCategory;

  /// No description provided for @selectIncomeCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Income Category'**
  String get selectIncomeCategory;

  /// No description provided for @selectProductCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Product Category'**
  String get selectProductCategory;

  /// No description provided for @selectSize.
  ///
  /// In en, this message translates to:
  /// **'Select Size'**
  String get selectSize;

  /// No description provided for @selectUnit.
  ///
  /// In en, this message translates to:
  /// **'Select Unit'**
  String get selectUnit;

  /// No description provided for @selectWarranty.
  ///
  /// In en, this message translates to:
  /// **'Select Warranty'**
  String get selectWarranty;

  /// No description provided for @selectYourLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Your Language'**
  String get selectYourLanguage;

  /// No description provided for @sendEmail.
  ///
  /// In en, this message translates to:
  /// **'Send Email'**
  String get sendEmail;

  /// No description provided for @sendMessage.
  ///
  /// In en, this message translates to:
  /// **'Send Message'**
  String get sendMessage;

  /// No description provided for @sendResetLink.
  ///
  /// In en, this message translates to:
  /// **'Send Reset Link'**
  String get sendResetLink;

  /// No description provided for @sendSms.
  ///
  /// In en, this message translates to:
  /// **'Send SMS'**
  String get sendSms;

  /// No description provided for @sendSmsQuestion.
  ///
  /// In en, this message translates to:
  /// **'Send SMS?'**
  String get sendSmsQuestion;

  /// No description provided for @sendYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Send Your Email'**
  String get sendYourEmail;

  /// No description provided for @setUpYourProfile.
  ///
  /// In en, this message translates to:
  /// **'Set up your profile'**
  String get setUpYourProfile;

  /// No description provided for @setting.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get setting;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @size.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @sms.
  ///
  /// In en, this message translates to:
  /// **'SMS'**
  String get sms;

  /// No description provided for @startDate.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// No description provided for @startNewSale.
  ///
  /// In en, this message translates to:
  /// **'Start New Sale'**
  String get startNewSale;

  /// No description provided for @state.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get state;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @stock.
  ///
  /// In en, this message translates to:
  /// **'Stock'**
  String get stock;

  /// No description provided for @stockList.
  ///
  /// In en, this message translates to:
  /// **'Stock List'**
  String get stockList;

  /// No description provided for @stockvalue.
  ///
  /// In en, this message translates to:
  /// **'Stock Value'**
  String get stockvalue;

  /// No description provided for @subTotal.
  ///
  /// In en, this message translates to:
  /// **'Sub Total'**
  String get subTotal;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @subscription.
  ///
  /// In en, this message translates to:
  /// **'Subscription'**
  String get subscription;

  /// No description provided for @supplier.
  ///
  /// In en, this message translates to:
  /// **'Supplier'**
  String get supplier;

  /// No description provided for @supplierDue.
  ///
  /// In en, this message translates to:
  /// **'Supplier Due'**
  String get supplierDue;

  /// No description provided for @supplierName.
  ///
  /// In en, this message translates to:
  /// **'Supplier Name'**
  String get supplierName;

  /// No description provided for @takeADriveruser.
  ///
  /// In en, this message translates to:
  /// **'Take a driver\'s license, national identity card or passport photo'**
  String get takeADriveruser;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get termsAndConditions;

  /// No description provided for @thisCustomerHasnoDue.
  ///
  /// In en, this message translates to:
  /// **'This customer has no due'**
  String get thisCustomerHasnoDue;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @totalAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get totalAmount;

  /// No description provided for @totalCollected.
  ///
  /// In en, this message translates to:
  /// **'Total Collected'**
  String get totalCollected;

  /// No description provided for @totalDue.
  ///
  /// In en, this message translates to:
  /// **'Total Due'**
  String get totalDue;

  /// No description provided for @totalExpense.
  ///
  /// In en, this message translates to:
  /// **'Total Expense'**
  String get totalExpense;

  /// No description provided for @totalIncome.
  ///
  /// In en, this message translates to:
  /// **'Total Income'**
  String get totalIncome;

  /// No description provided for @totalItems.
  ///
  /// In en, this message translates to:
  /// **'Total Items'**
  String get totalItems;

  /// No description provided for @totalPayable.
  ///
  /// In en, this message translates to:
  /// **'Total Payable'**
  String get totalPayable;

  /// No description provided for @totalPaymentIn.
  ///
  /// In en, this message translates to:
  /// **'Total Payment In'**
  String get totalPaymentIn;

  /// No description provided for @totalPaymentOut.
  ///
  /// In en, this message translates to:
  /// **'Total Payment Out'**
  String get totalPaymentOut;

  /// No description provided for @totalPrice.
  ///
  /// In en, this message translates to:
  /// **'Total Price'**
  String get totalPrice;

  /// No description provided for @totalProduct.
  ///
  /// In en, this message translates to:
  /// **'Total Product'**
  String get totalProduct;

  /// No description provided for @totalPurchase.
  ///
  /// In en, this message translates to:
  /// **'Total Purchase'**
  String get totalPurchase;

  /// No description provided for @totalSales.
  ///
  /// In en, this message translates to:
  /// **'Total Sales'**
  String get totalSales;

  /// No description provided for @totalStock.
  ///
  /// In en, this message translates to:
  /// **'Total Stocks'**
  String get totalStock;

  /// No description provided for @totalValue.
  ///
  /// In en, this message translates to:
  /// **'Total Value'**
  String get totalValue;

  /// No description provided for @transaction.
  ///
  /// In en, this message translates to:
  /// **'Transaction'**
  String get transaction;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @twitter.
  ///
  /// In en, this message translates to:
  /// **'Twitter'**
  String get twitter;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @unpaid.
  ///
  /// In en, this message translates to:
  /// **'Unpaid'**
  String get unpaid;

  /// No description provided for @unitName.
  ///
  /// In en, this message translates to:
  /// **'Unit Name'**
  String get unitName;

  /// No description provided for @unitPrice.
  ///
  /// In en, this message translates to:
  /// **'Unit Price'**
  String get unitPrice;

  /// No description provided for @units.
  ///
  /// In en, this message translates to:
  /// **'Units'**
  String get units;

  /// No description provided for @unlimitedInvoices.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Invoices'**
  String get unlimitedInvoices;

  /// No description provided for @unlimitedUsage.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Usage'**
  String get unlimitedUsage;

  /// No description provided for @unlockTheFull.
  ///
  /// In en, this message translates to:
  /// **'Unlock the full potential of AmrDev POS with personalized training sessions led by our expert team. From the basics to advanced techniques, we ensure you\'re well-versed in utilizing every facet of the system to optimize your business processes.'**
  String get unlockTheFull;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @updateContact.
  ///
  /// In en, this message translates to:
  /// **'Update Contact'**
  String get updateContact;

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateNow;

  /// No description provided for @updateProduct.
  ///
  /// In en, this message translates to:
  /// **'Update Product'**
  String get updateProduct;

  /// No description provided for @updateProfile.
  ///
  /// In en, this message translates to:
  /// **'Update Profile'**
  String get updateProfile;

  /// No description provided for @updateYourProfile.
  ///
  /// In en, this message translates to:
  /// **'Update your profile'**
  String get updateYourProfile;

  /// No description provided for @updateYourSubscription.
  ///
  /// In en, this message translates to:
  /// **'Update your subscription'**
  String get updateYourSubscription;

  /// No description provided for @uploadAnImage.
  ///
  /// In en, this message translates to:
  /// **'Upload an image'**
  String get uploadAnImage;

  /// No description provided for @uploadImage.
  ///
  /// In en, this message translates to:
  /// **'Upload Image'**
  String get uploadImage;

  /// No description provided for @useOfTheSystem.
  ///
  /// In en, this message translates to:
  /// **'Use of the system'**
  String get useOfTheSystem;

  /// No description provided for @useMobiPos.
  ///
  /// In en, this message translates to:
  /// **'Use AmrDev POS'**
  String get useMobiPos;

  /// No description provided for @userRole.
  ///
  /// In en, this message translates to:
  /// **'User Role'**
  String get userRole;

  /// No description provided for @userRoleDetails.
  ///
  /// In en, this message translates to:
  /// **'User Role Details'**
  String get userRoleDetails;

  /// No description provided for @vatGst.
  ///
  /// In en, this message translates to:
  /// **'VAT/GST'**
  String get vatGst;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @viewDetails.
  ///
  /// In en, this message translates to:
  /// **'View details'**
  String get viewDetails;

  /// No description provided for @viewReport.
  ///
  /// In en, this message translates to:
  /// **'View Report'**
  String get viewReport;

  /// No description provided for @warranty.
  ///
  /// In en, this message translates to:
  /// **'Warranty'**
  String get warranty;

  /// No description provided for @weight.
  ///
  /// In en, this message translates to:
  /// **'Weight'**
  String get weight;

  /// No description provided for @whatsNew.
  ///
  /// In en, this message translates to:
  /// **'What\'s New'**
  String get whatsNew;

  /// No description provided for @wholesaler.
  ///
  /// In en, this message translates to:
  /// **'Wholesaler'**
  String get wholesaler;

  /// No description provided for @wholeSalePrice.
  ///
  /// In en, this message translates to:
  /// **'Wholesale Price'**
  String get wholeSalePrice;

  /// No description provided for @writeYourMessageHere.
  ///
  /// In en, this message translates to:
  /// **'Write your message here'**
  String get writeYourMessageHere;

  /// No description provided for @wrongPasswordProvidedforThatUser.
  ///
  /// In en, this message translates to:
  /// **'Wrong password provided for that user.'**
  String get wrongPasswordProvidedforThatUser;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @yesDeleteForever.
  ///
  /// In en, this message translates to:
  /// **'Yes, Delete Forever'**
  String get yesDeleteForever;

  /// No description provided for @youAreUsing.
  ///
  /// In en, this message translates to:
  /// **'You are using'**
  String get youAreUsing;

  /// No description provided for @youHaveGotAnEmail.
  ///
  /// In en, this message translates to:
  /// **'You Have Got An Email'**
  String get youHaveGotAnEmail;

  /// No description provided for @youHaveSuccefulyLogin.
  ///
  /// In en, this message translates to:
  /// **'You have successfully logged in to your account. Stay with AmrDev POS.'**
  String get youHaveSuccefulyLogin;

  /// No description provided for @youHaveToReLogin.
  ///
  /// In en, this message translates to:
  /// **'You have to re-login to your account.'**
  String get youHaveToReLogin;

  /// No description provided for @youNeedToIdentityVerifyBeforeYouBuying.
  ///
  /// In en, this message translates to:
  /// **'You need to identity verify before your buying sms'**
  String get youNeedToIdentityVerifyBeforeYouBuying;

  /// No description provided for @yourMessageRemains.
  ///
  /// In en, this message translates to:
  /// **'Your message remains'**
  String get yourMessageRemains;

  /// No description provided for @yourPackage.
  ///
  /// In en, this message translates to:
  /// **'Your Package'**
  String get yourPackage;

  /// No description provided for @yourPackageWillExpireinDay.
  ///
  /// In en, this message translates to:
  /// **'Your Package Will Expire in 5 Day'**
  String get yourPackageWillExpireinDay;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return SEn();
  }

  throw FlutterError(
      'S.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
