/// ثوابت الصلاحيات الإدارية
class AdminPermissionsConstants {
  // ========== الصلاحيات الأساسية ==========

  /// صلاحيات المبيعات
  static const String salePermission = 'sale_permission';
  static const String salesListPermission = 'sales_list_permission';

  /// صلاحيات الأطراف
  static const String partiesPermission = 'parties_permission';

  /// صلاحيات المشتريات
  static const String purchasePermission = 'purchase_permission';
  static const String purchaseListPermission = 'purchase_list_permission';

  /// صلاحيات المنتجات
  static const String productPermission = 'product_permission';

  /// صلاحيات الملف الشخصي
  static const String profileEditPermission = 'profile_edit_permission';

  /// صلاحيات المصروفات
  static const String addExpensePermission = 'add_expense_permission';

  /// صلاحيات الأرباح والخسائر
  static const String lossProfitPermission = 'loss_profit_permission';

  /// صلاحيات المستحقات
  static const String dueListPermission = 'due_list_permission';

  /// صلاحيات المخزون
  static const String stockPermission = 'stock_permission';

  /// صلاحيات التقارير
  static const String reportsPermission = 'reports_permission';

  // ========== الصلاحيات المتقدمة - إدارة الاشتراكات ==========

  /// إدارة الاشتراكات العامة
  static const String subscriptionManagement =
      'subscription_management_permission';

  /// إنشاء خطط مخصصة
  static const String createCustomPlans = 'create_custom_plans_permission';

  /// تعديل خطط الاشتراك
  static const String modifySubscriptionPlans =
      'modify_subscription_plans_permission';

  /// تجميد الاشتراكات
  static const String freezeSubscriptions = 'freeze_subscriptions_permission';

  /// إلغاء الاشتراكات
  static const String cancelSubscriptions = 'cancel_subscriptions_permission';

  /// منح فترات تجريبية مجانية
  static const String grantFreeTrials = 'grant_free_trials_permission';

  /// إدارة الخصومات والعروض
  static const String manageDiscounts = 'manage_discounts_permission';

  /// عرض تحليلات الاشتراكات
  static const String viewSubscriptionAnalytics =
      'view_subscription_analytics_permission';

  /// العمليات المجمعة للاشتراكات
  static const String bulkSubscriptionOperations =
      'bulk_subscription_operations_permission';

  // ========== الصلاحيات المتقدمة - إدارة النظام ==========

  /// إعدادات النظام العامة
  static const String systemSettings = 'system_settings_permission';

  /// إدارة قاعدة البيانات
  static const String databaseManagement = 'database_management_permission';

  /// النسخ الاحتياطي والاستعادة
  static const String backupRestore = 'backup_restore_permission';

  /// إدارة الخوادم
  static const String serverManagement = 'server_management_permission';

  /// مراقبة النظام
  static const String systemMonitoring = 'system_monitoring_permission';

  /// تحليلات الأداء
  static const String performanceAnalytics = 'performance_analytics_permission';

  /// تنبيهات النظام
  static const String systemAlerts = 'system_alerts_permission';

  /// وضع الصيانة
  static const String maintenanceMode = 'maintenance_mode_permission';

  // ========== الصلاحيات المتقدمة - الأمان والحماية ==========

  /// إدارة الأمان العامة
  static const String securityManagement = 'security_management_permission';

  /// سياسات كلمات المرور
  static const String passwordPolicy = 'password_policy_permission';

  /// سجلات التدقيق
  static const String auditLogs = 'audit_logs_permission';

  /// إدارة الجلسات
  static const String sessionManagement = 'session_management_permission';

  /// التحكم في IP المسموح
  static const String ipControl = 'ip_control_permission';

  /// المصادقة الثنائية
  static const String twoFactorAuth = 'two_factor_auth_permission';

  /// إدارة التشفير
  static const String encryptionManagement = 'encryption_management_permission';

  /// تنبيهات الأمان
  static const String securityAlerts = 'security_alerts_permission';

  // ========== الصلاحيات المتقدمة - التحكم المالي ==========

  /// التحكم المالي المتقدم
  static const String advancedFinancialControl =
      'advanced_financial_control_permission';

  /// إدارة بوابات الدفع
  static const String paymentGatewayManagement =
      'payment_gateway_management_permission';

  /// إدارة الفواتير
  static const String invoiceManagement = 'invoice_management_permission';

  /// حدود الإنفاق
  static const String spendingLimits = 'spending_limits_permission';

  /// إدارة العمولات
  static const String commissionManagement = 'commission_management_permission';

  /// إدارة العملات
  static const String currencyManagement = 'currency_management_permission';

  /// إدارة الضرائب
  static const String taxManagement = 'tax_management_permission';

  /// التقارير المالية المتقدمة
  static const String financialReports = 'financial_reports_permission';

  // ========== الصلاحيات المتقدمة - إدارة المحتوى ==========

  /// إدارة المحتوى
  static const String contentManagement = 'content_management_permission';

  /// إدارة الإعلانات
  static const String advertisingManagement =
      'advertising_management_permission';

  /// التسويق عبر البريد الإلكتروني
  static const String emailMarketing = 'email_marketing_permission';

  /// التسويق عبر الرسائل النصية
  static const String smsMarketing = 'sms_marketing_permission';

  /// إدارة الإشعارات
  static const String notificationManagement =
      'notification_management_permission';

  /// إدارة وسائل التواصل الاجتماعي
  static const String socialMediaManagement =
      'social_media_management_permission';

  // ========== الصلاحيات المتقدمة - إدارة المتاجر ==========

  /// إدارة المتاجر المتقدمة
  static const String advancedShopManagement =
      'advanced_shop_management_permission';

  /// التحقق من المتاجر
  static const String shopVerification = 'shop_verification_permission';

  /// تعليق المتاجر
  static const String shopSuspension = 'shop_suspension_permission';

  /// تحليلات المتاجر
  static const String shopAnalytics = 'shop_analytics_permission';

  /// إعدادات المتاجر
  static const String shopSettings = 'shop_settings_permission';

  /// إدارة فئات المتاجر
  static const String shopCategoriesManagement =
      'shop_categories_management_permission';

  // ========== الصلاحيات المتقدمة - إدارة المستخدمين ==========

  /// إدارة المستخدمين المتقدمة
  static const String advancedUserManagement =
      'advanced_user_management_permission';

  /// التحقق من المستخدمين
  static const String userVerification = 'user_verification_permission';

  /// تعليق المستخدمين
  static const String userSuspension = 'user_suspension_permission';

  /// تحليلات المستخدمين
  static const String userAnalytics = 'user_analytics_permission';

  /// العمليات المجمعة للمستخدمين
  static const String massUserOperations = 'mass_user_operations_permission';

  /// إدارة أدوار المستخدمين
  static const String userRoleManagement = 'user_role_management_permission';

  // ========== الصلاحيات المتقدمة - التقارير والتحليلات ==========

  /// التقارير المتقدمة
  static const String advancedReports = 'advanced_reports_permission';

  /// ذكاء الأعمال
  static const String businessIntelligence = 'business_intelligence_permission';

  /// تصدير البيانات
  static const String dataExport = 'data_export_permission';

  /// التقارير المخصصة
  static const String customReports = 'custom_reports_permission';

  /// التحليلات الفورية
  static const String realTimeAnalytics = 'real_time_analytics_permission';

  /// التحليلات التنبؤية
  static const String predictiveAnalytics = 'predictive_analytics_permission';

  // ========== الصلاحيات المتقدمة - API والتكاملات ==========

  /// إدارة API
  static const String apiManagement = 'api_management_permission';

  /// التكاملات مع الطرف الثالث
  static const String thirdPartyIntegrations =
      'third_party_integrations_permission';

  /// إدارة Webhooks
  static const String webhookManagement = 'webhook_management_permission';

  /// إدارة مفاتيح API
  static const String apiKeysManagement = 'api_keys_management_permission';

  // ========== الصلاحيات المتقدمة - الجودة والدعم ==========

  /// مراقبة الجودة
  static const String qualityControl = 'quality_control_permission';

  /// إدارة دعم العملاء
  static const String customerSupportManagement =
      'customer_support_management_permission';

  /// إدارة التذاكر
  static const String ticketManagement = 'ticket_management_permission';

  /// إدارة التعليقات
  static const String feedbackManagement = 'feedback_management_permission';

  // ========== صلاحيات الطوارئ ==========

  /// الوصول في حالات الطوارئ
  static const String emergencyAccess = 'emergency_access_permission';

  /// إيقاف النظام
  static const String systemShutdown = 'system_shutdown_permission';

  /// استعادة البيانات
  static const String dataRecovery = 'data_recovery_permission';

  /// إشعارات الطوارئ
  static const String emergencyNotifications =
      'emergency_notifications_permission';

  // ========== صلاحيات المطور ==========

  /// وضع المطور
  static const String developerMode = 'developer_mode_permission';

  /// وضع التصحيح
  static const String debugMode = 'debug_mode_permission';

  /// إدارة السجلات
  static const String logManagement = 'log_management_permission';

  /// نشر الكود
  static const String codeDeployment = 'code_deployment_permission';

  // ========== مجموعات الصلاحيات ==========

  /// جميع الصلاحيات الأساسية
  static const List<String> basicPermissions = [
    salePermission,
    partiesPermission,
    purchasePermission,
    productPermission,
    profileEditPermission,
    addExpensePermission,
    lossProfitPermission,
    dueListPermission,
    stockPermission,
    reportsPermission,
    salesListPermission,
    purchaseListPermission,
  ];

  /// صلاحيات إدارة الاشتراكات
  static const List<String> subscriptionPermissions = [
    subscriptionManagement,
    createCustomPlans,
    modifySubscriptionPlans,
    freezeSubscriptions,
    cancelSubscriptions,
    grantFreeTrials,
    manageDiscounts,
    viewSubscriptionAnalytics,
    bulkSubscriptionOperations,
  ];

  /// صلاحيات إدارة النظام
  static const List<String> systemPermissions = [
    systemSettings,
    databaseManagement,
    backupRestore,
    serverManagement,
    systemMonitoring,
    performanceAnalytics,
    systemAlerts,
    maintenanceMode,
  ];

  /// صلاحيات الأمان
  static const List<String> securityPermissions = [
    securityManagement,
    passwordPolicy,
    auditLogs,
    sessionManagement,
    ipControl,
    twoFactorAuth,
    encryptionManagement,
    securityAlerts,
  ];

  /// صلاحيات التحكم المالي
  static const List<String> financialPermissions = [
    advancedFinancialControl,
    paymentGatewayManagement,
    invoiceManagement,
    spendingLimits,
    commissionManagement,
    currencyManagement,
    taxManagement,
    financialReports,
  ];

  /// صلاحيات الطوارئ
  static const List<String> emergencyPermissions = [
    emergencyAccess,
    systemShutdown,
    dataRecovery,
    emergencyNotifications,
  ];

  /// صلاحيات المطور
  static const List<String> developerPermissions = [
    developerMode,
    debugMode,
    logManagement,
    codeDeployment,
  ];

  /// جميع الصلاحيات المتقدمة
  static const List<String> allAdvancedPermissions = [
    ...subscriptionPermissions,
    ...systemPermissions,
    ...securityPermissions,
    ...financialPermissions,
    ...emergencyPermissions,
    ...developerPermissions,
  ];

  /// جميع الصلاحيات
  static const List<String> allPermissions = [
    ...basicPermissions,
    ...allAdvancedPermissions,
  ];

  // ========== أوصاف الصلاحيات ==========

  /// أوصاف الصلاحيات باللغة العربية
  static const Map<String, String> permissionDescriptions = {
    // الصلاحيات الأساسية
    salePermission: 'إدارة عمليات البيع والفواتير',
    partiesPermission: 'إدارة العملاء والموردين',
    purchasePermission: 'إدارة عمليات الشراء',
    productPermission: 'إدارة المنتجات والمخزون',
    profileEditPermission: 'تعديل بيانات المستخدم',
    addExpensePermission: 'إضافة وإدارة المصروفات',
    lossProfitPermission: 'عرض تقارير الأرباح والخسائر',
    dueListPermission: 'إدارة قوائم المستحقات',
    stockPermission: 'إدارة المخزون والكميات',
    reportsPermission: 'عرض التقارير العامة',
    salesListPermission: 'عرض قوائم المبيعات',
    purchaseListPermission: 'عرض قوائم المشتريات',

    // صلاحيات إدارة الاشتراكات
    subscriptionManagement: 'إدارة الاشتراكات العامة',
    createCustomPlans: 'إنشاء خطط اشتراك مخصصة',
    modifySubscriptionPlans: 'تعديل خطط الاشتراك الموجودة',
    freezeSubscriptions: 'تجميد الاشتراكات مؤقتاً',
    cancelSubscriptions: 'إلغاء الاشتراكات نهائياً',
    grantFreeTrials: 'منح فترات تجريبية مجانية',
    manageDiscounts: 'إدارة الخصومات والعروض الترويجية',
    viewSubscriptionAnalytics: 'عرض تحليلات الاشتراكات المفصلة',
    bulkSubscriptionOperations: 'تنفيذ عمليات مجمعة على الاشتراكات',

    // صلاحيات إدارة النظام
    systemSettings: 'التحكم في إعدادات النظام الأساسية',
    databaseManagement: 'إدارة وصيانة قاعدة البيانات',
    backupRestore: 'إنشاء واستعادة النسخ الاحتياطية',
    serverManagement: 'مراقبة وإدارة الخوادم',
    systemMonitoring: 'مراقبة أداء النظام في الوقت الفعلي',
    performanceAnalytics: 'تحليل أداء النظام والتطبيق',
    systemAlerts: 'إدارة تنبيهات النظام',
    maintenanceMode: 'تفعيل وإلغاء وضع الصيانة',

    // صلاحيات الأمان
    securityManagement: 'التحكم في جميع جوانب الأمان',
    passwordPolicy: 'تحديد قواعد وسياسات كلمات المرور',
    auditLogs: 'عرض وإدارة سجلات التدقيق',
    sessionManagement: 'مراقبة وإدارة جلسات المستخدمين',
    ipControl: 'تحديد عناوين IP المسموح بها',
    twoFactorAuth: 'إدارة المصادقة الثنائية',
    encryptionManagement: 'إدارة إعدادات التشفير',
    securityAlerts: 'إدارة تنبيهات الأمان',
  };
}
