import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../Screen/Widgets/Constant Data/constant.dart';
import '../../model/advanced_notification_model.dart';
import '../../Provider/admin_notifications_provider.dart';
import 'create_advanced_message_screen.dart';

/// مركز الإرسال المتقدم والشامل
class AdvancedMessagingCenter extends ConsumerStatefulWidget {
  static const String route = '/advanced-messaging-center';
  const AdvancedMessagingCenter({super.key});

  @override
  ConsumerState<AdvancedMessagingCenter> createState() =>
      _AdvancedMessagingCenterState();
}

class _AdvancedMessagingCenterState
    extends ConsumerState<AdvancedMessagingCenter>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // تحميل البيانات عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(adminNotificationsProvider.notifier).loadAllNotifications();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'مركز الإرسال المتقدم',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle, color: Colors.white, size: 28),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreateAdvancedMessageScreen(),
                ),
              );
            },
            tooltip: 'إنشاء رسالة جديدة',
          ),
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () {
              //  فتح شاشة التحليلات المتقدمة
            },
            tooltip: 'التحليلات المتقدمة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'إرسال سريع'),
            Tab(text: 'الرسائل المرسلة'),
            Tab(text: 'المجدولة'),
            Tab(text: 'الإحصائيات'),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildQuickSendTab(),
            _buildSentMessagesTab(),
            _buildScheduledTab(),
            _buildStatsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSendTab() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth > 1200;
        final isMediumScreen = constraints.maxWidth > 800;

        return SingleChildScrollView(
          padding: EdgeInsets.all(isWideScreen
              ? 32
              : isMediumScreen
                  ? 24
                  : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      kMainColor.withValues(alpha: 0.1),
                      kMainColor.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: kMainColor.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: kMainColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.rocket_launch,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'مركز الإرسال السريع والمتقدم',
                            style: GoogleFonts.cairo(
                              fontSize: isWideScreen
                                  ? 28
                                  : isMediumScreen
                                      ? 24
                                      : 20,
                              fontWeight: FontWeight.bold,
                              color: kMainColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'اختر نوع الاستهداف والمحتوى والجدولة لإرسال رسائل احترافية ومتقدمة',
                            style: GoogleFonts.cairo(
                              fontSize: isWideScreen ? 16 : 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Main Content Grid
              if (isWideScreen)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildTargetingSection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildContentTypeSection()),
                    const SizedBox(width: 24),
                    Expanded(flex: 2, child: _buildSchedulingSection()),
                  ],
                )
              else if (isMediumScreen)
                Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(child: _buildTargetingSection()),
                        const SizedBox(width: 20),
                        Expanded(child: _buildContentTypeSection()),
                      ],
                    ),
                    const SizedBox(height: 24),
                    _buildSchedulingSection(),
                  ],
                )
              else
                Column(
                  children: [
                    _buildTargetingSection(),
                    const SizedBox(height: 20),
                    _buildContentTypeSection(),
                    const SizedBox(height: 20),
                    _buildSchedulingSection(),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTargetingSection() {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withValues(alpha: 0.08),
            Colors.blue.withValues(alpha: 0.03),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.gps_fixed, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'اختيار الهدف',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Target Options Grid
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildTargetCard(
                  'جميع المستخدمين',
                  'إرسال شامل',
                  Icons.people,
                  Colors.blue,
                  () => _createMessage(NotificationTarget.all),
                ),
                _buildTargetCard(
                  'مستخدمين محددين',
                  'اختيار دقيق',
                  Icons.person_pin,
                  Colors.green,
                  () => _createMessage(NotificationTarget.specificUsers),
                ),
                _buildTargetCard(
                  'أجهزة محددة',
                  'استهداف الأجهزة',
                  Icons.devices,
                  Colors.orange,
                  () => _createMessage(NotificationTarget.specificDevices),
                ),
                _buildTargetCard(
                  'مناطق جغرافية',
                  'حسب المنطقة',
                  Icons.location_on,
                  Colors.purple,
                  () => _createMessage(NotificationTarget.regions),
                ),
                _buildTargetCard(
                  'أنواع اشتراك',
                  'حسب الاشتراك',
                  Icons.card_membership,
                  Colors.teal,
                  () => _createMessage(NotificationTarget.subscriptionTypes),
                ),
                _buildTargetCard(
                  'مجموعات مخصصة',
                  'مجموعات محفوظة',
                  Icons.group,
                  Colors.indigo,
                  () => _createMessage(NotificationTarget.customGroups),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentTypeSection() {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withValues(alpha: 0.08),
            Colors.green.withValues(alpha: 0.03),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.message, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'نوع المحتوى',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Content Types Grid
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildContentCard(
                  'نص فقط',
                  'رسالة بسيطة',
                  Icons.text_fields,
                  Colors.blue,
                ),
                _buildContentCard(
                  'نص + صورة',
                  'رسالة مرئية',
                  Icons.image,
                  Colors.orange,
                ),
                _buildContentCard(
                  'نص + رابط',
                  'رسالة تفاعلية',
                  Icons.link,
                  Colors.purple,
                ),
                _buildContentCard(
                  'محتوى كامل',
                  'رسالة شاملة',
                  Icons.dashboard,
                  Colors.teal,
                ),
                _buildContentCard(
                  'ملف صوتي',
                  'رسالة صوتية',
                  Icons.audiotrack,
                  Colors.indigo,
                ),
                _buildContentCard(
                  'مقطع فيديو',
                  'رسالة مرئية',
                  Icons.video_library,
                  Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulingSection() {
    return Container(
      height: 400,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.withValues(alpha: 0.08),
            Colors.orange.withValues(alpha: 0.03),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.schedule, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'خيارات الجدولة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Scheduling Options
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: _buildScheduleCard(
                    'إرسال فوري',
                    'إرسال الرسالة الآن مباشرة',
                    Icons.send,
                    Colors.red,
                    () => _scheduleMessage(SchedulingOptions.immediate),
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: _buildScheduleCard(
                    'إرسال مجدول',
                    'تحديد تاريخ ووقت محدد للإرسال',
                    Icons.calendar_today,
                    Colors.blue,
                    () => _scheduleMessage(SchedulingOptions.scheduled),
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: _buildScheduleCard(
                    'إرسال متكرر',
                    'إرسال يومي أو أسبوعي أو شهري',
                    Icons.repeat,
                    Colors.green,
                    () => _scheduleMessage(SchedulingOptions.recurring),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSentMessagesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final notificationsProvider = ref.watch(adminNotificationsProvider);
        final notifications = notificationsProvider.allNotifications;

        if (notificationsProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: kMainColor),
          );
        }

        if (notificationsProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل الرسائل',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  notificationsProvider.error!,
                  style: GoogleFonts.cairo(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref
                      .read(adminNotificationsProvider.notifier)
                      .loadAllNotifications(),
                  style: ElevatedButton.styleFrom(backgroundColor: kMainColor),
                  child: Text(
                    'إعادة المحاولة',
                    style: GoogleFonts.cairo(color: Colors.white),
                  ),
                ),
              ],
            ),
          );
        }

        return _buildSentMessagesList(notifications, ref);
      },
    );
  }

  Widget _buildSentMessagesList(
      List<AdminNotificationModel> notifications, WidgetRef ref) {
    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد رسائل مرسلة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإرسال رسالتك الأولى',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                _tabController.animateTo(0); // الانتقال لتبويب الإرسال السريع
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: kMainColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              icon: const Icon(Icons.add, color: Colors.white),
              label: Text(
                'إرسال رسالة جديدة',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // شريط البحث والفلترة المحسن
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.grey[50]!,
                Colors.white,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.search, color: kMainColor, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'البحث والفلترة',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: kMainColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: 'ابحث في العناوين والمحتوى...',
                          hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
                          prefixIcon:
                              const Icon(Icons.search, color: kMainColor),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 16),
                        ),
                        onChanged: (value) {
                          // البحث في الرسائل - سيتم تنفيذه لاحقاً
                          debugPrint('البحث عن: $value');
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: PopupMenuButton<String>(
                      icon: const Icon(Icons.filter_list, color: kMainColor),
                      tooltip: 'فلترة الرسائل',
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      onSelected: (value) {
                        // فلترة الرسائل - سيتم تنفيذه لاحقاً
                        debugPrint('فلترة حسب: $value');
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'all',
                          child: Row(
                            children: [
                              const Icon(Icons.list,
                                  color: Colors.blue, size: 20),
                              const SizedBox(width: 8),
                              Text('جميع الرسائل', style: GoogleFonts.cairo()),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'active',
                          child: Row(
                            children: [
                              const Icon(Icons.check_circle,
                                  color: Colors.green, size: 20),
                              const SizedBox(width: 8),
                              Text('الرسائل النشطة',
                                  style: GoogleFonts.cairo()),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'inactive',
                          child: Row(
                            children: [
                              const Icon(Icons.pause_circle,
                                  color: Colors.orange, size: 20),
                              const SizedBox(width: 8),
                              Text('الرسائل المعطلة',
                                  style: GoogleFonts.cairo()),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'emergency',
                          child: Row(
                            children: [
                              const Icon(Icons.warning,
                                  color: Colors.red, size: 20),
                              const SizedBox(width: 8),
                              Text('رسائل الطوارئ', style: GoogleFonts.cairo()),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // قائمة الرسائل المحسنة
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isWideScreen = constraints.maxWidth > 1200;
              final isMediumScreen = constraints.maxWidth > 800;

              if (isWideScreen) {
                // Grid layout for wide screens
                return GridView.builder(
                  padding: const EdgeInsets.all(24),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 20,
                    mainAxisSpacing: 20,
                  ),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return _buildMessageCard(notification, ref);
                  },
                );
              } else {
                // List layout for smaller screens
                return ListView.builder(
                  padding: EdgeInsets.all(isMediumScreen ? 20 : 16),
                  itemCount: notifications.length,
                  itemBuilder: (context, index) {
                    final notification = notifications[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildMessageCard(notification, ref),
                    );
                  },
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMessageCard(AdminNotificationModel notification, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: notification.isActive
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.red.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // رأس البطاقة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: notification.isActive
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  // أيقونة النوع
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getTypeColor(notification.type),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      notification.iconCode,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // معلومات الرسالة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notification.title,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: kMainColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          AdvancedNotificationTypes.getArabicName(
                              notification.type),
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: _getTypeColor(notification.type),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // حالة الرسالة
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: notification.isActive ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      notification.isActive ? 'نشطة' : 'معطلة',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // محتوى الرسالة
                  Text(
                    notification.message,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),

                  // معلومات إضافية
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 14, color: Colors.grey[500]),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(notification.createdAt),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                      const Spacer(),
                      if (notification.actionUrl != null)
                        Icon(Icons.link, size: 14, color: Colors.blue[500]),
                      if (notification.imageUrl != null) ...[
                        const SizedBox(width: 8),
                        Icon(Icons.image, size: 14, color: Colors.orange[500]),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            // أزرار التحكم
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  // زر التفعيل/التعطيل
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () =>
                          _toggleNotificationStatus(notification, ref),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: notification.isActive
                            ? Colors.orange
                            : Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      icon: Icon(
                        notification.isActive ? Icons.pause : Icons.play_arrow,
                        size: 16,
                      ),
                      label: Text(
                        notification.isActive ? 'تعطيل' : 'تفعيل',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),

                  // زر التعديل
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editNotification(notification),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                        side: const BorderSide(color: Colors.blue),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      icon: const Icon(Icons.edit, size: 16),
                      label: Text(
                        'تعديل',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),

                  // زر الحذف
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _deleteNotification(notification, ref),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      icon: const Icon(Icons.delete, size: 16),
                      label: Text(
                        'حذف',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduledTab() {
    return const Center(
      child: Text('الرسائل المجدولة - قيد التطوير'),
    );
  }

  Widget _buildStatsTab() {
    return const Center(
      child: Text('الإحصائيات - قيد التطوير'),
    );
  }

  Widget _buildTargetCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContentCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateAdvancedMessageScreen(),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScheduleCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: color,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _createMessage(String targetType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAdvancedMessageScreen(
          initialTargetType: targetType,
        ),
      ),
    );
  }

  void _scheduleMessage(String scheduleType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAdvancedMessageScreen(
          initialScheduleType: scheduleType,
        ),
      ),
    );
  }

  // Helper methods
  Color _getTypeColor(String type) {
    switch (type) {
      case 'emergency':
        return Colors.red;
      case 'error':
        return Colors.red[400]!;
      case 'warning':
        return Colors.orange;
      case 'success':
        return Colors.green;
      case 'info':
        return Colors.blue;
      case 'maintenance':
        return Colors.purple;
      case 'update':
        return Colors.cyan;
      case 'promotional':
        return Colors.deepOrange;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  // Action methods
  Future<void> _toggleNotificationStatus(
      AdminNotificationModel notification, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          notification.isActive ? 'تعطيل الرسالة' : 'تفعيل الرسالة',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
        content: Text(
          notification.isActive
              ? 'هل أنت متأكد من تعطيل هذه الرسالة؟ لن تظهر للمستخدمين بعد الآن.'
              : 'هل أنت متأكد من تفعيل هذه الرسالة؟ ستظهر للمستخدمين مرة أخرى.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  notification.isActive ? Colors.orange : Colors.green,
            ),
            child: Text(
              notification.isActive ? 'تعطيل' : 'تفعيل',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(adminNotificationsProvider.notifier)
          .updateNotificationStatus(notification.id, !notification.isActive);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                notification.isActive
                    ? 'تم تعطيل الرسالة بنجاح'
                    : 'تم تفعيل الرسالة بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل في تحديث حالة الرسالة',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _editNotification(AdminNotificationModel notification) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAdvancedMessageScreen(
          editingNotification: notification,
        ),
      ),
    );
  }

  Future<void> _deleteNotification(
      AdminNotificationModel notification, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف الرسالة',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من حذف هذه الرسالة نهائياً؟',
              style: GoogleFonts.cairo(),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.title,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.message,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.red[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '⚠️ هذا الإجراء لا يمكن التراجع عنه',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(
              'حذف نهائي',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(adminNotificationsProvider.notifier)
          .deleteNotification(notification.id);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف الرسالة بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'فشل في حذف الرسالة',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
