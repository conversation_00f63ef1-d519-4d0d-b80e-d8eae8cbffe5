import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// خدمة رفع الصور إلى Imgur
class ImgurService {
  // معرف العميل (Client ID) الخاص بـ Imgur API
  // يمكنك الحصول على معرف العميل من https://api.imgur.com/oauth2/addclient
  static const String clientId =
      ''; // تم تعطيل Client ID مؤقتاً - يحتاج تفعيل جديد

  /// التحقق من صحة التكوين
  static bool get isConfigured => clientId.isNotEmpty && clientId.length > 10;

  /// رفع صورة إلى Imgur
  /// يعيد رابط الصورة المباشر
  static Future<Map<String, dynamic>> uploadImage(Uint8List imageBytes) async {
    try {
      if (kDebugMode) {
        debugPrint('جاري رفع الصورة إلى Imgur...');
      }

      // التحقق من حجم الملف (الحد الأقصى 10MB)
      if (imageBytes.length > 10 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جداً (الحد الأقصى 10MB)');
      }

      // تحويل الصورة إلى base64
      final base64Image = base64Encode(imageBytes);

      // إعداد طلب الرفع
      final response = await http.post(
        Uri.parse('https://api.imgur.com/3/image'),
        headers: {
          'Authorization': 'Client-ID $clientId',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'image': base64Image,
          'type': 'base64',
          'name': 'upload.jpg',
          'title': 'Uploaded from AmrDevPOS Admin',
          'description': 'Image uploaded from AmrDevPOS Admin Panel',
        }),
      );

      if (kDebugMode) {
        debugPrint('استجابة Imgur: ${response.statusCode}');
        if (response.statusCode != 200) {
          debugPrint('محتوى الاستجابة: ${response.body}');
        }
      }

      // التحقق من نجاح الرفع
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true) {
          final data = responseData['data'];
          final directLink = data['link'];

          if (kDebugMode) {
            debugPrint('تم رفع الصورة بنجاح: $directLink');
          }

          return {
            'success': true,
            'directLink': directLink,
            'deleteHash': data['deletehash'],
            'fileName': 'image.jpg',
            'fileType': 'image',
            'fileInfo': data,
            'id': data['id'],
            'size': data['size'],
            'width': data['width'],
            'height': data['height'],
          };
        } else {
          final errorMessage =
              responseData['data']?['error'] ?? 'خطأ غير معروف';
          throw Exception('فشل في رفع الصورة: $errorMessage');
        }
      } else if (response.statusCode == 400) {
        // معالجة خاصة لخطأ 400
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final errorMessage = responseData['data']?['error'] ?? 'طلب غير صحيح';
        if (errorMessage.contains('forbidden')) {
          throw Exception('Client ID غير مفعل أو محدود - استخدم GoFile كبديل');
        } else {
          throw Exception('خطأ في البيانات المرسلة: $errorMessage');
        }
      } else if (response.statusCode == 403) {
        throw Exception('معرف العميل غير صحيح أو منتهي الصلاحية');
      } else if (response.statusCode == 429) {
        throw Exception(
            'تم تجاوز الحد المسموح من الطلبات، حاول مرة أخرى لاحقاً');
      } else {
        throw Exception(
            'فشل في رفع الصورة: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الصورة إلى Imgur: $e');
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// حذف صورة من Imgur
  /// يتطلب deleteHash الذي تم الحصول عليه عند رفع الصورة
  static Future<bool> deleteImage(String deleteHash) async {
    try {
      if (kDebugMode) {
        debugPrint('جاري حذف الصورة من Imgur...');
      }

      // التحقق من صحة deleteHash
      if (deleteHash.isEmpty) {
        throw Exception('معرف الحذف فارغ');
      }

      // إعداد طلب الحذف
      final response = await http.delete(
        Uri.parse('https://api.imgur.com/3/image/$deleteHash'),
        headers: {
          'Authorization': 'Client-ID $clientId',
        },
      );

      if (kDebugMode) {
        debugPrint('استجابة حذف Imgur: ${response.statusCode}');
        if (response.statusCode != 200) {
          debugPrint('محتوى الاستجابة: ${response.body}');
        }
      }

      // التحقق من نجاح الحذف
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true) {
          if (kDebugMode) {
            debugPrint('تم حذف الصورة بنجاح');
          }
          return true;
        } else {
          final errorMessage =
              responseData['data']?['error'] ?? 'خطأ غير معروف';
          throw Exception('فشل في حذف الصورة: $errorMessage');
        }
      } else if (response.statusCode == 404) {
        // الصورة غير موجودة (ربما تم حذفها مسبقاً)
        if (kDebugMode) {
          debugPrint('الصورة غير موجودة (ربما تم حذفها مسبقاً)');
        }
        return true;
      } else {
        throw Exception(
            'فشل في حذف الصورة: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في حذف الصورة من Imgur: $e');
      }
      return false;
    }
  }

  /// ضغط الصورة قبل الرفع (تحسين مقترح)
  static Uint8List? compressImage(Uint8List imageBytes, {int quality = 85}) {
    try {
      // هنا يمكن إضافة مكتبة ضغط الصور مثل image package
      // للآن سنعيد الصورة كما هي
      return imageBytes;
    } catch (e) {
      debugPrint('❌ خطأ في ضغط الصورة: $e');
      return imageBytes;
    }
  }

  /// التحقق من صحة الصورة
  static bool isValidImage(Uint8List imageBytes) {
    // التحقق من الحد الأقصى للحجم (10MB)
    if (imageBytes.length > 10 * 1024 * 1024) {
      return false;
    }

    // التحقق من أن الملف ليس فارغ
    if (imageBytes.isEmpty) {
      return false;
    }

    return true;
  }

  /// الحصول على معلومات الصورة
  static Future<Map<String, dynamic>?> getImageInfo(String imageId) async {
    try {
      final response = await http.get(
        Uri.parse('https://api.imgur.com/3/image/$imageId'),
        headers: {
          'Authorization': 'Client-ID $clientId',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['data'];
        }
      }

      return null;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على معلومات الصورة: $e');
      return null;
    }
  }
}

/// إحصائيات استخدام Imgur
class ImgurStats {
  static int _uploadCount = 0;
  static int _successCount = 0;
  static int _errorCount = 0;
  static double _totalSizeMB = 0.0;

  static void recordUpload(bool success, double sizeMB) {
    _uploadCount++;
    _totalSizeMB += sizeMB;

    if (success) {
      _successCount++;
    } else {
      _errorCount++;
    }
  }

  static Map<String, dynamic> getStats() {
    return {
      'totalUploads': _uploadCount,
      'successfulUploads': _successCount,
      'failedUploads': _errorCount,
      'successRate':
          _uploadCount > 0 ? (_successCount / _uploadCount * 100) : 0.0,
      'totalSizeMB': _totalSizeMB,
      'averageSizeMB': _uploadCount > 0 ? (_totalSizeMB / _uploadCount) : 0.0,
    };
  }

  static void reset() {
    _uploadCount = 0;
    _successCount = 0;
    _errorCount = 0;
    _totalSizeMB = 0.0;
  }
}
